"""
Router inclusion module for the FastAPI application.
This module centralizes all router inclusions to keep the main.py file clean.
"""

from fastapi import FastAPI
import nltk
from src.helper.logger import setup_new_logging
# Imported routers here to avoid circular imports
# from src.routes import router
from src.v2.dashboard import dashboard_router
from src.v2.dashboard.topic import topic_routers
from src.tenant import tenant_router
from src.v2 import v2_router


from src.routes import src_routers
from src.v2.KB.kb_setup.handle_documents import router as kb_setup
from src.v2.KB.qdrant import qd_router
from src.v2.dashboard.evaluate.routes import router as evaluate_router
from src.v2.dashboard.cta.routes import router as cta_router
from src.routes.activity_logs import router as activity_logs_router

from src.v2.widget.route import widget_router
from src.v2.external_hooks.whatsapp_webhook.email_sender.email import router as email_router
# Social media webhooks - consolidated
from src.v2.external_hooks.social_media_webhooks import social_media_router
# API documentation router
from src.v2.api_docs import router as api_docs_router
# Initialize logger

loggers = setup_new_logging(__name__)
def include_routers(app: FastAPI):
    """
    Include all routers in the FastAPI application.

    Args:
        app: The FastAPI application instance
    """


    loggers.info("Including routers in the application")


    app.include_router(tenant_router)

    app.include_router(src_routers)
    app.include_router(dashboard_router)
    app.include_router(topic_routers)
    app.include_router(v2_router)


    # Main router
    # app.include_router(router)

    # CTA and evaluation routers
    app.include_router(cta_router)
    app.include_router(evaluate_router)

    # Knowledge base and QDrant routers
    app.include_router(kb_setup)
    app.include_router(qd_router)


    # Activity logs router
    app.include_router(activity_logs_router)

    # Widget router
    app.include_router(widget_router,tags=["Widget"])

    app.include_router(email_router,tags=["Email Sender"])
    # Social media webhooks (Facebook, Instagram, WhatsApp)
    app.include_router(social_media_router)

    # API documentation router
    app.include_router(api_docs_router, tags=["API Documentation"])


    loggers.info("All routers included successfully")


# Initialize logger

# Asynchronous NLTK data download function
async def nltk_download():
    """
    Download only essential NLTK data packages.
    This improves startup time by avoiding unnecessary downloads.
    """
    import os
    from nltk.data import find, path

    # Get NLTK data path (used for logging purposes)
    nltk_data_path = path[0] if path else os.path.expanduser("~/nltk_data")
    loggers.info(f"NLTK data path: {nltk_data_path}")

    # Essential packages only - no more "all" download
    essential_packages = [
        'punkt',
        'punkt_tab',
        'wordnet',
        'omw-1.4',
        'stopwords',
        'averaged_perceptron_tagger'
    ]

    for package in essential_packages:
        try:
            # Try to find the package first
            if package in ['punkt', 'punkt_tab']:
                find(f'tokenizers/{package}')
            elif package == 'averaged_perceptron_tagger':
                find(f'taggers/{package}')
            else:
                find(f'corpora/{package}')
            loggers.info(f"NLTK {package} already exists, skipping download")
        except LookupError:
            loggers.info(f"Downloading NLTK {package}...")
            nltk.download(package, download_dir=nltk_data_path)
            loggers.info(f"NLTK {package} download complete")

    return True
