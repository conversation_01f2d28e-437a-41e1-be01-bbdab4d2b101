import os
import asyncio
import fitz
import pdfkit
from typing import Optional, List, Dict
from datetime import timed<PERSON><PERSON>
from docx import Document as Docx_bytes
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile
from src.reply.minio_client import MinIOClient
from io import BytesIO
from llama_index.core.schema import Document
from src.helper import logger
from datetime import datetime
import hashlib
import re
from src.models.user import UserTenantDB

loggers=logger.setup_new_logging(__name__)


async def convert_txt_to_pdf(text: str) -> bytes:
    """Convert a text string to a PDF (as bytes)."""
    html_content = f"<html><body><pre>{text}</pre></body></html>"
    return await asyncio.to_thread(pdfkit.from_string, html_content, False)


async def convert_docx_to_pdf(docx_content: bytes) -> bytes:
    """Convert a DOCX file (as bytes) to a PDF (as bytes)."""
    doc = Docx_bytes(BytesIO(docx_content))
    text = "\n".join([p.text for p in doc.paragraphs])
    return await convert_txt_to_pdf(text)

async def process_files(files: Optional[List[UploadFile]]) -> List[UploadFile]:
    if not files:
        return []
    loggers.info(f"Processing files...,{[file.filename for file in files]}")
    
    
    
    pdf_bytes = []

    for file in files:
        file_ext = os.path.splitext(file.filename)[1].lower()
        file_content = await file.read()
        
        
        if file_ext == ".pdf":
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(file_content), headers={"Content-Type": "application/pdf"}))
        elif file_ext == ".txt":
            pdf_byte = await convert_txt_to_pdf(file_content.decode('utf-8'))
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(pdf_byte), headers={"Content-Type": "application/pdf"}))
        elif file_ext == ".docx":
            pdf_byte = await convert_docx_to_pdf(file_content)
            pdf_bytes.append(UploadFile(filename=file.filename, file=BytesIO(pdf_byte), headers={"Content-Type": "application/pdf"}))
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_ext}")
    
    return pdf_bytes

from collections import deque

async def extract_text_and_images(Pdfs: List["UploadFile"], minio_client: MinIOClient) -> List[Dict]: # List["UploadFile"] wtf is this? just List[Uploadfile] works
    results = []
    
    for pdf in Pdfs:
        doc_bytes = await pdf.read()
        doc = fitz.open(stream=doc_bytes, filetype="pdf")
        pdf_name = pdf.filename
        minio_file_name = minio_client.upload_bytes(pdf_name, doc_bytes, "Files")
        
        pdf_data = []
        prev_text_blocks = deque()  # Store previous short text blocks
        prev_images = deque()  # Store images from pages with little/no text
        images=set()

        for page in doc:
            page_data = {
                "page_number": page.number + 1,
                "text_blocks": [],
                "images": []
            }

            # Extract text blocks
            text_blocks = []
            for block in page.get_text("blocks"):
                if block[6] == 0:
                    text = block[4].strip().rstrip("-")  # Remove trailing hyphens
                    text = " ".join(text.split())  # Normalize spaces
                    if text and len(text) < 100:  
                        prev_text_blocks.append(text)  # Store short text
                    else:
                        if prev_text_blocks:
                            text = " ".join(prev_text_blocks) + " " + text
                            prev_text_blocks.clear()  # Merge and reset
                        
                        text_blocks.append({
                            "text": text,
                            "bbox": list(fitz.Rect(block[:4]))
                        })
            
            # Extract images
            images = []
            for img_index, img in enumerate(page.get_images(full=True)):
                if img in images:
                    continue
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_path = f"page{page.number+1}_img{img_index}.{base_image['ext']}"
                
                minio_client.upload_bytes(
                    image_path,
                    base_image["image"],
                    f"Images/{pdf_name}"
                )
                
                img_data = {
                    "path": image_path,
                    "bbox": list(page.get_image_bbox(img))
                }
                images.append(img_data)

            # If no text or all text is too short, store images in prev_images
            if not text_blocks or sum(len(tb["text"]) for tb in text_blocks) < 50:
                prev_images.extend(images)
            else:
                # Merge previous short texts and images into this page
                if prev_text_blocks:
                    text_blocks.insert(0, {
                        "text": " ".join(prev_text_blocks),
                        "bbox": []
                    })
                    prev_text_blocks.clear()

                if prev_images:
                    images.extend(prev_images)
                    prev_images.clear()

            # Add final text and images to page data
            page_data["text_blocks"] = text_blocks
            page_data["images"] = images
            pdf_data.append(page_data)
        
        results.append({"document": minio_file_name, "pages": pdf_data})
    
    return results



def format_extracted_data(raw_data: List[Dict],current_user:UserTenantDB) -> List[Dict]:
    formatted = []
    created_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    for doc in raw_data:
        created_at = (datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S") + timedelta(seconds=1)).strftime("%Y-%m-%d %H:%M:%S")
        pdf_name = os.path.basename(doc["document"])
        for page in doc["pages"]:
            text=" ".join([b["text"] for b in page["text_blocks"]])
            if len(text)<50:
                continue
            formatted.append({
                "text": text,
                "metadata": {
                    "page_number": page["page_number"],
                    "images": [img["path"] for img in page["images"]],
                    "source": pdf_name,
                    "hash_id": hashlib.sha256(f"{pdf_name}_{page['page_number']}".encode()).hexdigest(),
                    "updated_by": current_user.user.id
                }
            })
    return formatted

def create_nodes(formatted_data):
    """Unecessary computation, mathi format_extracted_data mai last ma append garda Document banayera append gardeu
    Also you are looping through same list in multiple of these functions, yo sabai eutai loop ma garna milne khalko xa 
    """
    time = datetime.now()
    nodes=[]
    for i,doc in enumerate(formatted_data):
        created_at = (time + timedelta(seconds=i)).strftime("%Y-%m-%d %H:%M:%S")

        doc["metadata"]["created_at"] = created_at
        doc["metadata"]["updated_at"] = created_at
        nodes.append(Document(text=doc.get("text"), metadata=doc.get("metadata"))) 
    return nodes



async def handle_metadata(nodes: List[Document], minio,extra:Optional[list] = None):
    """Rename this to assign_urls_for_source_and_images and write a doc string"""
    results = []
  
    for node in nodes:
        # node=node.dict()
        node_m = node.metadata
        
        # remove every embedding field in the metadata as embeddings are too big for json output.
        new_node_m = {key: val for key, val in node_m.items() if "embedding" not in key}
        
        node_dict = node.dict()
        node_dict["metadata"] = new_node_m
        node_dict["text"] = node.text
        node = Document(**node_dict) # bad code that changes the value of list while looping through it.
        
        file = node.metadata.get("source")
        if file:
            presigned_url = await get_presigned_url_for_files([file], minio,"Files")
            if presigned_url:
                node.metadata["source_url"] = presigned_url[0]  # Assuming only one file

            # Handle images
        images = node.metadata.get("images", [])
        if images:
            presigned_urls = await get_presigned_url_for_files(images, minio,f"Images/{file}")
            node.metadata["images_url"] = presigned_urls

            # Add the node to the results list
        results.append(node)
    return results

async def get_presigned_url_for_files(files: List[str], minio: MinIOClient, folder: str) -> List[Dict[str, str]]:
    """
    Get presigned URLs for a list of files from MinIO storage.
    
    Args:
        files: List of file names/paths
        minio: MinIO client instance
        folder: Base folder path in MinIO
        
    Returns:
        List of dictionaries containing file names and their presigned URLs
    """
    async def get_url(file_str: str) -> Optional[Dict[str, str]]:
        try:
            if file_str.startswith(("http://", "https://")):
                return {"name": file_str, "url": file_str}
            presigned_url = minio.get_presigned_url(file_str, folder)
            return {"name": file_str, "url": presigned_url}
        except Exception as e:
            # loggers.error(f"\nError getting presigned URL for {file_str}: {e}")
            return None

    urls = await asyncio.gather(*[get_url(i) for i in files])
    return [url for url in urls if url is not None]



import pdfkit
import httpx
import re
from selectolax.parser import HTMLParser


async def process_urls(urls: Optional[str]) -> List[UploadFile]:
    """
    Optimized URL processing with improved parallel execution and error handling.
    """
    if not urls:
        return []

    if not isinstance(urls, list):
        urls = urls.split(",")

    # Clean and validate URLs
    urls = [url.strip() for url in urls if url.strip()]

    if not urls:
        return []

    loggers.info(f"Processing URLs: {len(urls)}")

    # Use semaphore to limit concurrent requests (prevents overwhelming servers)
    semaphore = asyncio.Semaphore(5)  # Max 5 concurrent requests

    async def process_with_semaphore(url):
        async with semaphore:
            return await process_single_url(url)

    # Create a list of tasks for parallel processing with rate limiting
    tasks = [process_with_semaphore(url) for url in urls]

    # Run tasks in parallel using asyncio.gather with exception handling
    pdfs = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out exceptions and None values, log failures
    successful_pdfs = []
    for i, result in enumerate(pdfs):
        if isinstance(result, Exception):
            loggers.error(f"Failed to process URL {urls[i]}: {result}")
        elif result is not None:
            successful_pdfs.append(result)
        else:
            loggers.warning(f"URL {urls[i]} returned None result")

    loggers.info(f"Successfully processed {len(successful_pdfs)}/{len(urls)} URLs")
    return successful_pdfs
async def generate_filename(url: str) -> str:
    """Generates a filename based on the last part of the URL."""
    if not url or not isinstance(url, str):
        raise ValueError("Invalid URL provided")

    # Extract the base name without extension
    pre_name = os.path.splitext(url.rstrip("/").split("/")[-1])[0]

    # Fallback: take the second-to-last segment if empty
    if not pre_name and "/" in url:
        parts = url.rstrip("/").split("/")
        pre_name = parts[-2] if len(parts) > 1 else ""

    # Final fallback: last 10 characters of the URL
    if not pre_name:
        pre_name = url[-10:].replace("/", "_")  # Replace slashes to avoid issues

    return f"{pre_name}_website.pdf"

async def process_single_url(url: str) -> Optional[UploadFile]:
    """
    Process a single URL and return an UploadFile object.
    Returns None if an error occurs.
    """
    try:
        pdf_byte = await clean_html(url)  # Now async function, no need for thread
        pre_name= await generate_filename(url)

        filename = pre_name + "_website.pdf"
        pdf_file = UploadFile(file=BytesIO(pdf_byte), filename=filename)
        return pdf_file
    except Exception as e:
        print(f"Error processing URL {url}: {e}")
        return None

async def clean_html(url):
    """
    Clean HTML content by removing non-essential elements while preserving main content,
    images, tables, and structural elements. Optimized with selectolax for faster parsing.
    """
    try:
        # Use httpx for async HTTP requests
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(url)
            html_content = response.text

        # Parse with selectolax (much faster than BeautifulSoup)
        tree = HTMLParser(html_content)

        # Remove header-related elements
        header_selectors = [
            'header', 'nav',
            '.header', '#header', '.navbar', '#navbar',
            '.top-bar', '#top-bar', '.main-nav', '.site-nav'
        ]
        for selector in header_selectors:
            for element in tree.css(selector):
                element.decompose()

        # Remove footer-related elements
        footer_selectors = [
            'footer',
            '.footer', '#footer', '.site-footer',
            '.copyright', '#copyright', '.bottom-bar'
        ]
        for selector in footer_selectors:
            for element in tree.css(selector):
                element.decompose()

        # Remove script and style elements
        for element in tree.css('script, style, link'):
            element.decompose()

        # Handle meta tags - preserve charset information
        for meta in tree.css('meta'):
            charset = meta.attributes.get('charset', '').lower()
            http_equiv = meta.attributes.get('http-equiv', '').lower()
            content = meta.attributes.get('content', '').lower()

            if charset in {'utf-8', 'utf8'}:
                continue
            elif http_equiv == 'content-type' and 'charset=' in content:
                continue
            else:
                meta.decompose()

        # Remove advertising and social media elements
        for selector in ['.ad', '#ad', '.social-widget', '.newsletter']:
            for element in tree.css(selector):
                element.decompose()

        # Clean inline attributes
        for tag in tree.css('*'):
            if tag.attributes:
                # Remove JavaScript event handlers and data attributes
                attrs_to_remove = []
                for attr in tag.attributes:
                    if attr.startswith('on') or attr.startswith('data-'):
                        attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    del tag.attributes[attr]

                # Remove class, id, and style attributes
                for attr in ['class', 'id', 'style']:
                    if attr in tag.attributes:
                        del tag.attributes[attr]

        # Remove HTML comments (selectolax handles this automatically)

        # Clean empty elements (preserve semantic elements)
        PRESERVE_TAGS = {'img', 'br', 'hr', 'td', 'th', 'iframe', 'svg'}
        for tag in tree.css('*'):
            if tag.tag in PRESERVE_TAGS:
                continue

            if not tag.text(strip=True) and not tag.css('img, br, hr, td, th, iframe, svg'):
                if tag.tag not in PRESERVE_TAGS:
                    tag.decompose()

        # Get cleaned HTML
        cleaned_html = tree.html
        cleaned_html = re.sub(r'\n{3,}', '\n\n', cleaned_html)  # Remove excessive newlines

        # Configure pdfkit with the path to wkhtmltopdf
        return makepdf(cleaned_html)

    except Exception as e:
        print(f"Error cleaning HTML from {url}: {e}")
        raise
def makepdf(html_content):
    """
    Generate a PDF file from a string of HTML using pdfkit.
    Configured for better readability and cross-platform compatibility.
    """
    # pdfkit options for better PDF quality and readability
    options = {
        'page-size': 'A4',
        'margin-top': '0.75in',
        'margin-right': '0.75in',
        'margin-bottom': '0.75in',
        'margin-left': '0.75in',
        'encoding': "UTF-8",
        'no-outline': None,
        'enable-local-file-access': None,
        'print-media-type': None,
        'disable-smart-shrinking': None,
        'minimum-font-size': '12',
        'dpi': 300,
        'image-quality': 94,
        'image-dpi': 300,
        'enable-javascript': None,
        'javascript-delay': 1000,
        'load-error-handling': 'ignore',
        'load-media-error-handling': 'ignore'
    }

    try:
        return pdfkit.from_string(html_content, False, options=options)
    except Exception as e:
        # Fallback with minimal options if the above fails
        print(f"PDF generation with full options failed: {e}, trying with minimal options...")
        minimal_options = {
            'page-size': 'A4',
            'encoding': "UTF-8",
            'enable-local-file-access': None
        }
        return pdfkit.from_string(html_content, False, options=minimal_options)