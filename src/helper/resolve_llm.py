from llama_index.llms.openai import OpenAI
from llama_index.llms.gemini import Gemini
import os
from src.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)

def resolve_llm(model_name: str, api_config: dict | None = None):
    logger.info(f"Resolving LLM model: {model_name}")
    """
    Resolve the LLM model based on the model name.
    """

    if not api_config:
        api_config = {}


    OPENAI_API_KEY = api_config.get("OPENAI_API_KEY", os.environ.get("OPENAI_API_KEY"))
    GOOGLE_API_KEY = api_config.get("GOOGLE_API_KEY", os.environ.get("GOOGLE_API_KEY"))

    if not model_name:
        return OpenAI(model="gpt-4o-mini", api_key=OPENAI_API_KEY)
        
    model_name = model_name.lower()
    
    if "gpt" in model_name:
        # print("OPENAI_API_KEY",OPENAI_API_KEY)
        
        return OpenAI(model=model_name, api_key=OPENAI_API_KEY)
    elif "gemini" in model_name:
        # print("GOOGLE_API_KEY",GOOGLE_API_KEY)
        return Gemini(model_name=model_name, api_key=GOOGLE_API_KEY)
    else:
        # Default to gpt-4o-mini if model name not recognized
        return OpenAI(model="gpt-4o-mini", api_key=OPENAI_API_KEY)
        