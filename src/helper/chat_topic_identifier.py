import asyncio
from typing import Any, Dict, List
from src.models.chat_hist import ModelRunRequest
from src.helper.resolve_llm import resolve_llm
from datetime import datetime
import re

def fetch_chat_data_with_message(db, start_date, end_date) -> list[dict]:
    pipeline = [
        {
            "$match": {
                "response.request_time": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.message": {"$ne": None}
            }
        },
        {
            "$group": {
                "_id": "$request.user_id",
                "messages": {
                    "$push": {
                        "message": "$request.message",
                    }
                }
            }
        },
        {
            "$project": {
                "_id": 0,
                "user_id": "$_id",
                "messages": 1
            }
        }
    ]

    return list(db.ai_response.aggregate(pipeline))










# 
# 
# 
# 
def extract_tags(topic_analysis: str) -> list[str]:
    raw_tags = re.findall(r"##(\w+(?:\s\w+)*)", topic_analysis)
    
    # Normalize and clean newlines/numbers like "risk\n2" → "risk"
    cleaned_tags = [re.split(r'\n|\d+$', tag.strip())[0].strip().lower() for tag in raw_tags]

    # Return unique tags
    return list(set(cleaned_tags))

async def generate_message_topic_async(usermessage: str, db) -> Dict[str, Any]:
    PROMPT_NAME = "find_chat_topic"
    prompt = db["prompt"].find_one({"name": PROMPT_NAME})
    try:
        llm = resolve_llm(prompt.get("model"))
        # First await the completion coroutine
        completion = await llm.acomplete(
            prompt.get("text").format(messages_str=usermessage),
            formatted=True
        )
        # Then access the text attribute
        topic_analysis = completion.text if hasattr(completion, 'text') else str(completion)
        
        return {
            "topic_analysis": topic_analysis,
            "tags": extract_tags(topic_analysis),
            "time": datetime.now()
        }
    except Exception as e:
        raise e

async def generate_topics_for_chat_messages(db, start_date, end_date) -> list[dict]:
    user_messages = fetch_chat_data_with_message(db, start_date, end_date)
    
    results = []
    
    for user_data in user_messages:
        user_id = user_data["user_id"]
        messages = user_data["messages"]
        
        try:
            # Get all valid request times
            request_times = [
                msg["request_time"] for msg in messages 
                if msg.get("request_time")
            ]
            
            # Generate topic for the entire conversation
            combined_text = "\n".join([msg["message"] for msg in messages])
            topic_info = await generate_message_topic_async(combined_text, db)
            
            results.append({
                "user_id": user_id,
                "message_count": len(messages),
                "tags": topic_info["tags"],
                "topic_analysis": topic_info["topic_analysis"],
                # "last_interaction": request_times[-1] if request_times else None,  # Use last time or None
                # "messages": messages  # Original messages
            })
            
        except Exception as e:
            results.append({
                "user_id": user_id,
                "error": str(e),
                "message_count": len(messages),
                "last_interaction": request_times[-1] if request_times else None
            })

    return results



async def process_message(db, user_id: str, message: str, request_time: datetime) -> Dict[str, Any]:
    try:
        topic_info = await generate_message_topic_async(message, db)
        return {
            "user_id": user_id,
            # "message": message,
            "request_time": request_time,
            "tags": topic_info["tags"],
            "topic_analysis": topic_info["topic_analysis"]
        }
    except Exception as e:
        return {
            "user_id": user_id,
            "message": message,
            "request_time": request_time,
            "error": str(e)
        }