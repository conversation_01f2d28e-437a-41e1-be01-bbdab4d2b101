# Create the pipeline with transformations
from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    Document
)
from typing import List, Dict

#load qdrant_coll
from llama_index.core import VectorStoreIndex
from llama_index.core import StorageContext
from llama_index.vector_stores.qdrant import QdrantVectorStore
from qdrant_client import QdrantClient

from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.ingestion import IngestionPipeline
pipeline = IngestionPipeline(
    transformations=[
        OpenAIEmbedding(),
    ]
)
from llama_index.core.prompts import PromptTemplate

from src.helper.resolve_llm import resolve_llm

from src.helper.TOCGenerator import TOCGenerator
from src.helper.QuestionGenerator import QuestionGenerator , NodesWithRelation
from src.helper import logger

loggers = logger.setup_new_logging(__name__)
def create_nodes(formatted_data: List[List[Dict]]) -> List[Document]:
    
    # Create nodes from formatted data
    nodes = []
    for doc in formatted_data:
        for page in doc:
            # node = Document(text=page.get("text"), metadata=page.get("metadata"))
            # nodes.append(node)
            metadata = page.get("metadata", {})
            node = Document(
                text=page.get("text"),
                metadata=metadata  
            )
            nodes.append(node)
    
    print("\n\nnodes",nodes[0].to_json())
    return nodes

def generate_tableOfContent(documents: List[Document], api_config):

    toc, updated_docs = TOCGenerator(documents=documents, llm=resolve_llm("models/gemini-1.5-pro", api_config), 
    ).generate_toc()

    return toc, updated_docs

#for Resolving the QnA from the documents
def generateQAFromDocuments(documents: List[Document], api_config, answer_gen_coll:str, qdrant_config, qdrant, minio) -> List[NodesWithRelation]:

    data_generator = get_data_generator(documents, api_config, answer_gen_coll, qdrant_config, qdrant, minio)
    loggers.info("Generating questions...")
    questions = data_generator.generate_questions_from_nodes()
    loggers.info("Generating answers...")
    answers = data_generator.generate_answers_from_questions(questions)
    loggers.info("Generated answers...")
    return answers


# For generating the QnA from the section documents

def get_data_generator(section_documents: List[Document],  api_config,answer_gen_coll:str,qdrant_config,qdrant,minio,num_questions_per_chunk=10):
    llm = resolve_llm("models/gemini-1.5-pro", api_config)

    answer_gen_query_engine = VectorStoreIndex.from_vector_store(
        QdrantVectorStore(client=qdrant, collection_name=answer_gen_coll),
        embed_model=OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536)
    ).as_query_engine(similarity_top_k=2, use_async=True)
    data_generator = QuestionGenerator(
        nodes=section_documents,
        llm=llm,
        num_questions_per_chunk=num_questions_per_chunk,
        question_gen_query=f"You are a Product Service Representative. Your task is to setup \
                            5 questions that a customer can ask about the services. The questions should be diverse in nature \
                                across the document. Restrict the questions to the \
                                    context information provided.",
        text_question_template=PromptTemplate(
            """\
    Context information is below.
    ---------------------
    {context_str}
    ---------------------
    Given the context information and not prior knowledge.
    generate only questions based on the below query with no preamble or explanation.
    {query_str}
    """
        ),
        answer_gen_query_engine=answer_gen_query_engine,
        qdrant_config=qdrant_config,
        qdrant=qdrant,
        minio=minio

        
    )
    return data_generator

def load_nodes_from_qdrant_coll(coll_name, limit=100):
 
    client = QdrantClient(host="*************", port=6333)
    vector_store = QdrantVectorStore(client=client, collection_name=coll_name)
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    # index = VectorStoreIndex.from_vector_store(vector_store, embed_model=OpenAIEmbedding(model="text-embedding-large", dimensions=1536))

    nodes = vector_store.get_nodes(limit=1)
    return nodes