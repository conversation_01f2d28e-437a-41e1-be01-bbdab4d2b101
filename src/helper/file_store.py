import requests
from src.helper import get_DB, logger
from dotenv import load_dotenv
import os
loggers = logger.setup_new_logging(__name__)
load_dotenv()
API_URL=os.getenv("API_URL")


async def upload_file(file,metadata):
    headers = {
    'accept': 'application/json',
    'Content-Type': 'application/x-www-form-urlencoded',
        }

    data = {
        'grant_type': 'password',
        'username': 'string',
        'password': 'string',
        'scope': '',
        'client_id': 'string',
        'client_secret': 'string',
    }

    response = requests.post(f'{API_URL}:8402/login', headers=headers, data=data)
    response_data = response.json()
    headers = {
        'accept': 'application/json',
        'Authorization': f'Bearer {response_data.get("access_token")}',
    }
    # Use the file object directly instead of opening it
    # files = {'file': file}
    try:
        # Send the file to the external storage service
        response = requests.post(f'{API_URL}:8402/store_files/', headers=headers, files=file)
        if response.status_code == 200:
            response_data = response.json()
            # Save metadata in the database
            file_metadata = {
                "fms_id": response_data.get("fms_id"),
                "access_url": response_data.get("access_url"),
                "stored_path": response_data.get("stored_path"),
                "original_filename": response_data.get("original_filename"),
                "ttl_seconds": response_data.get("ttl_seconds"),
                "meta_data": metadata if metadata else None,
            }
        
            insert=get_DB("echo").uploaded_files.insert_one(file_metadata)
            loggers.info("File metadata saved to the database successfully.")
            return {"access_url": response_data.get("access_url"),"status":"success","file_id":insert.inserted_id}
        else:
            loggers.error(f"File upload failed with response: {response.json()}")
            raise Exception("Failed to upload file")
    except Exception as e:
        loggers.exception(f"Error during file upload: {e}")
        raise