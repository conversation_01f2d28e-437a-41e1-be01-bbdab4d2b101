
import os
import json
from bson import ObjectId
from pymongo import MongoClient
from urllib.parse import urlparse, urlunparse
from dotenv import load_dotenv

load_dotenv()


def get_DB(ENV, MONGO_URI=None):
    MONGO_URI = os.getenv("MONGO_URI_Live") if MONGO_URI is None else MONGO_URI
    client = MongoClient(MONGO_URI)
    if ENV == "DEV":
        return client["AG_Classify_Bot"]
    elif ENV == "PROD":
        return client["AG_Classify_Bot_PROD"]
    return client[ENV]

# def convert_objectid_to_str(document):
#     if isinstance(document, dict):
#         for key, value in document.items():
#             if isinstance(value, ObjectId):
#                 document[key] = str(value)
#             elif isinstance(value, dict):
#                 convert_objectid_to_str(value)
#             elif isinstance(value, list):
#                 for item in value:
#                     convert_objectid_to_str(item)
#     return document

def convert_objectid_to_str(document):
    if isinstance(document, dict):
        for key, value in document.items():
            if isinstance(value, ObjectId):
                document[key] = str(value)
            elif isinstance(value, dict):
                convert_objectid_to_str(value)
            elif isinstance(value, list):
                for index, item in enumerate(value):
                    if isinstance(item, ObjectId):
                        value[index] = str(item)
                    else:
                        convert_objectid_to_str(item)
    elif isinstance(document, list):
        for index, item in enumerate(document):
            if isinstance(item, ObjectId):
                document[index] = str(item)
            else:
                convert_objectid_to_str(item)

    return document



def get_base_url(full_url):
    # parsed_url = urlparse(full_url)
    # parsed_url = urlunparse(parsed_url._replace(query=""))
    # return parsed_url
    return full_url


from urllib.parse import urlparse, urlunparse

def change_port(url, new_port):
    parsed_url = urlparse(url)
    if parsed_url.port == 8410:
        updated_netloc = parsed_url.netloc.replace(f":{parsed_url.port}", f":{new_port}")
        updated_url = urlunparse(parsed_url._replace(netloc=updated_netloc))
        if parsed_url.scheme == "http":
            updated_url = updated_url.replace("http://", "https://")
        
        return updated_url
    else:
        return url