from typing import Dict, Any, List, Union, Optional

def get_filter_access(current_user, route,permissions) -> Dict:
    match_conditions = {}
    
    # Get access filters for the specified route
    filter_access = current_user.access.get(route, {}) if hasattr(current_user, 'access') and current_user.access else {}
    
    # Process each category in the filter access
    for category, values in filter_access.items():
        if isinstance(values, dict):
            denied_items = [k for k, v in values.items() if v is permissions]
            if denied_items:
                match_conditions[category] = denied_items
    
    return match_conditions
