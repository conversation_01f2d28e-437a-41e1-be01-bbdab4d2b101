"""Question Generation from documents."""

from __future__ import annotations

import asyncio
import json
import re
import uuid
from typing import Coroutine, Dict, List, Optional, Tuple

from deprecated import deprecated
from llama_index.core.async_utils import asyncio_run
from llama_index.core.bridge.pydantic import BaseModel, Field
from llama_index.core.callbacks.base import CallbackManager
from llama_index.core.indices.list import SummaryIndex
from llama_index.core.ingestion import run_transformations
from llama_index.core.llms.llm import LLM
from llama_index.core.query_engine import BaseQueryEngine
from llama_index.core.postprocessor.node import KeywordNodePostprocessor
from llama_index.core.prompts.base import BasePromptTemplate, PromptTemplate
from llama_index.core.prompts.default_prompts import DEFAULT_TEXT_QA_PROMPT
from llama_index.core.prompts.mixin import (
    PromptDictType,
    PromptMixin,
    PromptMixinType,
)
from llama_index.core.schema import (
    BaseNode,
    Document,
    MetadataMode,
    NodeWithScore,
    TransformComponent,
)
from llama_index.core.settings import (
    Settings,
)
from datetime import datetime
from src.helper.qdrant import extract_page_sent
from src.helper.logger import setup_new_logging
from src.models.resolve import QnA as NodesWithRelation
from src.models.resolve import Answer,Question

loggers=setup_new_logging(__name__)

DEFAULT_QUESTION_GENERATION_PROMPT = """\
Context information is below.
---------------------
{context_str}
---------------------
Given the context information and not prior knowledge.
generate only questions based on the below query.
{query_str}
"""

NODE_RELATION_IDENTIFY_PROMPT = """
Context information is below.
---------------------
Question: ```{question}```
---------------------
Answer Sources: ```{answer_sources_str}```

Given the answer sources for the question, Determine whether these sources are either [duplicate,conflicting,no_relation]
1. duplicate: The sources contain identical or nearly identical information, with overlapping details and no significant differences.
2. conflicting: The sources provide contradictory or inconsistent information about the same topic or question.
3. no_relation: The sources discuss entirely different topics or provide unrelated information that does not address the same question.


In case of conflict list the reason of conflict 
Output Format:
```
relation:
conflict_reason:
```
"""



@deprecated(
    "Deprecated in favor of `LabelledRagDataset` which should be used instead.",
    action="always",
)
class QueryResponseDataset(BaseModel):
    """Query Response Dataset.

    The response can be empty if the dataset is generated from documents.

    Args:
        queries (Dict[str, str]): Query id -> query.
        responses (Dict[str, str]): Query id -> response.

    """

    queries: Dict[str, Document] = Field( description="Query id -> query"
)
    # responses: Dict[str, str] = Field(
    #     default_factory=dict, description="Query id -> response"
    # )

    @classmethod
    def from_qr_pairs(
        cls,
        qr_pairs: List[Tuple[str, str]],
    ) -> QueryResponseDataset:
        """Create from qr pairs."""
        # define ids as simple integers
        queries = {str(idx): query for idx, (query, _) in enumerate(qr_pairs)}
        responses = {str(idx): response for idx, (_, response) in enumerate(qr_pairs)}
        return cls(queries=queries, responses=responses)

    @property
    def qr_pairs(self) -> List[Tuple[str, str]]:
        """Get pairs."""
        # if query_id not in response, throw error
        for query_id in self.queries:
            if query_id not in self.responses:
                raise ValueError(f"Query id {query_id} not in responses")

        return [
            (self.queries[query_id], self.responses[query_id])
            for query_id in self.queries
        ]

    @property
    def questions(self) -> List[Dict]:
        """Get questions."""
        return list(self.queries.values())

    def save_json(self, path: str) -> None:
        """Save json."""
        with open(path, "w") as f:
            json.dump(self.model_dump(), f, indent=4)

    @classmethod
    def from_json(cls, path: str) -> QueryResponseDataset:
        """Load json."""
        with open(path) as f:
            data = json.load(f)
        return cls(**data)



# class NodesWithRelation(BaseModel):
#     nodes: List[NodeWithScore]
#     question: Document
#     answer: str
#     relation:str
#     conflict:str

    # @property
    # def reason_of_conflict(self):
    #     return self.relation.split("\n")[1].split(":")[1].strip()

    # @property
    # def relation_word(self):
    #     return self.relation.split("\n")[0].split(":")[1].strip()
    


@deprecated(
    "Deprecated in favor of `RagDatasetGenerator` which should be used instead.",
    action="always",
)
class QuestionGenerator(PromptMixin):
    """Generate dataset (question/ question-answer pairs) \
    based on the given documents.

    NOTE: this is a beta feature, subject to change!

    Args:
        nodes (List[Node]): List of nodes. (Optional)
        llm (LLM): Language model.
        callback_manager (CallbackManager): Callback manager.
        num_questions_per_chunk: number of question to be \
        generated per chunk. Each document is chunked of size 512 words.
        text_question_template: Question generation template.
        question_gen_query: Question generation query.

    """

    def __init__(
        self,
        nodes: List[BaseNode],
        llm: Optional[LLM] = None,
        callback_manager: Optional[CallbackManager] = None,
        num_questions_per_chunk: int = 3,
        text_question_template: BasePromptTemplate | None = None,
        text_qa_template: BasePromptTemplate | None = None,
        question_gen_query: str | None = None,
        metadata_mode: MetadataMode = MetadataMode.NONE,
        answer_gen_query_engine: BaseQueryEngine | None = None,
        qdrant_config=None,
        qdrant=None,
        minio=None,
        show_progress: bool = False,
    ) -> None:
        """Init params."""
        self.llm = llm or Settings.llm
        self.callback_manager = callback_manager or Settings.callback_manager
        self.text_question_template = text_question_template or PromptTemplate(
            DEFAULT_QUESTION_GENERATION_PROMPT
        )
        self.text_qa_template = text_qa_template or DEFAULT_TEXT_QA_PROMPT
        self.question_gen_query = (
            question_gen_query
            or f"You are a Teacher/Professor. Your task is to setup \
                        {num_questions_per_chunk} questions for an upcoming \
                        quiz/examination. The questions should be diverse in nature \
                            across the document. Restrict the questions to the \
                                context information provided."
        )
        self.nodes = nodes
        self._metadata_mode = metadata_mode
        self._show_progress = show_progress
        self._answer_gen_query_engine = answer_gen_query_engine
        self.qdrant_config=qdrant_config
        self.qdrant=qdrant
        self.minio=minio

    @classmethod
    def from_documents(
        cls,
        documents: List[Document],
        llm: Optional[LLM] = None,
        transformations: Optional[List[TransformComponent]] = None,
        callback_manager: Optional[CallbackManager] = None,
        num_questions_per_chunk: int = 10,
        text_question_template: BasePromptTemplate | None = None,
        text_qa_template: BasePromptTemplate | None = None,
        question_gen_query: str | None = None,
        required_keywords: List[str] | None = None,
        exclude_keywords: List[str] | None = None,
        show_progress: bool = False,
    ) -> QuestionGenerator:
        """Generate dataset from documents."""
        llm = llm or Settings.llm
        transformations = transformations or Settings.transformations
        callback_manager = callback_manager or Settings.callback_manager

        nodes = run_transformations(
            documents, transformations, show_progress=show_progress
        )

        # use node postprocessor to filter nodes
        required_keywords = required_keywords or []
        exclude_keywords = exclude_keywords or []
        node_postprocessor = KeywordNodePostprocessor(
            callback_manager=callback_manager,
            required_keywords=required_keywords,
            exclude_keywords=exclude_keywords,
        )
        node_with_scores = [NodeWithScore(node=node) for node in nodes]
        node_with_scores = node_postprocessor.postprocess_nodes(node_with_scores)
        nodes = [node_with_score.node for node_with_score in node_with_scores]

        return cls(
            nodes=nodes,
            llm=llm,
            callback_manager=callback_manager,
            num_questions_per_chunk=num_questions_per_chunk,
            text_question_template=text_question_template,
            text_qa_template=text_qa_template,
            question_gen_query=question_gen_query,
            show_progress=show_progress,
        )

    async def _agenerate_dataset(
        self,
        nodes: List[BaseNode],
        num: int | None = None,
        generate_response: bool = False,
    ) -> QueryResponseDataset:
        """Node question generator."""
        query_tasks: List[Coroutine] = []
        queries: Dict[str, str] = {}
        responses_dict: Dict[str, str] = {}

        if self._show_progress:
            from tqdm.asyncio import tqdm_asyncio

            async_module = tqdm_asyncio
        else:
            async_module = asyncio

        node_metadatas = []
        summary_indices: List[SummaryIndex] = []
        for node in nodes:
            if num is not None and len(query_tasks) >= num:
                break
            index = SummaryIndex.from_documents(
                [
                    Document(
                        text=node.get_content(metadata_mode=self._metadata_mode),
                        metadata=node.metadata,  # type: ignore
                    )
                ],
                callback_manager=self.callback_manager,
            )

            query_engine = index.as_query_engine(
                llm=self.llm,
                text_qa_template=self.text_question_template,
                use_async=True,
            )
            task = query_engine.aquery(
                self.question_gen_query,
            )
            query_tasks.append(task)
            summary_indices.append(index)
            node_metadatas.append(node.metadata)

        responses = await async_module.gather(*query_tasks)

        for idx, response in enumerate(responses):
            result = str(response).strip().split("\n")
            cleaned_questions = [
                re.sub(r"^\d+[\).\s]", "", question).strip() for question in result
            ]
            cleaned_questions = [
                question for question in cleaned_questions if len(question) > 0
            ]
            
            cur_queries = {
                str(uuid.uuid4()): Document(**{"text":question, "metadata": node_metadatas[idx]}) for question in cleaned_questions
            }
            queries.update(cur_queries)

        queries = await self.remove_duplicate_sentences(queries, threshold=5)


        # print(queries)
        return QueryResponseDataset(queries=queries)



    async def remove_duplicate_sentences(self, queries: Dict[str, Document], threshold: int = 5) -> Dict[str, Document]:
        """
        Removes duplicate sentences from the queries based on word overlap.
        :param queries: Dictionary of query IDs to Document objects.
        :param threshold: Minimum number of common words to consider two sentences duplicates.
        :return: Filtered dictionary of unique queries.
        """
        unique_queries = {}
        seen_sentences = []
        
        duplicate_counter=0

        for q_id, document in queries.items():
            sentence = document.text.lower()
            words = set(sentence.split())
            is_duplicate = False

            for seen_sentence in seen_sentences:
                seen_words = set(seen_sentence.split())
                common_words = words & seen_words  # Intersection

                if len(common_words) > threshold:
                    is_duplicate = True
                    duplicate_counter+=1
                    break

            if not is_duplicate:
                seen_sentences.append(sentence)
                unique_queries[q_id] = document
        loggers.info(f"Duplicate sentences: {duplicate_counter}")
        return unique_queries



    async def agenerate_questions_from_nodes(self, num: int | None = None) -> List[str]:
        """Generates questions for each document."""
        dataset = await self._agenerate_dataset(
            self.nodes, num=num, generate_response=False
        )
        return dataset.questions

    async def agenerate_dataset_from_nodes(
        self, num: int | None = None
    ) -> QueryResponseDataset:
        """Generates questions for each document."""
        return await self._agenerate_dataset(
            self.nodes, num=num, generate_response=True
        )

    def generate_questions_from_nodes(self, num: int | None = None):
        """Generates questions for each document."""
        return asyncio_run(self.agenerate_questions_from_nodes(num=num))


    async def aidentify_node_relation(self, nodes:List[NodeWithScore], question:Document, answer:str) -> NodesWithRelation:
        """Identify the relation between nodes using LLM as 
        Similar, Duplicate, Conflicting_Info, NoRelation
        """
        answer_sources_str = ""
        for node in nodes:
            node_str = f"""<source>{node.get_content()}</source>\n"""
            answer_sources_str += node_str 

        resp_ = await self.llm.acomplete(prompt=NODE_RELATION_IDENTIFY_PROMPT.format(
            question=question.get_content(), answer=answer, answer_sources_str=answer_sources_str
        ))


        relation_ = resp_.text.strip()


        
        relationship = await self.split_relation(relation_)

        nodes=await extract_page_sent(nodes,qdrant_config=self.qdrant_config,qdrant=self.qdrant,minio=self.minio)
        answer=Answer(answer=answer, source_nodes=nodes, relation=relationship[0], conflict_reason=relationship[1])
        question=Question(question=question.get_content(), question_id=question.get_doc_id(), metadata=question.metadata)
        return NodesWithRelation(question=question, answer=answer,created_at=datetime.utcnow(),resolved=False,resolved_at=None,resolved_by=None)
        # try:
        #     if relationship in ["duplicate","conflicting"]:
        #         return None
        # except Exception as e:
        #     print(e)
        #     return None
    async def agenerate_answers_from_questions(self, questions: List[Document]) -> List[NodesWithRelation]:

        tasks = []
        for question in questions:
            tasks.append(self._answer_gen_query_engine.query(question.get_content()))
        # responses = await asyncio.gather(*tasks)
        responses = tasks
        self._answer_gen_query_engine.aquery
        node_relation_tasks = []
        for idx, response in enumerate(responses):
            answer = response.response
            answer_nodes = response.source_nodes
            node_relation_tasks.append(self.aidentify_node_relation(nodes=answer_nodes, question=questions[idx], answer=answer))
        nodes_with_relation = await asyncio.gather(*node_relation_tasks)
        
        nodes_with_relation = [node for node in nodes_with_relation if node is not None]
        return nodes_with_relation
    
    def generate_answers_from_questions(self, questions: List[Document]) -> List[NodesWithRelation]:
        """Generates answers from questions"""
        return asyncio_run(self.agenerate_answers_from_questions(questions))

    def generate_dataset_from_nodes(
        self, num: int | None = None
    ) -> QueryResponseDataset:
        """Generates questions for each document."""
        return asyncio_run(self.agenerate_dataset_from_nodes(num=num))

    def _get_prompts(self) -> PromptDictType:
        """Get prompts."""
        return {
            "text_question_template": self.text_question_template,
            "text_qa_template": self.text_qa_template,
        }

    def _get_prompt_modules(self) -> PromptMixinType:
        """Get prompt modules."""
        return {}

    def _update_prompts(self, prompts: PromptDictType) -> None:
        """Update prompts."""
        if "text_question_template" in prompts:
            self.text_question_template = prompts["text_question_template"]
        if "text_qa_template" in prompts:
            self.text_qa_template = prompts["text_qa_template"]
            
            
    async def split_relation(self,data):
        import re
        pattern = r"relation:\s*(.*?)\s*conflict_reason:\s*(.*)"
        match = re.search(pattern, data, re.DOTALL)
        if match:
            relation = match.group(1)
            conflict = match.group(2)
            conflict = conflict.replace("`", "").strip()
            return relation, conflict
        return None, None