from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Optional
import os
from passlib.context import CryptContext
from pymongo import MongoClient
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB

router = APIRouter(prefix="/tenants", tags=["Tenants"])

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class TenantCreate(BaseModel):
    name: str
    slug: str
    username: str
    password: str
    label: Optional[str] = None

# @router.post("/crate")
async def create_tenant(
    tenant: TenantCreate,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    if not tenant.label:
        tenant.label = tenant.name.replace(" ", "_").lower()
    
    database_name = f"{tenant.name}_db"
    
    mongo_uri = os.getenv("MONGO_URI")
    client = MongoClient(mongo_uri)
    
    admin_db = client["eko_admin"]
    existing_tenant = admin_db.tenants.find_one({"$or": [
        {"name": tenant.name},
        {"slug": tenant.slug},
        {"database_name": database_name}
    ]})
    
    if existing_tenant:
        raise HTTPException(
            status_code=400,
            detail="Tenant with this name or slug already exists"
        )
    
    try:
        tenant_db = client[database_name]
        
        tenant_db.create_collection("settings")
        tenant_db.create_collection("users")
        
        admin_db.tenants.insert_one({
            "name": tenant.name,
            "slug": tenant.slug,
            "database_name": database_name,
            "label": tenant.label
        })
        
        hashed_password = pwd_context.hash(tenant.password)
        tenant_db.users.insert_one({
            "username": tenant.username,
            "hashed_password": hashed_password,
            "role": "admin"
        })
        
        tenant_db.settings.insert_one({
            "admin": {
                "Dashboard": True,
                "Channels": True,
                "Manage Agents": True,
                "User Registration": False,
                "Setup": False,
                "Playground": True,
                "EditDoc": False,
                "DropzoneComponent": False,
                "KnowledgeBase": True,
                "Feedback Log": True,
                "CTA": True
            },
            "agent": {
                "Dashboard": True,
                "Channels": True,
                "Manage Agents": False,
                "User Registration": False,
                "Setup": False,
                "Playground": True,
                "EditDoc": False,
                "DropzoneComponent": False,
                "KnowledgeBase": True,
                "Feedback Log": True,
                "CTA": True
            },
            "supervisor": {
                "Dashboard": False,
                "Channels": True,
                "Manage Agents": False,
                "User Registration": False,
                "Setup": False,
                "Playground": True,
                "EditDoc": False,
                "DropzoneComponent": False,
                "KnowledgeBase": True,
                "Feedback Log": True,
                "CTA": False
            },
            "name": "nav_permission"
        })
        
        tenant_db.settings.insert_one({
            "name": "env",
            "config": {
                "AROMA_BACKEND_URL": os.getenv("AROMA_BACKEND_URL"),
                "AROMA_TENANT_SLUG": os.getenv("AROMA_TENANT_SLUG"),
                "AROMA_TENANT_USERNAME": os.getenv("AROMA_TENANT_USERNAME"),
                "AROMA_TENANT_PASSWORD": os.getenv("AROMA_TENANT_PASSWORD"),
                "gemini_api_key": os.getenv("GEMINI_API_KEY"),
                "GOOGLE_API_KEY": os.getenv("GEMINI_API_KEY"),
                "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY"),
            },
            "identify_product": False,
            "contextualize": False,
            "qdrant_config": {
                "coll_name": f"{tenant.name}_sentence_context",
                "host": os.getenv("qdrant_host"),
                "port": "6333",
                "sentence_collection": f"{tenant.name}_sentence_context",
                "page_collection": f"{tenant.name}_page_info",
                "sentence_split_collection": f"{tenant.name}_sentence_split",
                "topk": 3
            },
            "minio_config": {
                "access_key": os.getenv("minio_access_key"),
                "secret_key": os.getenv("minio_secret_key"),
                "bucket_name": f"eko.{tenant.name}",
                "minio_url": os.getenv("minio_url_live"),
                "secure": True
            },
        })
        
        return {
            "status": "success",
            "message": f"Tenant '{tenant.name}' created successfully",
            "tenant_id": tenant.name,
            "database_name": database_name
        }
        
    except Exception as e:
        try:
            admin_db.tenants.delete_one({"name": tenant.name})
            client.drop_database(database_name)
        except:
            pass
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create tenant: {str(e)}"
        )
