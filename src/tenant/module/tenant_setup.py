from datetime import datetime
from typing import Optional
from pydantic import BaseModel
from fastapi import HTTPException
from src.core.database import get_admin_db
class ProductDetail(BaseModel):
    name:str
    detail:str

class BusinessInfo(BaseModel):
    org_name:str
    org_type:str
    org_description:Optional[str]=None
    org_goal:Optional[str]=None
    org_contact:Optional[str]=None
    agent_name:Optional[str]="AI Bot"
    agent_role:str
    language:Optional[list[str]]=["English"]
    set_up_complete:Optional[bool]=False
    # product:Optional[list[ProductDetail]]=None
    # questions:Optional[list[str]]=None
    # required_cta_type:list[ProductDetail]=None

    def format_dummy_prompt(self, current_user, response_mode_str: str="Provide a detailed and explained answer in a couple of sentences."):
        

        simplified_prompt=get_admin_db().prompt.find_one({"name": "reply_prompt_openai_simplified_setup"})
        simplified_prompt_text = simplified_prompt["text"].format(
            org_name=self.org_name,
            business_type=", ".join(self.org_type),
            agent_name=self.agent_name,
            agent_role=self.agent_role,
            org_description=self.org_description,
            org_goal=self.org_goal,
            
            # product_section=product_section,
            response_mode_str=response_mode_str

            # required_cta_type=", ".join([p.name for p in self.required_cta_type])
        )
        current_user.db.prompt.update_one({"name": "reply_prompt_openai_simplified"}, {"$set": {"text": simplified_prompt_text}}, upsert=True)

        reply_prompt_openai_setup = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_setup"})
        reply_prompt_openai_setup_text = reply_prompt_openai_setup["text"].format(
            org_name=self.org_name,
            business_type=", ".join(self.org_type),
            agent_name=self.agent_name,
            agent_role=self.agent_role,

            org_description=self.org_description,
            org_goal=self.org_goal,
            # product_section=product_section,
            response_mode_str=response_mode_str
            # required_cta_type=", ".join([p.name for p in self.required_cta_type])
        )
        current_user.db.prompt.update_one({"name": "reply_prompt_openai"}, {"$set": {"text": simplified_prompt_text}}, upsert=True)
        
        reply_prompt_openai_elaborated_setup = get_admin_db().prompt.find_one({"name": "reply_prompt_openai_elaborated_setup"})
        reply_prompt_openai_elaborated_setup = reply_prompt_openai_elaborated_setup["text"].format(
            org_name=self.org_name,
            business_type=", ".join(self.org_type),
            agent_name=self.agent_name,
            agent_role=self.agent_role,

            org_description=self.org_description,
            org_goal=self.org_goal,
            # product_section=product_section,
            response_mode_str=response_mode_str
            # required_cta_type=", ".join([p.name for p in self.required_cta_type])
        )
        current_user.db.prompt.update_one({"name": "reply_prompt_openai_elaborated"}, {"$set": {"text": reply_prompt_openai_elaborated_setup}}, upsert=True)
        

        reply_prompt_openai_simplified = simplified_prompt_text
        reply_prompt_openai = reply_prompt_openai_setup_text
        reply_prompt_openai_elaborated = reply_prompt_openai_elaborated_setup


   

        return {"simplified":reply_prompt_openai_simplified,
                 "detailed":reply_prompt_openai,
                 "elaborated":reply_prompt_openai_elaborated}
        
    @classmethod
    def update_prompt(cls, prompt, tools,
                       current_user, 
                    #    business_info: 'BusinessInfo'
                       ):
   
        # generated_prompts = business_info.format_dummy_prompt(current_user)
   
        # # Map prompt name to DB document name
        # if prompt=="simplified":
        #     name = "reply_prompt_openai_simplified"
        # elif prompt=="elaborated":
        #     name = "reply_prompt_openai_elaborated"
        # elif prompt=="detailed":
        #     name = "reply_prompt_openai"
        # else:
        #     raise HTTPException(status_code=400, detail=f"Invalid prompt name {prompt}")

        if tools:
            for tool_name in tools:
                tool_definition = current_user.db.tools.find_one({"name": "create_issue_tickets"})
                if tool_definition:
                    current_enum = tool_definition["tools"][0]["function"]["parameters"]["properties"]["issue_type"]["enum"]
                    
                    # Check if the tool_name is already in enum (as a string or inside an array)
                    is_present = any(
                        (isinstance(item, str) and item == tool_name) or
                        (isinstance(item, list) and item[0] == tool_name)
                        for item in current_enum
                    )
                    
                    # If not present, add it as a plain string (like "ticket" or "booking")
                    if not is_present:
                        current_enum.append(tool_name)
                        
                        # Update the document
                        current_user.db.tools.update_one(
                            {"name": "create_issue_tickets"},
                            {
                                "$set": {
                                    "updated_at": datetime.now(),
                                    "tools.$[tool].function.parameters.properties.issue_type.enum": current_enum
                                }
                            },
                            array_filters=[{"tool.type": "function", "tool.function.name": "create_issue_tickets"}]
                        )

        # current_user.db.prompt.update_one(
        #     {"name": name},
        #     {
        #         "$set": {
        #             "text": prompt_content,
        #             "updated_at": datetime.now()
        #         }
        #     },
        #     upsert=True
        # )
        current_user.db.business_info.update_one(
            {"name": "BusinessInfo"},
            {"$set": {"preferred_prompt": prompt}},
            upsert=True
        )

        return {
            "status": "success tool and prefered prompt added"
        }
