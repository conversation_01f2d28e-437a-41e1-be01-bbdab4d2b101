
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
import json
from dotenv import load_dotenv
import asyncio
from pymongo import AsyncMongoClient

from src.core.database import get_admin_db
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.tenant.module.create_tenent import Tenant

from qdrant_client.http.models import Filter, FieldCondition, MatchValue

from typing import Optional
from pydantic import BaseModel
from src.tenant.module.tenant_setup import BusinessInfo
from src.models.user import UserTenantDB
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.v2.KB.kb_setup.handle_documents import Data, add_documents_json
from src.v3.chat.dynamic_reply import generate_response_openai
from src.streamlitdemo.chatdemo.respond import MsgRequest
from src.helper.qdrant import (
    fetch_qdrant_config,
    initialize_clients
)

tenant_router = APIRouter(
    prefix="/tenants",
    tags=["Tenants"],
)

class TenantCreate(BaseModel):
    tenant_name: str
    tenant_slug: str


@tenant_router.post("/create-tenents")
async def create_tenant(
    request: TenantCreate=Depends(),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    print("current_user",current_user.user.role,current_user.user.username)
    if current_user.user.role != "admin" or current_user.user.username != "superadmin":
        raise HTTPException(status_code=403, detail="You are not authorized for this action")
    if not request.tenant_name or not request.tenant_slug:
        raise HTTPException(status_code=400, detail="Tenant name and slug are required")
    
    requirements=get_admin_db().requirements.find_one({"name":"new_tenant_requirement"})
    
    tenant = Tenant(request.tenant_name, request.tenant_slug,requirements)
    try:
        await tenant._register_tenant()
        await tenant._preapre_client_database()
        await tenant._insert_default_data()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
    return {"message": "Tenant created successfully make sure to change tenant_id in data"}




# class Stage1Request(BaseModel):
#     business_info: BusinessInfo  # agent_role = goal

async def get_questions_by_org_type(doc, org_type):
    questions = []
    # Convert org_type to lowercase for case-insensitive comparison
    org_type_lower = org_type.lower() if isinstance(org_type, str) else ""
    
    for entry in doc.get("data", []):
        if entry["type"].lower() == org_type_lower:
            questions.extend(entry.get("questions", []))
    return list(set(questions))  # Remove duplicates (if any)


@tenant_router.post("/stage-1")
async def stage_1(
    data: BusinessInfo,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    # business_data = data.business_info.model_dump()
    # business_data = {k: v for k, v in data.business_info.model_dump().items() if v is not None}
    business_data = {k: v for k, v in data.model_dump().items() if v is not None}



    current_user.db.business_info.update_one(
        {"name": "BusinessInfo"},
        {"$set": business_data},
        upsert=True
    )

    qd_client = Qdrant_Call(config=QdrantConfig(**current_user.qdrant_config))
    org_type = data.org_type

    questions_doc = get_admin_db().requirements.find_one({"name": "setup_questions"})
   
    # if org_type == "Sales":
    #     questions = questions_doc.get("questions", {}).get("sales_question", [])
    # else:
    #     questions = []
    questions = await get_questions_by_org_type(questions_doc, org_type)

    print("questions", questions)

    # Get prompts with different styles
    simplified_prompt, detailed_prompt, elobrated_prompt = data.format_dummy_prompt(current_user)
    
    prompt_variants = [
        ("simplified", simplified_prompt),
        ("detailed", detailed_prompt),
        ("elobrated", elobrated_prompt)
    ]

    async def generate_single_response(question: str, style: str, AI_REPLY_MODE: str) -> dict:
        """Helper function to generate a single response for a question and style"""
        if AI_REPLY_MODE == "simplified":
            system_prompt = current_user.db.prompt.find_one(
                {"name": "reply_prompt_openai_simplified"}
            )
        elif AI_REPLY_MODE == "elaborated":
            system_prompt = current_user.db.prompt.find_one(
                {"name": "reply_prompt_openai_elaborated"}
            )
        else:
            system_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai"})

        if not system_prompt:
            raise HTTPException(status_code=500, detail="System prompt not found")

        try:
            reply, _ = await generate_response_openai(
                MsgRequest(
                    customer_id=current_user.user.id,
                    message=question,
                    channel="Playground",
                ),
                system_prompt,
                qd_client,
                current_user,
            )
            return {
                "style": style,
                "user_query": question,
                "response_example": reply if isinstance(reply, str) else str(reply)
            }
        except Exception as e:
            return {
                "style": style,
                "user_query": question,
                "error": f"Error processing response: {str(e)}"
            }

    # Create all tasks
    tasks = []
    for question in questions:
        for style, AI_REPLY_MODE in prompt_variants:
            tasks.append(generate_single_response(question, style, AI_REPLY_MODE))

    # Run all tasks concurrently
    example_responses = await asyncio.gather(*tasks)

    return {
        "questions": questions,
        "prompt_variants": example_responses
    }





@tenant_router.get("/tools")
async def setup_tenant(current_user: UserTenantDB = Depends(get_tenant_info)):
    tool_config = get_admin_db().requirements.find_one({"name": "tool_setup"})
    available_tools = tool_config.get("available_tools", []) if tool_config else []

    return available_tools
    



class Stage2Request(BaseModel):
    prompt: str
    tools: list[str]



@tenant_router.post("/stage-2")
async def setup_tenant_tools(
    data: Stage2Request, 
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        tenant_doc = current_user.db.business_info.find_one({"name":"BusinessInfo"})
        if not tenant_doc:
            raise HTTPException(status_code=400, detail="Business info not found. Please complete stage-1 first.")

        # business_info = BusinessInfo(**tenant_doc["business_info"])

        # tools_list = []
        # tool_setup = get_admin_db().requirements.find_one({"name": "tool_setup"})
        # if not tool_setup:
        #     raise HTTPException(status_code=404, detail="Tool setup configuration not found")

        # tool_mapping = {tool["name"]: tool["description"] 
        #                for tool in tool_setup.get("available_tools", [])}

        

        result = BusinessInfo.update_prompt(
            prompt=data.prompt,  
            tools=data.tools,
            # business_info=business_info,
            current_user=current_user
        )
        
        return {
            "result": result
        }
    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    



@tenant_router.get("/business-info")
async def get_business_info(current_user: UserTenantDB = Depends(get_tenant_info)):
    tenant_doc = current_user.db.business_info.find_one(
        {"name": "BusinessInfo"},
        {"_id": 0,"name":0}  
    )

    if not tenant_doc:
        return {
            "status": "error",
            "message": "Business info not found"
        }
    
    return {
        "status": "success",
        "result": tenant_doc
    }

class BusinessInfoEdit(BaseModel):
    org_name: Optional[str] = None
    org_type: Optional[str] = None
    agent_name: Optional[str] = None
    agent_role: Optional[str] = None
    preferred_prompt:Optional[str] = None


@tenant_router.post("/edit-info")
async def edit_business_info(
    data: BusinessInfoEdit,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    update_data = data.model_dump(exclude_unset=True)
    
    if not update_data:
        return {"status": "error", "message": "No valid fields provided for update"}
    
    # Update only the provided fields in the database
    current_user.db.business_info.update_one(
        {"name": "BusinessInfo"},
        {"$set": {**update_data, "set_up_complete": True}},
        upsert=True
    )

    return {"status": "success", "updated_fields": list(update_data.keys())}


@tenant_router.get("/setup-check")
async def setup_check(current_user: UserTenantDB = Depends(get_tenant_info)):
    check = current_user.db.business_info.find_one({"name": "BusinessInfo"})

    if not check:
        raise HTTPException(status_code=400, detail="Business Info not found")

    verified = check.get("set_up_complete", False)

    if not verified:
        raise HTTPException(status_code=400, detail="Setup not complete")

    return {
        "status": "success"
    }


@tenant_router.post("/dummy-data")
async def dummy_data(current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        metadata = {
            "category": "Laptop",
            "name": "MAC",
        }

        qdrant_config, minio_config = await fetch_qdrant_config(current_user)
        qarant, minio = await initialize_clients(qdrant_config, minio_config)

        # Define metadata filter
        filter_condition = Filter(
            must=[
                FieldCondition(key="category", match=MatchValue(value=metadata["category"])),
                FieldCondition(key="name", match=MatchValue(value=metadata["name"])),
            ]
        )
        count = qarant.client_().count(
            collection_name=qdrant_config["page_collection"],
            exact=True,
            count_filter=filter_condition
        )

        if count.count > 0:
            return {"message": "Documents with this metadata already exist"}


        # Step 2: Prepare and insert if not exists
        data_json = {
            "text": dummy_text,
            "metadata": metadata
        }

        formatted_data = [Data(**data_json)]
        await add_documents_json(formatted_data=formatted_data, current_user=current_user)

        return {
            "status": "dummy data uploaded"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@tenant_router.get("/org-type")
async def get_org_types(current_user: UserTenantDB = Depends(get_tenant_info)):
    org_type_doc = get_admin_db().requirements.find_one({"name": "org_type"})
    types=org_type_doc.get("types", [])
    return types
