# from typing import Optional
# from fastapi import APIRouter, Depends, HTTPException
# from pydantic import BaseModel
# from src.core.database import get_admin_db
# from src.tenant.module.tenant_setup import BusinessInfo
# from src.models.user import UserTenantDB
# from src.core.security import get_tenant_info
# import asyncio

# from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
# from src.v2.KB.kb_setup.handle_documents import Data, add_documents_json
# from src.v3.chat.dynamic_reply import generate_response_openai
# from src.streamlitdemo.chatdemo.respond import MsgRequest

# tenant_setup = APIRouter(prefix="/tenant_setup", tags=["tenant_setup"])

# class Stage1Request(BaseModel):
#     business_info: BusinessInfo  # agent_role = goal

# @tenant_setup.post("/stage-1")
# async def stage_1(
#     data: Stage1Request,
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ):
#     current_user.db.business_info.update_one(
#         {"name": "BusinessInfo"},
#         {"$set": {"business_info": data.business_info.model_dump()}},
#         upsert=True
#     )

#     qd_client = Qdrant_Call(config=QdrantConfig(**current_user.qdrant_config))
#     goal = data.business_info.agent_role
#     business_name = data.business_info.org_name
#     org_type = data.business_info.org_type

#     questions_doc = get_admin_db().requirements.find_one({"name": "setup_questions"})
#     dummy_data = questions_doc.get("dummy_data", {}).get("sales", " ")
    
#     data_json = {
#         "text": f"{dummy_data}",
#         "metadata": {
#             "category": "Laptop",
#             "name": "MAC",
#         }
#     }
#     formatted_data = [Data(**data_json)]
#     dummy_data = await add_documents_json(formatted_data=formatted_data, current_user=current_user)

#     if org_type == "sales":
#         questions = questions_doc.get("questions", {}).get("sales_question", [])
#     else:
#         questions = []

#     print("questions", questions)

#     # Get prompts with different styles
#     simplified_prompt, detailed_prompt, elobrated_prompt = data.business_info.format_dummy_prompt(current_user)
    
#     prompt_variants = [
#         ("simplified", simplified_prompt),
#         ("detailed", detailed_prompt),
#         ("elobrated", elobrated_prompt)
#     ]

#     async def generate_single_response(question: str, style: str, AI_REPLY_MODE: str) -> dict:
#         """Helper function to generate a single response for a question and style"""
#         if AI_REPLY_MODE == "simplified":
#             system_prompt = current_user.db.prompt.find_one(
#                 {"name": "reply_prompt_openai_simplified"}
#             )
#         elif AI_REPLY_MODE == "elaborated":
#             system_prompt = current_user.db.prompt.find_one(
#                 {"name": "reply_prompt_openai_elaborated"}
#             )
#         else:
#             system_prompt = current_user.db.prompt.find_one({"name": "reply_prompt_openai"})

#         if not system_prompt:
#             raise HTTPException(status_code=500, detail="System prompt not found")

#         try:
#             reply, _ = await generate_response_openai(
#                 MsgRequest(
#                     customer_id=current_user.user.id,
#                     message=question,
#                     channel="Playground",
#                 ),
#                 system_prompt,
#                 qd_client,
#                 current_user,
#             )
#             return {
#                 "style": style,
#                 "user_query": question,
#                 "response_example": reply if isinstance(reply, str) else str(reply)
#             }
#         except Exception as e:
#             return {
#                 "style": style,
#                 "user_query": question,
#                 "error": f"Error processing response: {str(e)}"
#             }

#     # Create all tasks
#     tasks = []
#     for question in questions:
#         for style, AI_REPLY_MODE in prompt_variants:
#             tasks.append(generate_single_response(question, style, AI_REPLY_MODE))

#     # Run all tasks concurrently
#     example_responses = await asyncio.gather(*tasks)

#     return {
#         "questions": questions,
#         "prompt_variants": example_responses
#     }





# @tenant_setup.post("/tools")
# async def setup_tenant(current_user: UserTenantDB = Depends(get_tenant_info)):
#     tool_config = get_admin_db().requirements.find_one({"name": "tool_setup"})
#     available_tools = tool_config.get("available_tools", []) if tool_config else []

#     return {
#         "tools": available_tools
#     }


# # class tool_request(BaseModel):
# #     name: str
# #     description:str

# class Stage2Request(BaseModel):
#     prompt: str
#     tools: list[str]



# @tenant_setup.post("/stage-2")
# async def setup_tenant_tools(
#     data: Stage2Request, 
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ):
#     try:
#         tenant_doc = current_user.db.business_info.find_one({"name":"BusinessInfo"})
#         if not tenant_doc or "business_info" not in tenant_doc:
#             raise HTTPException(status_code=400, detail="Business info not found. Please complete stage-1 first.")

#         business_info = BusinessInfo(**tenant_doc["business_info"])

#         # tools_list = []
#         # tool_setup = get_admin_db().requirements.find_one({"name": "tool_setup"})
#         # if not tool_setup:
#         #     raise HTTPException(status_code=404, detail="Tool setup configuration not found")

#         # tool_mapping = {tool["name"]: tool["description"] 
#         #                for tool in tool_setup.get("available_tools", [])}

        

#         result = BusinessInfo.update_prompt(
#             prompt=data.prompt,  
#             tools=data.tools,
#             business_info=business_info,
#             current_user=current_user
#         )
        
#         return {
#             "status": "success",
#             "result": result
#         }
#     except HTTPException as he:
#         raise he
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))