from src.v3.chat.routes import v3_reply_router
# from src.v3.chat.agent_sdk_tool.api import agent_router
from fastapi import FastAP<PERSON>, Request
from fastapi.openapi.utils import get_openapi
from fastapi.responses import RedirectResponse

def get_dynamic_server_url(request: Request = None) -> str:
    """
    Get dynamic server URL based on request context.
    Falls back to relative URLs if no request context is available.
    """
    if request is None:
        return "/v3"  # Relative URL fallback

    # Get scheme (http/https) and host from request
    scheme = request.url.scheme
    host = request.headers.get("host", "localhost:8000")

    return f"{scheme}://{host}/v3"

def get_dynamic_login_url(request: Request = None) -> str:
    """
    Get dynamic login URL based on request context.
    Falls back to relative URL if no request context is available.
    """
    if request is None:
        return "/login"  # Relative URL fallback

    # Get scheme (http/https) and host from request
    scheme = request.url.scheme
    host = request.headers.get("host", "localhost:8000")

    return f"{scheme}://{host}/login"

v3_app = FastAPI(
    title="Echo Bot API v3",
    description="Version 3 of the Echo Bot API",
    version="3.0.0",
    docs_url=None,
    redoc_url=None,
    swagger_ui_oauth2_redirect_url="/v3/docs/oauth2-redirect",
    openapi_url="/openapi.json",
    servers=[
        {
            "url": "/v3",
            "description": "V3 API Service"
        }
    ]
)

def custom_openapi():
    if v3_app.openapi_schema:
        return v3_app.openapi_schema

    # Use relative URLs for servers - FastAPI will resolve them dynamically
    openapi_schema = get_openapi(
        title=v3_app.title,
        version=v3_app.version,
        routes=v3_app.routes,
        servers=[
            {"url": "/v3", "description": "V3 Chat API Server (Current Host)"},
            {"url": "http://localhost:8000/v3", "description": "V3 Chat API Server (Localhost)"}
        ]
    )

    # Use relative URL for token endpoint - FastAPI will resolve dynamically
    openapi_schema["components"]["securitySchemes"] = {
        "OAuth2PasswordBearer": {
            "type": "oauth2",
            "flows": {
                "password": {
                    "tokenUrl": "/login",  # Relative URL - will be resolved dynamically
                    "scopes": {}
                }
            }
        }
    }

    v3_app.openapi_schema = openapi_schema
    return v3_app.openapi_schema

v3_app.openapi = custom_openapi
v3_app.include_router(v3_reply_router, tags=["Chat v3"])

# redirect / to docs



# v3_app.include_router(agent_router, tags=["Agents"])