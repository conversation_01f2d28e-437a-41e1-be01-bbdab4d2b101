

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException
from fastapi.security import OAuth2Password<PERSON>earer
from pydantic import BaseModel, constr, field_validator

from src.core.security import get_tenant_info
from src.models.model_run import ModelRunRequest
from src.models.user import UserTenantDB
from src.routes.respond import final
from src.helper.logger import  setup_new_logging, apply_logging_to_all_functions

from src.models.model_run import FollowUpRequest
from nest_asyncio import apply
from typing import List
from bson import ObjectId
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.chat_history import ChatHistMessage
from src.models.model_run import MessageVerificationRequest,AgentReplyRequest
from src.helper import logger
from src.helper.resolve_llm import resolve_llm


loggers = logger.setup_new_logging(__name__)
# from src.helper.logger import apply_logging_to_all_functions

# apply_logging_to_all_functions(globals())



# Configure logging
# logger = setup_new_logging(__name__)
# apply()
# apply_logging_to_all_functions(globals())


ag_router = APIRouter()

class Chat(BaseModel):
    role: constr(min_length=4, max_length=6)
    content: constr(min_length=1)
    media_urls: List[str] | None = None 

    @field_validator("role")
    def validate_key(cls, v):
        allowed_keys = {"user", "agent"}
        if v not in allowed_keys:
            raise ValueError(f"Role must be one of {allowed_keys}")
        return v


class FinalRequestModel(BaseModel):
    user_name: str
    user_id: int
    message: str
    chat_data: list
    chat_data_format: str
    primary_product_code: str | None
    primary_product: str | None
    media_ids: list
    tags: list


class AGRequest(BaseModel):
    message: str = "Tsc ko class routine?"
    message_media_urls: List[str] | None = []
    chat_history: List[Chat] | None = []
    primary_product: str | None = None
    user_name: str | None = "Developer"
    user_id: int | None = -1
    conversation_id: int | None = -1
    guru_id: int | None = -1
    guru_name: str | None = None

    @field_validator("message_media_urls")
    def validate_media_urls(cls, v):
        from urllib.parse import urlparse, urlunparse
        def get_base_url(full_url):
            parsed_url = urlparse(full_url)
            return urlunparse(parsed_url._replace(query=''))
        
        return [get_base_url(url) for url in v]

    @field_validator("message")
    def validate_message(cls, v):
        if v.strip() == "":
            raise ValueError("Message should not be empty")
        else:
            return v
        
    
    def change_to_eko_analyse_format(self):

        def change_chat_to_eko_format(chat: Chat):
            return {
                'role': chat.role,
                'content': chat.content,
                'media': chat.media_urls,
            }
        
        chat_data = [change_chat_to_eko_format(chat) for chat in self.chat_history]
        chat_data.append(
            {
                "role": "user",
                "content": self.message,
                "media": self.message_media_urls,
            }
        )
        
        return FinalRequestModel(
            user_name=self.user_name,
            user_id=self.user_id,
            message=self.message,
            chat_data=chat_data,
            chat_data_format="role_content",
            primary_product_code=self.primary_product,
            primary_product=self.primary_product,
            media_ids=self.message_media_urls,
            tags=[],
        )



@ag_router.post("/async_context/", tags=["ask_guru"])
async def context_post(
    request: AGRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)  
) -> dict:
    print(f"current_user : {current_user}")
    json_data = request.change_to_eko_analyse_format()
    response = await final(json_data, current_user)
    print(f"final api response: {response}")
    req_data = response

    to_return = {
        "contextualized_sentence": req_data.get("response", {}).get("contextualize"),
        "suggested_reply": req_data.get("response", {}).get("suggested_reply"),
        "category": req_data.get("response", {}).get("categories", [])[0] if req_data.get("response", {}).get("categories") else None,
        "message_id": req_data.get("response", {}).get("chat_message_id"),
        "verified": False,
    }

    return to_return




  
@ag_router.post("/agent_message_verification",tags=["ask_guru"])
async def agent_verify_message(request: MessageVerificationRequest, current_user: UserTenantDB = Depends(get_tenant_info)):
    #is_edited is true if AI response is edited, false if its new message from agent

        print(request)
        update_message={
            "role":"assistant",
            "content":request.verified_answer,
            "sender":request.verified_by,
            "created_at":datetime.now(),
            "user_id":request.user_id,
            "chat_ids":[],
            "verified":request.verified,
            "verified_by":request.verified_by,
            "verified_at":datetime.now()


        }

        if current_user.db.chat_messages.find_one({"_id":ObjectId(request.response_id)}):
            current_user.db.chat_messages.update_one(
                {"_id":ObjectId(request.response_id)},
            {"$set":{"content":request.verified_answer,"verified":request.verified,"verified_by":request.verified_by,"verified_at":datetime.now()}})
        else:
            update_message["_id"] = ObjectId(request.response_id)
            current_user.db.chat_messages.insert_one(update_message)
            
        current_user.db.ai_response.update_one(
                    {"response.chat_message_id": request.response_id},
                    {
                        "$set": {
                            "response.verified": request.verified,  
                            "response.verified_by": request.verified_by,
                            "response.reply": request.verified_answer
                        }
                    }
                )
        return {"message":"Suggested reply saved successfully"}


@ag_router.post("/agent_reply",tags=["ask_guru"])
async def agent_reply(
    request: AgentReplyRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        reply_data = ChatHistMessage(
                    role="agent", content=request.content, sender=request.sender, created_at=datetime.now(), user_id=request.user_id,verified=True,verified_by=request.sender,verified_at=datetime.now(),id=None)
        reply_data_dict = reply_data.model_dump_mongo()
        chat_message_id = current_user.db["chat_messages"].insert_one(reply_data_dict)
        if chat_message_id:
            loggers.info("Agent reply saved to database")
        else:
            raise HTTPException(status_code=500, detail="Failed to save reply")
        
        ai_response_data = current_user.db["ai_response"].update_one({"_id":ObjectId(request.response_id)},{"$push":{"request.chat_data":ObjectId(chat_message_id.inserted_id)}})
        if ai_response_data:
            loggers.info("Modified ai response")
        return {"message":"Reply saved successfully"}
    except Exception as e:
        raise e

async def fetch_message_info(db, message_id: str):
    """Fetch contextualized message, actual response message with media_value, and suggested reply from the database."""
    chat_message = db.chat_messages.find_one({"_id": ObjectId(message_id)})
    if not chat_message:
        return {"error": "Message ID not found in chat_messages"}

    ai_response = db.ai_response.find_one({"response.chat_message_id": message_id})  

    if not ai_response:
        return {"error": "Message ID not found in ai_response"}

    # Step 3: Extract relevant data safely
    response_data = ai_response.get("response", {})
    request_data = ai_response.get("request", {})

    loggers.debug(f"Fetched ai_response document: {ai_response}")

    return {
        "contextualize": response_data.get("contextualize", "No contextualized message found"),
        "message": response_data.get("latest_message", "No message found"),
        "media_values": request_data.get("media_values", None),
        "suggested_reply": response_data.get("suggested_reply", "No suggested reply found"),
    }


def follow_up_engine(current_user, contextualize, message, suggested_reply, follow_up_msg):
    """Generate a follow-up response using OpenAI."""

    PROMPT_NAME = "follow_up_reply_prompt"

    prompt = current_user.db["prompt"].find_one({"name": PROMPT_NAME})

    try:
        llm = resolve_llm(model_name=prompt.get("model"))
        return llm.complete(
            prompt.get("text").format(
                contextualize=contextualize,
                message=message,
                suggested_reply=suggested_reply,
                follow_up_msg=follow_up_msg,
            ),
            formatted=True,
        ).text
    except Exception as e:
        raise e

@ag_router.post("/follow-up")
async def reply_follow_up(
    request: FollowUpRequest,  
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    
    try:
        message_id = request.message_id
        follow_up_msg = request.follow_up_msg

        loggers.debug(f"Fetching message ID for follow-up: {message_id}")  
        message_info = await fetch_message_info(current_user.db, message_id)

        if "error" in message_info:
            raise HTTPException(status_code=404, detail=message_info["error"])

        contextualize = message_info["contextualize"]
        message = message_info["message"]
        media_values = message_info["media_values"]
        if media_values:
            loggers.debug(f"Media values found")
        final_query = f"User's Message: ```{message}```\n\nQuestion: ```{media_values}```" if media_values else message

        suggested_reply = message_info["suggested_reply"]

        follow_up_response = follow_up_engine(
            current_user, contextualize, final_query, suggested_reply, follow_up_msg
        )

        update_result = current_user.db.ai_response.update_one(
            {"response.chat_message_id": message_id},
            {"$set": {"response.suggested_reply": follow_up_response}}
        )
        
        update_chat_message = current_user.db.chat_messages.update_one(
            {"_id": ObjectId(message_id)},
            {"$set": {"content": follow_up_response}}
        )

        if update_result.modified_count == 0:
            loggers.warning("Warning: AI response was updated")  # Changed to `warning`
        if update_chat_message.modified_count == 0:
            loggers.warning("Warning: Chat message was updated")  # Changed to `warning`
        return {
            "message_id": message_id,
            "follow_up_response": follow_up_response,
        }

    except Exception as e:
        loggers.error(f"Error processing follow-up: {str(e)}")  # Logging the error
        raise HTTPException(
            status_code=500,
            detail=f"Internal Server Error: {str(e)}"
        )
