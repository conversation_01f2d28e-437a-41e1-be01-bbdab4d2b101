import asyncio
from datetime import datetime
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from langchain_core.runnables import RunnableMap
from llama_index.llms.openai import OpenAI
from nest_asyncio import apply
from bson import ObjectId

from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.chat_history import ChatHistMessage
from src.models.customers import CustomerModel
from src.models.summary import SummaryModel
from src.models.model_run import FollowUpRequest, ModelRunRequest, ModelResponse
from src.models.ReplyHandler import ReplyHandler
from src.v2.chat.contextualize import contextualize_messages
from src.v2.chat.identify_product import identify_product_from_query_and_summary
from src.v2.chat.categorize.categorise import Category as CategoryOutput
from src.reply.sentiment_language import sentiment_classification, detect_language
from src.helper.logger import  setup_new_logging, apply_logging_to_all_functions
from src.helper.resolve_llm import resolve_llm
from src.v2.chat.categorize.additional_information import check_requirements
from datetime import datetime
from fastapi import HTTPException, Depends
from typing import Dict, Any

import logging
from bson import ObjectId

logger = logging.getLogger(__name__)

# Configure logging
logger = setup_new_logging(__name__)
apply()
apply_logging_to_all_functions(globals())

# Initialize router
respond_router = APIRouter()

async def get_root_category(current_user: UserTenantDB) -> CategoryOutput:
    """Retrieve root category based on tenant ID."""
    if current_user.tenant_id == "6762cd86b4e4379d71ef93ee":
        category_filter = {"_id": ObjectId("6796042da046d6f229babb4b")}
    else:
        category_filter = {"name": "Root"}
    
    if root_category := current_user.db.category.find_one(category_filter):
        return CategoryOutput(**root_category)
    raise HTTPException(status_code=402, detail="Root category not found")

async def process_chat_history(current_user: UserTenantDB, request: ModelRunRequest) -> Any:
    """Fetch and format chat history."""
    user_chat_history = CustomerModel.fetch_chat_hist(request,current_user)
    chat_history = CustomerModel.format_chat_history(
        current_user, request.user_id, user_chat_history, request.chat_data_format
    )
    
    if not chat_history:
        raise HTTPException(status_code=400, detail="No chat history found")
    
    return chat_history[0][-1], chat_history

async def execute_initial_chain(current_user: UserTenantDB, chat_history: Any, latest_message: str, user_id: str) -> dict:
    """Execute initial processing chain."""
    print(f"chat hist {chat_history}"
          )
    print(f"latest_message {latest_message}")
    
    return RunnableMap({
        "chat_summary": lambda _: SummaryModel.process_chat_history(current_user.db, chat_history, user_id),
        "contextualize": lambda _: contextualize_messages(
            current_user=current_user,
            message=latest_message,
            chat_hist=chat_history,
        ),
    }).invoke({})

async def execute_analysis_chain(
    current_user: UserTenantDB, 
    initial_results: dict, 
    latest_message: str, 
    latest_chat_data: Any, 
    summary: Any,
    category: CategoryOutput
) -> dict:
    """Execute analysis chain with language model."""
    return RunnableMap({
        "identify": lambda _: identify_product_from_query_and_summary(
            current_user=current_user,
            query=latest_message,
            context=initial_results["contextualize"],
            chat_summary=initial_results["chat_summary"],
            chat_hist=latest_chat_data,
        ),
        "category": lambda _: asyncio.run(
            category.resolve(
                initial_message=initial_results["contextualize"],
                summary=summary,
                
                media_values=latest_chat_data.media_values,
                client=OpenAI(model="gpt-4o-mini"),
                current_user=current_user
            )
        ),
        "sentiment": lambda _: sentiment_classification(current_user=current_user, text=latest_message),
        "language": lambda _: detect_language(current_user=current_user, text=latest_message),
    }).invoke({})

async def handle_database_operations(
    current_user: UserTenantDB,
    chat_history_format: list[ChatHistMessage],
    summary_format: Any,
    request: ModelRunRequest,
    final_response: dict
) -> None:
    """Handle database insertions and updates."""
    # Insert chat messages
    for chat in chat_history_format:
        try:
            chat.summary_id = summary_format.id
            if current_user.db.chat_messages.find_one({"_id": ObjectId(chat.id)}):
                print(f"Chat message with ID {chat.id} already exists. Skipping insertion.")
                continue
            current_user.db.chat_messages.insert_one(chat.model_dump_mongo())
        except Exception as e:
            print(chat)
            raise Exception
            logger.error(f"Failed to insert chat message: {str(e)}")

    # Insert reply into the database if it exists
    if final_response["reply"]:
        print("reply",final_response["reply"])
        reply = ChatHistMessage(
            role="assistant",
            content=final_response["reply"],
            sender="bot",
            created_at=datetime.now(),
            user_id=request.user_id,
            summary_id=None,
            id=None

        )
        try:
            current_user.db.chat_messages.insert_one(reply.model_dump_mongo())
            # summary_format.chat_ids.append(str(reply.id))
        except Exception as e:
            logger.error(f"Failed to insert reply: {str(e)}")
    if final_response["suggested_reply"]:
        print("suggested_reply",final_response["suggested_reply"])
        reply = ChatHistMessage(
            role="assistant",
            content=final_response["suggested_reply"],
            sender="bot",
            created_at=datetime.now(),
            user_id=request.user_id,
            summary_id=None,
            id=None,
            verified=False

        )
        final_response["verified"]=False
        final_response["verified_by"]=None
        message_id=current_user.db.chat_messages.insert_one(reply.model_dump_mongo())
        final_response["chat_message_id"]=str(message_id.inserted_id)
        summary_format.chat_ids.append(str(reply.id))
    # Insert updated summary
    try:
        current_user.db.chat_messages.insert_one(summary_format.model_dump_mongo())
    except Exception as e:
        logger.error(f"Failed to insert summary: {str(e)}")
    # Insert AI response
    try:
        latest_chat_data=chat_history_format[0]
        request_data = request.dict()
        request_data["incoming_chat"] = request_data.pop("chat_data")
        request_data["chat_data"] = [str(chat.id) for chat in chat_history_format]
        request_data["media_values"] = latest_chat_data.media_values
        request_data["media_ids"] = latest_chat_data.media_ids
        
        current_user.db.ai_response.insert_one({
            "request": request_data,
            "response": final_response,
            "created_at": datetime.now()
        })
    except Exception as e:
        logger.error(f"Failed to insert AI response: {str(e)}")
        raise


@respond_router.post("/final", response_model=ModelResponse)
async def final(
    request: ModelRunRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> dict:
    """Endpoint to process chat messages and generate responses with step-by-step response building."""
    request_time = datetime.now()
    final_response: Dict[str, Any] = {}

    logger.debug(f"request data: {request}")
    
    print(current_user)
    try:
        # Step 1: Initial Setup and Chat History Processing
        final_response["request_time"] = str(datetime.now() - request_time)
        latest_chat_data, chat_history_format = await process_chat_history(current_user, request)

        print(f"Latest Chat Data: {latest_chat_data}")
        latest_message = latest_chat_data.content
        final_response["latest_message"] = latest_message
        
        # Step 2: Category Processing
        category = await get_root_category(current_user)
        
        # Step 3: Execute Initial Chain
        initial_results = await execute_initial_chain(
            current_user, 
            chat_history_format, 
            latest_message, 
            request.user_id
        )
        logger.debug(f"Initial results: {initial_results}")
        final_response["contextualize"] = initial_results["contextualize"]
        final_response["formatted_summary"] = initial_results["chat_summary"][0]
        final_response["summary_id"] = initial_results["chat_summary"][1].id
        
        # Step 4: Execute Analysis Chain
        analysis_results = await execute_analysis_chain(
            current_user,
            initial_results,
            latest_message,
            latest_chat_data,
            initial_results["chat_summary"][0],
            category
        )
        final_response["identified_product"] = [
            product.course for product in analysis_results["identify"]
        ]
        final_response["categories"] = [
            cat.name for cat in analysis_results["category"]
        ]
        final_response["sentiment"] = analysis_results["sentiment"]
        final_response["language"] = analysis_results["language"]
        
        # Step 5: Generate Response
        reply_handler = ReplyHandler(
            current_user=current_user,
            request=latest_message,
            categories=analysis_results["category"],
            identified_product=analysis_results["identify"],
            contextualize=initial_results["contextualize"],
            summary=initial_results["chat_summary"],
            chat_raw_hist=chat_history_format,
            language_response=analysis_results["language"],
            db=current_user.db,
            final_response=final_response
        )
        response = await reply_handler.handle()
        
        # Step 6: Add Response Data
        response_fields = [
            "reply", "suggested_reply", "retreival_reply",
            "relevance", "faithfulness","suggested_images"
        ]
        for field in response_fields:
            final_response[field] = response[field]
        
        # Step 7: Process Call to Action
        final_response["call_to_action"] = response["call_to_action"] + await check_requirements(
            analysis_results["category"],
            final_response,
            "call_to_action",
            current_user
        )
        final_response["response_time"]= str(datetime.now() - request_time)
        
        # Step 8: Database Operations
        print("Final Response", chat_history_format)
        await handle_database_operations(
            current_user,
            chat_history_format[0],
            initial_results["chat_summary"][1],
            request,
            final_response
        )
        
        # Step 9: Update Customer Profile
        current_user.db.customers.update_one(
            {"customer_id": request.user_id},
            {
                "$set": {
                    "customer_name": request.user_name,
                    "primary_product": final_response["identified_product"]
                }
            },
            upsert=True
        )
        
        # Step 10: Return Final Response
        return {
            "request": request.dict(),
            "response": final_response
        }
        
    except HTTPException as e:
        raise
        
    except Exception as e:
        logger.error(f"Processing error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Processing error: {str(e)}"
        )



async def fetch_message_info(db, message_id: str):
    """Fetch contextualized message, actual response message with media_value, and suggested reply from the database."""
    chat_message = db.chat_messages.find_one({"_id": ObjectId(message_id)})
    if not chat_message:
        return {"error": "Message ID not found in chat_messages"}

    ai_response = db.ai_response.find_one({"response.chat_message_id": message_id})  

    if not ai_response:
        return {"error": "Message ID not found in ai_response"}

    # Step 3: Extract relevant data safely
    response_data = ai_response.get("response", {})
    request_data = ai_response.get("request", {})

    logger.debug(f"Fetched ai_response document: {ai_response}")

    return {
        "contextualize": response_data.get("contextualize", "No contextualized message found"),
        "message": response_data.get("latest_message", "No message found"),
        "media_values": request_data.get("media_values", None),
        "suggested_reply": response_data.get("suggested_reply", "No suggested reply found"),
    }


def follow_up_engine(current_user, contextualize, message, suggested_reply, follow_up_msg):
    """Generate a follow-up response using OpenAI."""

    PROMPT_NAME = "follow_up_reply_prompt"

    prompt = current_user.db["prompt"].find_one({"name": PROMPT_NAME})

    try:
        llm = resolve_llm(model_name=prompt.get("model"))
        return llm.complete(
            prompt.get("text").format(
                contextualize=contextualize,
                message=message,
                suggested_reply=suggested_reply,
                follow_up_msg=follow_up_msg,
            ),
            formatted=True,
        ).text
    except Exception as e:
        raise e

@respond_router.post("/follow-up")
async def reply_follow_up(
    request: FollowUpRequest,  
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    
    try:
        message_id = request.message_id
        follow_up_msg = request.follow_up_msg

        logger.debug(f"Fetching message ID for follow-up: {message_id}")  
        message_info = await fetch_message_info(current_user.db, message_id)

        if "error" in message_info:
            raise HTTPException(status_code=404, detail=message_info["error"])

        contextualize = message_info["contextualize"]
        message = message_info["message"]
        media_values = message_info["media_values"]

        final_query = f"User's Message: ```{message}```\n\nQuestion: ```{media_values}```" if media_values else message

        suggested_reply = message_info["suggested_reply"]

        follow_up_response = follow_up_engine(
            current_user, contextualize, final_query, suggested_reply, follow_up_msg
        )

        update_result = current_user.db.ai_response.update_one(
            {"_id": ObjectId(message_id)},
            {"$set": {"response.suggested_reply": follow_up_response}}
        )

        if update_result.modified_count == 0:
            logger.warning("Warning: No document was updated")  # Changed to `warning`

        return {
            "message_id": message_id,
            "follow_up_response": follow_up_response,
        }

    except Exception as e:
        logger.error(f"Error processing follow-up: {str(e)}")  # Logging the error
        raise HTTPException(
            status_code=500,
            detail=f"Internal Server Error: {str(e)}"
        )
