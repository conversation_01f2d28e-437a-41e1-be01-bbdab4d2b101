from fastapi import APIRouter
src_routers = APIRouter()

from src.v2.external_hooks.whatsapp_webhook.twilio_setup import router as whatsapp_webhook_router
from src.routes.respond import respond_router
from src.routes.ask_guru import ag_router
# from src.routes.prompts import prompts_router
from src.routes.reply import reply_router as reply_router
# from src.v2.external_hooks.whatsapp_webhook.send_message import router as send_whatsapp_message_router
from src.routes.send_reply import router as send_reply_router
from src.v2.external_hooks.whatsapp_webhook.twilio_hooks import hook_router
# Sociar routes now consolidated in social_media_webhooks module
# from src.routes.credits import credits_router
# from src.tenant.create_tenent import tenant_router


src_routers.include_router(whatsapp_webhook_router)
src_routers.include_router(respond_router)
src_routers.include_router(ag_router)
# src_routers.include_router(prompts_router)
src_routers.include_router(reply_router)
# src_routers.include_router(send_whatsapp_message_router)
src_routers.include_router(send_reply_router)
src_routers.include_router(hook_router)
# sociar_router now included in consolidated social_media_webhooks module
# src_routers.include_router(credits_router)

