"""
API routes for activity logs.
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any

from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.activity_log import ActivityLog
from src.core.activity_utils import get_user_activity, get_active_users
from src.helper.logger import setup_new_logging
from src.background_tasks.assign_agent import assign_to_cta

# Initialize logging
loggers = setup_new_logging(__name__)

# Create router
router = APIRouter(prefix="/activity", tags=["Activity Logs"])

@router.get("/logs/{user_id}", response_model=ActivityLog)
async def get_user_activity_logs(
    user_id: str,
    limit: int = Query(100, ge=1, le=1000),
    skip: int = Query(0, ge=0),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get activity logs for a specific user.

    Args:
        user_id: ID of the user
        limit: Maximum number of activity data entries to return
        skip: Number of activity data entries to skip

    Returns:
        ActivityLog object
    """
    # Check if user has permission to view this user's logs
    if current_user.id != user_id and not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to view this user's activity logs"
        )

    # Get activity logs
    activity_log = await get_user_activity(
        current_user.async_db,
        user_id,
        limit,
        skip
    )

    if not activity_log:
        raise HTTPException(
            status_code=404,
            detail="Activity logs not found for this user"
        )

    return activity_log

@router.get("/logs", response_model=List[ActivityLog])
async def get_all_activity_logs(
    limit: int = Query(10, ge=1, le=100),
    skip: int = Query(0, ge=0),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get activity logs for all users.

    Args:
        limit: Maximum number of activity logs to return
        skip: Number of activity logs to skip

    Returns:
        List of ActivityLog objects
    """
    # Check if user has permission to view all logs
    if not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="You don't have permission to view all activity logs"
        )

    # Get activity logs
    try:
        cursor = current_user.async_db.activity_logs.find().skip(skip).limit(limit)
        activity_logs = await cursor.to_list(length=limit)

        # Convert to ActivityLog models
        return [ActivityLog(**log) for log in activity_logs]
    except Exception as e:
        loggers.error(f"Error getting all activity logs: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error retrieving activity logs"
        )

@router.get("/active-users", response_model=List[Dict[str, Any]])
async def get_recently_active_users(
    hours: int = Query(24, ge=1, le=720),  # Max 30 days
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get users who have been active within the specified time period.

    Args:
        hours: Number of hours to look back (default: 24)

    Returns:
        List of user documents with their last active time
    """
    # Check if user has permission to view all logs
    # if not current_user.is_admin:
    #     raise HTTPException(
    #         status_code=403,
    #         detail="You don't have permission to view active users"
    #     )

    # Get active users
    try:
        active_users = await get_active_users(current_user.async_db, hours)
        return active_users
    except Exception as e:
        loggers.error(f"Error getting active users: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error retrieving active users"
        )



# @router.post("/assign_agent")
# async def assign_agent(cta_id: str, current_user: UserTenantDB = Depends(get_tenant_info)):
#     assign_to=await assign_to_cta(current_user)
#     return assign_to