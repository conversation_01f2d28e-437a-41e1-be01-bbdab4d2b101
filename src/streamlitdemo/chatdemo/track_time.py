# import time

# def time_it(func):
#     """Decorator to measure and print execution time of a function."""
#     def wrapper(*args, **kwargs):
#         # Remove any custom unhashable class from kwargs if needed
#         filtered_args = [a for a in args if a.__class__.__name__ != "UserTenantDB"]
#         filtered_kwargs = {k: v for k, v in kwargs.items() if v.__class__.__name__ != "UserTenantDB"}

#         start_time = time.perf_counter()
#         try:
#             result = func(*filtered_args, **filtered_kwargs)
#         except Exception as e:
#             end_time = time.perf_counter()
#             print(f"**{func.__name__} failed after {end_time - start_time:.6f} seconds**")
#             raise e
#         end_time = time.perf_counter()
#         print(f"**{func.__name__} executed in {end_time - start_time:.6f} seconds**")
#         return result
#     return wrapper
