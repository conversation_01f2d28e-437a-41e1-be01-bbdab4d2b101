
# from .get_db import db
# from .resolve_llm import resolve_llm

# from .track_time import time_it

# @time_it
# def sentiment_classification(data):
#     latest_message_str = data["latest_message_str"]


#     try:
#         prompt_db = db["prompt"] # variable naming bhayena
#         sentiment_classification_prompt = prompt_db.find_one({"name":"sentiment_classification"}) # k ho yo jepayetei naming?
#         prompt_ = sentiment_classification_prompt["text"]
#         model = sentiment_classification_prompt["model"]
#         llm= resolve_llm(model_name=model)
#         sentiment_prompt=prompt_.format(question=latest_message_str)
#         response = llm.complete(prompt=sentiment_prompt,formatted=True)
#         return response.text.strip(" ").split(":")[-1]
#     except Exception as e:
#         # loggers.error(f"Error classifying sentiment: {e}") # i dont see the point of this error log
#         raise e

