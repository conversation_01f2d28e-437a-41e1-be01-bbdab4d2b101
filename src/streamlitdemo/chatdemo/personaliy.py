# from .resolve_llm import resolve_llm
# from .get_db import db 

# from .track_time import time_it

# @time_it
# def generate_personality(data):
#     PROMPT_NAME = "generate_personality"    

#     latest_message_str = data["latest_message_str"]
#     summary_str = data["summary_str"]
    
#     prompt=db["prompt"].find_one({"name":PROMPT_NAME})
        
#     try:
#         llm = resolve_llm(prompt.get("model"))
#         return llm.complete(
#             prompt.get("text").format(summary_data=summary_str + f"Latest User Question: {latest_message_str}"),
#             formatted=True
#         ).text
#     except Exception as e:
#         raise e
    

    