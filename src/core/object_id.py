"""
Core module for handling ObjectId in Pydantic models.
"""
from pydantic import Field, GetCoreSchemaHandler
from pydantic_core import core_schema
from typing import Any
from bson import ObjectId

# Custom type for ObjectId with proper schema generation
class PyObjectId(ObjectId):
    """Custom type for handling MongoDB ObjectId with Pydantic."""

    @classmethod
    def __get_pydantic_core_schema__(
        cls,
        _source_type: Any,
        _handler: GetCoreSchemaHandler
    ) -> core_schema.CoreSchema:
        """
        Generate a proper schema for ObjectId.

        This method is called by Pydantic to generate the schema for this type.
        We use _source_type and _handler with underscore prefix to indicate
        they're not used but required by the interface.
        """
        return core_schema.union_schema([
            # Handle ObjectId instances directly
            core_schema.is_instance_schema(ObjectId),
            # Handle string conversion to ObjectId
            core_schema.chain_schema([
                core_schema.str_schema(),
                core_schema.no_info_plain_validator_function(cls.validate),
            ]),
        ], serialization=core_schema.plain_serializer_function_ser_schema(str))

    @classmethod
    def validate(cls, value: str) -> ObjectId:
        """Validate and convert string to ObjectId."""
        if not ObjectId.is_valid(value):
            raise ValueError(f"Invalid ObjectId: {value}")
        return ObjectId(value)

    def __str__(self) -> str:
        """Return string representation of ObjectId."""
        return str(super().__str__())

    # Make PyObjectId JSON serializable
    def __iter__(self):
        """Make PyObjectId iterable for jsonable_encoder."""
        # This is needed to make PyObjectId work with jsonable_encoder
        # We only need to yield the string representation once
        yield str(self)

    def __repr__(self) -> str:
        """Return string representation for PyObjectId."""
        return f"PyObjectId({super().__repr__()})"

    def __eq__(self, other):
        """Compare PyObjectId with other objects."""
        if isinstance(other, (str, ObjectId, PyObjectId)):
            return str(self) == str(other)
        return False

# Function to validate and convert string to ObjectId
def validate_object_id(v: Any) -> ObjectId:
    """
    Validate and convert string to ObjectId.

    Args:
        v: Value to validate

    Returns:
        ObjectId instance
    """
    if isinstance(v, str):
        if ObjectId.is_valid(v):
            return ObjectId(v)
        raise ValueError(f"Invalid ObjectId: {v}")
    elif isinstance(v, ObjectId):
        return v
    raise TypeError(f"Expected ObjectId or str, got {type(v)}")

# Helper function to create an ObjectId field with alias="_id"
def object_id_field(*, default_factory=ObjectId, **kwargs):
    """
    Create a Field with PyObjectId type and default alias="_id".

    Args:
        default_factory: Factory function for default value (default: ObjectId)
        **kwargs: Additional field parameters

    Returns:
        Field object configured for ObjectId
    """
    return Field(default_factory=default_factory, alias="_id", **kwargs)

# Create a serializable ObjectId type
ObjectIdStr = str  # For type hints where we expect a string representation of ObjectId
