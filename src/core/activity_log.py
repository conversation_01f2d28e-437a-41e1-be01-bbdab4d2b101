"""
FastAPI JWT Authentication Middleware

This middleware automatically decodes JW<PERSON> tokens from the Authorization header
and adds the decoded payload to the request state for easy access in route handlers.
"""

from typing import Optional, Dict, Any, List, Union
import jwt
from jwt.exceptions import PyJWTError
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from pymongo import AsyncMongoClient, MongoClient
from bson import ObjectId
from src.core.config import SECRET_KEY, ALGORITHM
from src.helper.logger import setup_new_logging
# No longer need activity log models
# from src.models.activity_log import ActivityLog, ActivityData
from dotenv import load_dotenv
import os
import json
from datetime import datetime
from urllib.parse import urlparse, parse_qs
load_dotenv()


# Initialize logging
loggers = setup_new_logging(__name__)

class UserActivityMiddleware(BaseHTTPMiddleware):
    """
    FastAPI middleware for JWT token processing and user activity tracking.

    This middleware:
    1. Extracts the JWT token from the Authorization header
    2. Decodes the token and validates its signature
    3. Adds the decoded payload to request.state.jwt_payload for easy access in endpoints
    4. Updates the user's last_active_at timestamp in the users collection
    5. Does NOT add entries to the activity_logs collection
    """

    def __init__(
        self,
        app: ASGIApp,
        secret_key:str =SECRET_KEY,
        algorithm: str =ALGORITHM,
        auth_header_name: str = "Authorization",
        auth_header_prefix: str = "Bearer",
        token_field_name: str = "jwt_payload",
        verify_token: bool = True,
        auto_error: bool = False,
        exclude_paths: List[str] = None,
        exclude_prefixes: List[str] = None,
    ):
        """
        Initialize the JWT middleware.

        Args:
            app: The FastAPI application
            secret_key: Secret key for JWT token validation
            algorithm: Algorithm used for JWT token (default: HS256)
            auth_header_name: Name of the header containing the token (default: Authorization)
            auth_header_prefix: Prefix before the token value, e.g. "Bearer" (default: Bearer)
            token_field_name: Name of the attribute to store the decoded token on request.state (default: jwt_payload)
            verify_token: Whether to verify the token signature (default: True)
            auto_error: Whether to raise an HTTPException for invalid tokens (default: False)
            exclude_paths: List of exact paths to exclude from JWT processing (default: None)
            exclude_prefixes: List of path prefixes to exclude from JWT processing (default: None)
        """
        super().__init__(app)
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.auth_header_name = auth_header_name
        self.auth_header_prefix = auth_header_prefix
        self.token_field_name = token_field_name
        self.verify_token = verify_token
        self.auto_error = auto_error
        self.exclude_paths = exclude_paths or []
        self.exclude_prefixes = exclude_prefixes or []

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and extract JWT token information.

        Args:
            request: The FastAPI request object
            call_next: The next middleware or endpoint handler

        Returns:
            The response from the next handler
        """
        # Check if the path should be excluded from JWT processing
        # Extract domain-related info
        host_header = request.headers.get('host', 'Unknown')
        full_url = str(request.url)
        scheme = request.url.scheme  # http or https

        # Split domain and subdomain
        domain_parts = host_header.split('.')
        if len(domain_parts) >= 2:
            domain = '.'.join(domain_parts[-2:])  # e.g., example.com
            subdomain = '.'.join(domain_parts[:-2]) if len(domain_parts) > 2 else None
        else:
            domain = host_header
            subdomain = None

        # Get client IP and port
        client_ip = request.client.host if request.client else "Unknown"
        client_port = request.client.port if request.client else "Unknown"

        # Log all these details
        # loggers.info(f"""
        # Request Info:
        # - Full URL: {full_url}
        # - Scheme: {scheme}
        # - Host: {host_header}
        # - Domain: {domain}
        # - Subdomain: {subdomain or '[None]'}
        # - Client IP: {client_ip}
        # - Client Port: {client_port}
        # """)

        

        path = request.url.path
        if path in self.exclude_paths or any(path.startswith(prefix) for prefix in self.exclude_prefixes):
            return await call_next(request)

        # Initialize with empty payload
        setattr(request.state, self.token_field_name, {})

        # Extract token from header
        token = self._extract_token(request)
        # loggers.debug(f"Extracted token: {'[PRESENT]' if token else '[MISSING]'}")
        if token:
            try:
                # Decode the token
                payload = jwt.decode(
                    token,
                    self.secret_key,
                    algorithms=[self.algorithm],
                    verify=self.verify_token
                )

                # Store the decoded payload in the request state
                setattr(request.state, self.token_field_name, payload)

            except PyJWTError as e:
                if self.auto_error:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"Invalid token: {str(e)}",
                        headers={"WWW-Authenticate": "Bearer"},
                    )
        elif self.auto_error:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization token required",
                headers={"WWW-Authenticate": "Bearer"},
            )
        # Only log activity if we have a valid payload with user_id and tenant_id
        # loggers.debug(f"JWT payload: {request.state.jwt_payload}")
        if request.state.jwt_payload:
            payload = request.state.jwt_payload
            user_id = payload.get("user_id")
            if not user_id:
                loggers.warning("User ID not found in JWT payload. Skipping activity logging.")
                return await call_next(request)
            # finding username from users collection

            tenant_id = payload.get("tenant_id")
            username = payload.get("sub")
            # email no longer needed
            # email = payload.get("email")

            # loggers.info(f"Processing request for user: {username} ({user_id}) in tenant: {tenant_id}")

            if user_id and tenant_id:
                try:
                    # Extract request details for logging
                    request_body = None
                    if request.method in ["POST", "PUT", "PATCH"]:
                        try:
                            # Clone the request to avoid consuming the body
                            body_bytes = await request.body()
                            if body_bytes:
                                try:
                                    # Try to parse as JSON
                                    request_body = json.loads(body_bytes)
                                except:
                                    # If not JSON, store as string (truncated if too large)
                                    request_body = body_bytes.decode('utf-8', errors='replace')[:500]
                                    if len(body_bytes) > 500:
                                        request_body += "... [truncated]"
                        except Exception as e:
                            loggers.warning(f"Could not extract request body: {str(e)}")

                    # No longer need query parameters
                    # query_params = dict(request.query_params)

                    # Connect to MongoDB
                    # Use MongoClient instead of AsyncMongoClient for synchronous operations
                    client = MongoClient(os.getenv("MONGO_URI"))
                    admin_db = client["eko_admin"]

                    # Get tenant database name
                    tenant_info = admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
                    if tenant_info and "database_name" in tenant_info:
                        # Connect to tenant's database
                        tenant_db_name = tenant_info["database_name"]
                        loggers.debug(f"Connected to tenant database: {tenant_db_name}")
                        tenant_db = client[tenant_db_name]

                        # No longer creating or checking activity_logs collection indexes
                        # as we're only updating the user's last active time

                        # No longer need user agent information
                        # user_agent = request.headers.get("user-agent", "Unknown")
                        # ip_address = request.client.host if request.client else "Unknown"

                        # Get current timestamp for updating last active time
                        timestamp = datetime.now()

                        # Only update user's last activity in users collection
                        tenant_db.users.update_one(
                            {"_id": ObjectId(user_id)},
                            {"$set": {"last_active_at": timestamp}},
                            upsert=False
                        )

                        # loggers.info(f"Updated last active time for user {username} ({user_id}) in tenant {tenant_id}: {request.method} {path}")

                        # User's last activity already updated above
                except Exception as e:
                    # Log the error but don't interrupt the request flow
                    loggers.error(f"Error logging activity: {str(e)}", exc_info=True)

        # Continue with the request
        return await call_next(request)

    def _extract_token(self, request: Request) -> Optional[str]:
        """
        Extract the JWT token from the request headers.

        Args:
            request: The FastAPI request object

        Returns:
            The token string if found, None otherwise
        """
        # Get the Authorization header
        auth_header = request.headers.get(self.auth_header_name)

        # Return None if no Authorization header is present
        if not auth_header:
            return None

        # Check if the header has the expected format (e.g., "Bearer token_value")
        parts = auth_header.split()

        # Verify the Authorization scheme matches the expected prefix
        if len(parts) != 2 or parts[0] != self.auth_header_prefix:
            return None

        # Return just the token part
        token = parts[1]
        return token