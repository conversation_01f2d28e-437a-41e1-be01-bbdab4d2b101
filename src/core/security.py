# src/core/security.py

from datetime import datetime, timedelta, timezone
from passlib.context import Crypt<PERSON>ontext
from fastapi.security import OAuth2<PERSON><PERSON><PERSON><PERSON>earer
from fastapi import Depends, HTTPException
import os
import httpx
import asyncio
from typing import Optional, Dict, Any, List

import jwt
from google.oauth2 import id_token
from google.auth.transport import requests
from src.core.config import SECRET_KEY, ALGORITHM
from src.models.user import UserTenantDB, User
from src.core.database import get_async_db_from_tenant_id, get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from bson import ObjectId
from src.core.database import get_admin_db
from src.helper.logger import setup_new_logging

# Configure logging
loggers = setup_new_logging(__name__)

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Use auto_error=False to prevent automatic 401 responses
# This allows us to handle authentication manually
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/login",
    auto_error=False  # Don't automatically return 401, let us handle it
)

def create_access_token(data: dict, expires_delta:timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt






async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    # Check if token is provided (since auto_error=False)
    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception

    tenant_db = get_db_from_tenant_id(payload.get("tenant_id"))
    async_tenant_db = get_async_db_from_tenant_id(payload.get("tenant_id"))
    user = tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception
    tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    slug = get_admin_db().tenants.find_one({"_id": ObjectId(payload.get("tenant_id"))})["slug"]
    user_role = user.get("role")
    user_access = tenant_db.settings.find_one({"name": "nav_access"})
    role_access = user_access.get(user_role, {}) if user_access else {}
    return UserTenantDB(
        tenant_id=payload.get("tenant_id"),
        tenant_database_name=tenant_database_name, 
        slug=slug,
        db=tenant_db, 
        user=User(**user),
        async_db=async_tenant_db, 
        access=role_access
    )

def require_user(users: list):
    async def check_username(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_name = user_tenant_info.user.username
        if user_name not in users:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_username

def min_role(min_role: str):
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.user.role
        ROLES = user_tenant_info.db.settings.find_one({"name":"role_hierarchy"})['roles']
        if ROLES[user_role] < ROLES[min_role]:
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_role

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)


def hash_password(password: str) -> str:
    """Hash a password using bcrypt"""
    return pwd_context.hash(password)


async def verify_google_token(id_token_str: str) -> Dict[str, Any]:
    """
    Verify Google ID token and return user information.

    Args:
        id_token_str: Google ID token string

    Returns:
        Dictionary with user information from Google

    Raises:
        HTTPException: If token verification fails
    """
    try:
        # Get Google client ID from environment
        google_client_id = os.getenv("GOOGLE_CLIENT_ID")
        if not google_client_id:
            raise HTTPException(
                status_code=500,
                detail="Google OAuth not configured - missing GOOGLE_CLIENT_ID"
            )

        # Verify the token
        idinfo = id_token.verify_oauth2_token(
            id_token_str,
            requests.Request(),
            google_client_id
        )

        # Extract user information
        user_info = {
            "google_id": idinfo.get("sub"),
            "email": idinfo.get("email"),
            "name": idinfo.get("name"),
            "picture": idinfo.get("picture"),
            "email_verified": idinfo.get("email_verified", False)
        }

        loggers.info(f"Google token verified for user: {user_info.get('email')}")
        return user_info

    except ValueError as e:
        loggers.error(f"Google token verification failed: {str(e)}")
        raise HTTPException(
            status_code=401,
            detail=f"Invalid Google token: {str(e)}"
        )
    except Exception as e:
        loggers.error(f"Error verifying Google token: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Error verifying Google token"
        )

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, slug:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id, "slug": slug}
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> Dict[str, Any]:
    """
    Decode a JWT token and return the payload.

    Args:
        token: The JWT token to decode

    Returns:
        The decoded token payload or empty dict if invalid

    Raises:
        jwt.PyJWTError: If the token is invalid
    """
    try:
        # First try to decode with verification
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        loggers.warning(f"Token expired: {token[:10]}...")
        return {}
    except jwt.InvalidTokenError as e:
        loggers.warning(f"Invalid token: {token[:10]}... Error: {str(e)}")
        return {}
    except Exception as e:
        loggers.error(f"Unexpected error decoding token: {str(e)}")
        return {}


def require_roles(allowed_roles: List[str]):
    """
    Dependency factory for role-based access control.

    Args:
        allowed_roles: List of roles that are allowed to access the endpoint

    Returns:
        Dependency function that checks user role
    """
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.user.role
        if user_role not in allowed_roles:
            raise HTTPException(
                status_code=403,
                detail=f"Access denied. Required roles: {allowed_roles}, your role: {user_role}"
            )
        return user_tenant_info
    return check_role

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        tenant_id: str = payload.get("tenant_id")
        role: str = payload.get("role")
        invited_by: str = payload.get("invited_by")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or tenant_id is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        return username, tenant_id,invited_by, role

    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


def verify_password(plain_password, hashed_password):
    """
    Verifies a plain password against a hashed password.
    """
    return pwd_context.verify(plain_password, hashed_password)


