"""
Utility functions for working with activity logs.
"""
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from bson import ObjectId
from pymongo.database import Database

from src.models.activity_log import ActivityLog, ActivityData, ActivityLogCreate
from src.helper.logger import setup_new_logging

# Initialize logging
loggers = setup_new_logging(__name__)

async def log_user_activity(
    db: Database,
    user_id: str,
    username: Optional[str],
    email: Optional[str],
    tenant_id: str,
    path: str,
    method: str,
    query_params: Optional[Dict[str, Any]] = None,
    request_body: Optional[Any] = None,
    user_agent: Optional[str] = None,
    ip_address: Optional[str] = None
) -> str:
    """
    Log user activity to the database.

    Args:
        db: MongoDB database instance
        user_id: ID of the user
        username: Username of the user
        email: Email of the user
        tenant_id: ID of the tenant
        path: Request path
        method: HTTP method
        query_params: Query parameters
        request_body: Request body
        user_agent: User agent string
        ip_address: IP address

    Returns:
        ID of the created or updated activity log
    """
    try:
        # Create activity data
        timestamp = datetime.now()
        activity_data = ActivityData(
            path=path,
            method=method,
            query_params=query_params or {},
            request_body=request_body,
            user_agent=user_agent,
            ip_address=ip_address,
            created_at=timestamp
        )

        # Check if user already has an activity log
        existing_log = await db.activity_logs.find_one({"user_id": user_id})

        if existing_log:
            # Update existing log
            result = await db.activity_logs.update_one(
                {"user_id": user_id},
                {
                    "$set": {"last_active_at": timestamp, "timestamp": timestamp},
                    "$push": {"data": activity_data.model_dump()}
                }
            )

            # Update user's last activity in users collection
            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"last_active_at": timestamp}},
                upsert=False
            )

            loggers.debug(f"Updated existing activity log for user {username}")
            return str(existing_log["_id"])
        else:
            # Create new activity log
            activity_log = ActivityLog(
                user_id=user_id,
                username=username,
                email=email,
                tenant_id=tenant_id,
                created_at=timestamp,
                last_active_at=timestamp,
                data=[activity_data]
            )

            # Add timestamp field for TTL index
            activity_log_dict = activity_log.model_dump(by_alias=True)
            activity_log_dict["timestamp"] = timestamp  # For TTL index

            # Log the activity
            result = await db.activity_logs.insert_one(activity_log_dict)
            loggers.info(f"Created new activity log for user {username}")

            # Update user's last activity in users collection
            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$set": {"last_active_at": timestamp}},
                upsert=False
            )

            return str(result.inserted_id)
    except Exception as e:
        loggers.error(f"Error logging activity: {str(e)}", exc_info=True)
        return None

async def get_user_activity(
    db: Database,
    user_id: str,
    limit: int = 100,
    skip: int = 0
) -> Optional[ActivityLog]:
    """
    Get user activity from the database.

    Args:
        db: MongoDB database instance
        user_id: ID of the user
        limit: Maximum number of activity data entries to return
        skip: Number of activity data entries to skip

    Returns:
        ActivityLog object or None if not found
    """
    try:
        # Get activity log
        activity_log = await db.activity_logs.find_one({"user_id": user_id})

        if activity_log:
            # Convert to ActivityLog model
            result = ActivityLog(**activity_log)

            # Limit and skip data entries
            if limit or skip:
                result.data = result.data[skip:skip+limit]

            return result
        else:
            return None
    except Exception as e:
        loggers.error(f"Error getting user activity: {str(e)}", exc_info=True)
        return None

async def cleanup_old_activity_logs(db: Database, days: int = 30) -> int:
    """
    Clean up old activity logs.

    Args:
        db: MongoDB database instance
        days: Number of days to keep activity logs

    Returns:
        Number of deleted activity logs
    """
    try:
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=days)

        # Delete old activity logs
        result = await db.activity_logs.delete_many({"created_at": {"$lt": cutoff_date}})

        loggers.info(f"Deleted {result.deleted_count} old activity logs")
        return result.deleted_count
    except Exception as e:
        loggers.error(f"Error cleaning up old activity logs: {str(e)}", exc_info=True)
        return 0

async def get_active_users(db: Database, hours: int = 24) -> List[Dict[str, Any]]:
    """
    Get users who have been active within the specified time period.

    Args:
        db: MongoDB database instance
        hours: Number of hours to look back

    Returns:
        List of user documents with their last active time
    """
    try:
        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=1)

        # Find users who have been active since the cutoff date
        cursor = db.users.find(
            {"last_active_at": {"$gte": cutoff_date},"role": "agent"},
            {"_id": 1, "username": 1, "last_active_at": 1}
        )

        # Convert cursor to list
        users = await cursor.to_list(length=1000)

        # Convert ObjectId to string
        for user in users:
            user["_id"] = str(user["_id"])

        return users
    except Exception as e:
        loggers.error(f"Error getting active users: {str(e)}", exc_info=True)
        return []
