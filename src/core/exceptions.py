"""
Exception handlers for the FastAPI application.
"""

import json
from fastapi import Request
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from typing import Union
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY
from src.helper.logger import setup_new_logging

# Initialize logger
logger = setup_new_logging(__name__)

async def http422_error_handler(
    _: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    """
    Handle validation errors and return a standardized JSON response.
    """
    logger.error(f"Validation error: {json.dumps(exc.errors())}")
    return JSONResponse(
        {"errors": exc.errors()}, status_code=HTTP_422_UNPROCESSABLE_ENTITY
    )

async def request_validation_exception_handler(_: Request, exc: RequestValidationError):
    """
    Handle request validation errors with more readable error messages.
    """
    return JSONResponse(
        status_code=422,
        content={"detail": [{"msg": str(err["msg"]), "type": err["type"], "loc": err["loc"]} for err in exc.errors()]},
    )

def add_exception_handlers(app):
    """
    Add exception handlers to the FastAPI application.
    """
    app.add_exception_handler(ValidationError, http422_error_handler)
    app.add_exception_handler(RequestValidationError, request_validation_exception_handler)
