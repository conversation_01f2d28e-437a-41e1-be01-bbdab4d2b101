"""
Authentication Service for Echo Bot.
Handles user authentication, registration, and role management.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.responses import RedirectResponse
from src.helper.logger import setup_new_logging

# Import and include routers
from src.auth.routes.users import router as users_router

# Configure logging
loggers = setup_new_logging(__name__)

# Create FastAPI instance
app: FastAPI = FastAPI(
    title="Echo Bot - Auth Service",
    description="Authentication and user management service for Echo Bot",
    version="1.0.0",
    docs_url=None,  # Custom docs endpoint
    redoc_url=None,  # Custom redoc endpoint
    swagger_ui_oauth2_redirect_url="/auth/docs/oauth2-redirect",
    openapi_url="/openapi.json",  # Relative path - service internal
    servers=[
        {
            "url": "/auth",
            "description": "Auth Service API"
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(users_router, tags=["Users"])


# Custom OpenAPI schema generation to ensure correct server URLs
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Ensure the server URL is correctly set
    openapi_schema["servers"] = [
        {
            "url": "/auth",
            "description": "Auth Service API"
        }
    ]

    # Fix OAuth2 security scheme for Swagger UI
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}
    if "securitySchemes" not in openapi_schema["components"]:
        openapi_schema["components"]["securitySchemes"] = {}

    # Update the OAuth2PasswordBearer scheme to work with Swagger UI
    openapi_schema["components"]["securitySchemes"]["OAuth2PasswordBearer"] = {
        "type": "oauth2",
        "flows": {
            "password": {
                "tokenUrl": "/login",
                "scopes": {}
            }
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


@app.get("/health")
async def health_check():
    """Minimal health check endpoint"""
    return {"status": "healthy"}


@app.get("/")
async def root():
    """Redirect to API documentation"""
    return RedirectResponse(url="/docs")

@app.get("/docs")
async def docs():
    """Custom Swagger UI documentation"""
    return get_swagger_ui_html(
        openapi_url="/auth/openapi.json",  # Full external path for OpenAPI schema
        title=app.title + " - Documentation",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
        swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
        oauth2_redirect_url="/auth/docs/oauth2-redirect",
        init_oauth=None,
        swagger_ui_parameters=None,
    )


@app.get("/docs/oauth2-redirect", include_in_schema=False)
async def swagger_ui_redirect():
    """OAuth2 redirect endpoint for Swagger UI authentication."""
    from fastapi.openapi.docs import get_swagger_ui_oauth2_redirect_html
    return get_swagger_ui_oauth2_redirect_html()
