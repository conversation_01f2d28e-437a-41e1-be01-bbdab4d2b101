from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.models.chat_hist import (
    ModelRunRequest,
    ModelRunResponse

)

loggers=setup_new_logging(__name__)


# hook for receving message from sociar to receive all message throught here whatsapp, instagram , facebook etc any method from sociar

async def receive_message(request: ModelRunRequest, current_user: UserTenantDB):

    loggers.info(f"Recived request: \n{request}")



    loggers.info("Request processed successfully")
    return {"status": "success", "message": "Message received"}