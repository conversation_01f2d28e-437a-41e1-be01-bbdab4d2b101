"""
Shared models for social media webhook APIs.

This module contains Pydantic models used across Facebook, Instagram, and WhatsApp integrations.
"""

from typing import Dict, Any, List, Optional
from pydantic import BaseModel



class MediaItem(BaseModel):
    name: str
    url: str

class SocialMediaMessageRequest(BaseModel):
    user_id: str
    content: str
    channel: str
    media_url: Optional[List[MediaItem]] = None



class WhatsAppTemplate(BaseModel):
    """Model for WhatsApp message template."""
    name: str
    language: str = "en_US"
    header_type: str = "TEXT"
    template_type: str = "1"
    body_text: str
    header_text: Optional[str] = None
    footer_text: Optional[str] = None
    smm_profile_id: str = "2"

    model_config = {
        "json_schema_extra": {
            "examples": [
                # Example 1: Template with variables
                {
                    "name": "WelcomeMessage2",
                    "language": "en_US",
                    "header_type": "TEXT",
                    "template_type": "1",
                    "body_text": "Hello , {{CUSTOMER_NAME}} Welcome to our service! We're excited to have you on board.\nOur team is here to help you with any questions you might have.",
                    "header_text": "Welcome Greetings",
                    "footer_text": "Need help? Contact our support team.",
                    "smm_profile_id": "2"
                },
                # Example 2: Template without variables
                {
                    "name": "WelcomeMessage1`",
                    "language": "en_US",
                    "header_type": "TEXT",
                    "template_type": "1",
                    "body_text": "Welcome to our service! We're excited to have you on board.\nOur team is here to help you with any questions you might have.",
                    "header_text": "Welcome",
                    "footer_text": "Your trusted partner in healthcare.",
                    "smm_profile_id": "2"
                }
            ]
        }
    }


class WhatsAppMessageRequest(BaseModel):
    """Model for WhatsApp message requests."""
    profile_id: int
    template_id: int
    to_number: str
    variables: Optional[Dict[str, str]] = None


class SocialMediaResponse(BaseModel):
    """Response model for social media operations."""
    success: bool
    status_code: int = 200
    data: Optional[Dict[str, Any]] = None
    message: str
    error_details: Optional[str] = None
