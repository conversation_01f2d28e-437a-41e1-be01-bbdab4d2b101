from fastapi import APIRouter, Request
import urllib.parse

from src.core.database import get_db_from_tenant_id
from .twilio_req_body import TwilioRequest
from src.helper.logger import setup_new_logging
import requests

# Reply generation
from src.routes.reply import (
    generate_reply,
    get_tenant_info,
    ModelRunRequest,
    BackgroundTasks,
)
from twilio.rest import Client
from src.reply.minio_client import MinIOClient, MinIOConfig
import hashlib
import re
from datetime import datetime
from bson import ObjectId

logger = setup_new_logging(__name__)


router = APIRouter(tags=["Twilio WebHooks"], prefix="/whatsapp_webhook")


@router.post("/{tenant_id}/", include_in_schema=True)
async def webhook(tenant_id: str, request: Request):
    try:
        logger.info(f"Processing webhook for tenant: {tenant_id}")
        body = await request.body()
        logger.info(f"Body: {body}")
        decoded_body = urllib.parse.parse_qs(body.decode())
        cleaned_data = {key: value[0] for key, value in decoded_body.items()}
        logger.debug(f"Cleaned request data: {cleaned_data}")
        tw_req = TwilioRequest(**cleaned_data)
        logger.info(f"Received WhatsApp message from: {tw_req.From} to: {tw_req.To}")
        # request
        # return "Hello sir"
        try:
            import phonenumbers

            try:
                number = phonenumbers.parse(tw_req.From, None)
                country_code = "+" + str(number.country_code)
                national_number = str(number.national_number)
            except Exception as e:
                logger.error(f"Error parsing phone number: {e}")
                number = None
                country_code = None
                national_number = None
            db = get_db_from_tenant_id(tenant_id)
            whatsapp_env = db.settings.find_one({"name": "whatsapp_env"})
            if not whatsapp_env:
                logger.error("WhatsApp environment settings not found")
                return "WhatsApp environment settings not found", 500

            WHATSAPP_EKO_TOKEN = whatsapp_env.get("WHATSAPP_EKO_BOT_ACESS_TOKEN")
            ACCOUNT_SID = whatsapp_env.get("ACCOUNT_SID")
            AUTH_TOKEN = whatsapp_env.get("AUTH_TOKEN")

            # Get the current user from the token
            current_user = await get_tenant_info(WHATSAPP_EKO_TOKEN)
            user = db.customers.find_one({"customer_id": tw_req.WaId})
            if user is None:
                # Insert new user and get the inserted document
                insert_result = db.customers.insert_one(
                    {
                        "customer_id": tw_req.WaId,
                        "customer_name": tw_req.ProfileName,
                        "whatsapp_number": tw_req.From,
                        "whatsapp_id": tw_req.WaId,
                        "country_code": country_code,
                        "phone_number": national_number,
                        "channel": "Whatsapp",
                        "created_at": datetime.now(),
                        "last_active_at": datetime.now(),
                    }
                )
                # Get the newly created user document
                user = db.customers.find_one({"_id": insert_result.inserted_id})
                logger.info(f"New user created with ID: {insert_result.inserted_id}")
            else:
                # Store the user ID before updating
                user_id = user["_id"]
                # Update the last active time
                db.customers.update_one(
                    {
                        "_id": user_id,
                        "customer_id": tw_req.WaId,
                        "name": tw_req.ProfileName,
                    },
                    {"$set": {"last_active_at": datetime.now()}},
                )
                # Fetch the updated user document
                user = db.customers.find_one({"_id": user_id})
            # current_user = UserTenantDB(tenant_id=tenant_id, db=db, user=None,async_db=async_tenant_db)

            # VERIFY_TOKEN = whatsapp_env.get("VERIFY_TOKEN")
            # TOKEN = whatsapp_env.get("TOKEN")
            # PHONE_NUMBER_ID = whatsapp_env.get("PHONE_NUMBER_ID")
            minio_config = db.settings.find_one({"name": "env"}).get("minio_config")
            minio = MinIOClient(
                config=MinIOConfig(
                    access_key=minio_config.get("access_key"),
                    secret_key=minio_config.get("secret_key"),
                    minio_url=minio_config.get("minio_url"),
                    bucket_name=minio_config.get("bucket_name"),
                )
            )
        except Exception as e:
            logger.error(f"Error getting whatsapp env from db: {e}")
            return "Error getting tenant" + str(e), 500
        reply_urls = []
        try:
            response = None
            if tw_req.MessageType == "text" or tw_req.MessageType == "image":
                # Call the reply_generate function
                # current_user =

                chat_data = {
                    "role": "user",
                    "data": {"content": tw_req.Body, "created_at": None},
                }
                if tw_req.MediaUrl0:
                    response = requests.get(
                        tw_req.MediaUrl0, auth=(ACCOUNT_SID, AUTH_TOKEN)
                    )

                    obj_name = minio.upload_bytes(
                        object_name=hashlib.sha256(
                            tw_req.MediaUrl0.encode()
                        ).hexdigest(),
                        file_bytes=response.content,
                    )
                    tw_req.MediaUrl0 = minio.get_presigned_url(object_name=obj_name)
                    logger.info(f"Media url: {tw_req.MediaUrl0}")

                    chat_data["data"]["media_ids"] = [tw_req.MediaUrl0]
                response, response_id = await generate_reply(
                    ModelRunRequest(
                        **{
                            "chat_data": [chat_data],
                            "chat_data_format": "role_data",
                            "media_ids": ["string"],
                            "media_values": "string",
                            "message": "string",
                            "primary_product": "string",
                            "primary_product_code": "string",
                            "tags": ["Whatsapp"],
                            "user_id": tw_req.WaId,
                            "user_name": tw_req.ProfileName,
                            "channel": "Whatsapp",
                            "mode": "elaborated",
                        }
                    ),
                    BackgroundTasks(),
                    current_user,
                )
                response_id = ObjectId(response_id)
                
            logger.critical(f"response reply: {response['response']['reply']}")
            # Get the reply text and any URLs directly from the response
            reply_ = response["response"]["reply"]
            logger.info(f"reply from response: {reply_}")
            reply_urls = response["response"]["reply_urls"]

            # Log what we found in the response
            logger.info(f"Response structure keys: {list(response.keys())}")
            logger.info(f"Reply URLs from response: {reply_urls}")

            # IMPORTANT: When URLs are found in WhatsApp integration, make a list of set and return them immediately
            # rather than processing them in the background (as per user's preference)
            if (
                "source_nodes" in response["response"]
                and response["response"]["source_nodes"]
            ):
                # Extract URLs from source nodes if they exist
                extracted_urls = []
                for node in response["response"]["source_nodes"]:
                    # Check if the node has resource_url
                    if "metadata" in node and "resource_url" in node["metadata"]:
                        resource_url = node["metadata"]["resource_url"]
                        if isinstance(resource_url, list):
                            for url in resource_url:
                                if isinstance(url, dict) and "url" in url:
                                    extracted_urls.append(url["url"])
                                elif isinstance(url, str):
                                    extracted_urls.append(url)
                        elif isinstance(resource_url, str):
                            extracted_urls.append(resource_url)

                # If we found URLs, add them to reply_urls
                if extracted_urls:
                    reply_urls.extend(extracted_urls)
                    logger.info(
                        f"Extracted {len(extracted_urls)} URLs from source nodes"
                    )

            # Remove duplicates
            reply_urls = list(set(reply_urls))
            try:
                current_user.db.ai_response.update_one(
                    {"_id": ObjectId(response_id)},
                    {
                        "$set": {
                            "response.usage.twilio_cost": {
                                "images": len(reply_urls),
                                "text": 1 if reply_ else 0,
                            }
                        }
                    },
                )
            except Exception as e:
                logger.error(f"Error updating ai_response: {e}")
            logger.info(f"Final reply URLs: {reply_urls}")
        except Exception as e:
            logger.error(f"Error generating reply: {e}")
            reply_ = "Error"

        # test sending msg to user

        client = Client(ACCOUNT_SID, AUTH_TOKEN)

        to = tw_req.From
        from_ = tw_req.To
        try:
            if not from_.startswith("whatsapp:"):
                from_ = "whatsapp:" + from_

        except Exception as e:
            logger.error(f"Error parsing phone number: {e}")

        send_message = {
            "from_": await format_phone_number(from_),
            "body": response.get("response", {}).get("reply", "Thank you for contacting us. We will get back to you soon."),
            "to": await format_phone_number(to),
            "force_delivery": True,
            # "content_sid": CONTENT_SID,
            # "content_variables":{
            #     "1": reply_
            # },
        }
        clean_parts, reply_urls = await sanitize_reply(reply_, reply_urls)
        logger.info(f"Message parts to send: {len(clean_parts)}")
        logger.info(f"Media URLs to send: {len(reply_urls)}")

        # Send text messages
        for chunk in clean_parts:
            if not chunk.strip():  # Skip empty chunks
                logger.warning("Skipping empty message chunk")
                continue
                
            send_message["body"] = chunk.strip()
            try:
                message = client.messages.create(**send_message)
                logger.info(f"Text message sent successfully - SID: {message.sid}")
            except Exception as e:
                logger.error(f"Failed to send text message chunk: {str(e)}")
                logger.error(f"Message details: {send_message}")

        # If no chunks were sent and we have a reply, send the full reply
        if len(clean_parts) == 0 and reply_:
            logger.info("No message chunks, sending full reply")
            send_message["body"] = reply_
            try:
                message = client.messages.create(**send_message)
                logger.info(f"Full reply message sent successfully - SID: {message.sid}")
            except Exception as e:
                logger.error(f"Failed to send full reply message: {str(e)}")
                logger.error(f"Message details: {send_message}")

        # Send media messages
        if reply_urls:
            logger.info(f"Processing {len(reply_urls)} media URLs")
            for url in reply_urls:
                media_message = {
                    "from_": await format_phone_number(tw_req.To),
                    "to": await format_phone_number(tw_req.From),
                }

                if "youtube.com" in url or "youtu.be" in url:
                    logger.info(f"Sending YouTube URL as text: {url}")
                    media_message["body"] = url
                else:
                    logger.info(f"Sending media URL: {url}")
                    media_message["media_url"] = url

                try:
                    message = client.messages.create(**media_message)
                    logger.info(f"Media message sent successfully - SID: {message.sid}")
                except Exception as media_error:
                    logger.error(f"Failed to send media message: {str(media_error)}")
                    logger.error(f"Media message details: {media_message}")

        return "ok"

    except Exception as e:
        import traceback

        traceback.print_exc()
        logger.error(f"Error in webhook: {e}")
        return "Error in webhook" + str(e), 500


async def format_phone_number(phone_number):
    try:
        if not phone_number.startswith("whatsapp:"):
            phone_number = "whatsapp:" + phone_number
        return phone_number
    except Exception as e:
        logger.error(f"Error formatting phone number: {e}")
        return phone_number


async def sanitize_reply(reply_: str, reply_urls: list) -> tuple[list, list]:
    """
    Sanitize and split reply text into parts, handling URLs appropriately.
    
    Args:
        reply_: The reply text to sanitize
        reply_urls: List of URLs to process
        
    Returns:
        tuple: (list of sanitized text parts, list of media URLs)
    """
    logger.info("Starting reply sanitization")
    
    if not reply_ and not reply_urls:
        logger.warning("Both reply text and URLs are empty")
        return [], []
        
    # Separate YouTube URLs from other media URLs
    youtube_urls = [url for url in reply_urls if "youtube.com" in url or "youtu.be" in url]
    media_urls = [url for url in reply_urls if "youtube.com" not in url and "youtu.be" not in url]
    
    logger.info(f"Found {len(youtube_urls)} YouTube URLs and {len(media_urls)} media URLs")
    
    # Process the reply text
    if reply_:
        # Remove special characters that might cause issues
        sanitized = re.sub(r"[&*]", "", reply_)
        
        # Add YouTube URLs to the text
        if youtube_urls:
            sanitized += "\n\n" + "\n".join(youtube_urls)
            logger.info("Added YouTube URLs to reply text")
        
        # Split into parts by multiple newlines
        parts = re.split(r"\n{2,}", sanitized)
        clean_parts = [part.strip() for part in parts if part.strip()]
        
        logger.info(f"Split reply into {len(clean_parts)} parts")
    else:
        clean_parts = []
        logger.info("No reply text to process")
    
    # If no parts were created but we have a reply, use the whole reply
    if not clean_parts and reply_:
        clean_parts = [reply_]
        logger.info("Using full reply as single part")
    
    return clean_parts, media_urls
