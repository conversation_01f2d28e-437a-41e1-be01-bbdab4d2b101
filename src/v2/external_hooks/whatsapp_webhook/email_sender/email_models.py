"""
Models for email notification configuration.
"""
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import List, Optional, Literal
from bson import ObjectId
from src.v2.dashboard.cta.models import CTAType
from src.core.object_id import PyObjectId, object_id_field
class CTATypeConfig(BaseModel):
    """Model for a CTA type configuration."""
    id: Optional[PyObjectId] = object_id_field()
    name: CTAType  # Using the CTAType enum
    enable: bool = True

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "name": "ticket",
                "enable": True
            }
        }
    )

class EmailRecipient(BaseModel):
    """Model for an email recipient with per-recipient CTA type configuration."""
    id: Optional[PyObjectId] = object_id_field()
    email: EmailStr  # Only email is required
    enable: Optional[bool] = False  # Default to disabled
    name: Optional[str] = None
    cta_types: Optional[List[CTATypeConfig]] = None  # Per-recipient CTA configuration

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "enable": True,
                "name": "User Name",
                "cta_types": [
                    {"name": "ticket", "enable": True},
                    {"name": "booking", "enable": False}
                ]
            }
        }
    )

class EmailNotificationConfig(BaseModel):
    """Model for email notification configuration."""
    id: Optional[PyObjectId] = object_id_field()
    name: str = "email_notifications"
    emails: List[EmailRecipient] = []
    cta_types: List[CTATypeConfig] = []  # List of CTA types with enable/disable configuration
    enabled: bool = False  # Master switch to enable/disable all email notifications

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        populate_by_name=True,
        json_schema_extra={
            "examples": [
                {
                    "emails": [
                        {"email": "<EMAIL>", "enable": True, "name": "User 1"},
                    ],
                    "cta_types": [
                        {"name": "ticket", "enable": True},
                        {"name": "booking", "enable": True}
                    ],
                    "enabled": True
                }
            ]
        }
    )

class EmailStatus(BaseModel):
    """Model for email notification status."""
    status: Literal["enabled", "disabled"]

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "enabled"
            }
        }
    )

class EmailRecipientStatus(BaseModel):
    """Model for email recipient status."""
    status: Literal["enabled", "disabled"]

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "status": "enabled"
            }
        }
    )

class EmailRequest(BaseModel):
    """Model for sending an email."""
    TO: str
    FULL_NAME: str="Not Available"
    EMAIL: Optional[str]="Not Available"
    PHONE_NUMBER: Optional[str]="Not Available"
    CHANNEL: str
    DESCRIPTION: str
    ASSIGNED_TO: Optional[str]="Not Available"
    type: Optional[str] = None  # CTA type (ticket, booking, etc.)
    SUBJECT: Optional[str] = None  # Will be generated based on type if not provided

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [
                {
                    "TO": "<EMAIL>",
                    "FULL_NAME": "test name",
                    "EMAIL": "<EMAIL>",
                    "PHONE_NUMBER": "9841234567",
                    "CHANNEL": "whatsapp",
                    "DESCRIPTION": "test description",
                    "ASSIGNED_TO": "Agent Name",
                    "type": "ticket"
                }
            ]
        }
    )
