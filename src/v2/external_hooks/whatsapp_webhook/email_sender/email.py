"""
Email notification endpoints for the WhatsApp webhook.
"""
import json
import requests
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException
from fastapi.responses import J<PERSON><PERSON>esponse
from bson import ObjectId
from datetime import datetime

from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.whatsapp_webhook.email_sender.template import html_
from src.v2.external_hooks.whatsapp_webhook.email_sender.email_models import (
    EmailRecipient,
    CTATypeConfig,
    EmailNotificationConfig,
    EmailRequest,
    EmailStatus,
    EmailRecipientStatus
)
from src.v2.external_hooks.whatsapp_webhook.email_sender.email_service import EmailConfigService
from src.v2.external_hooks.whatsapp_webhook.email_sender.send_ import send_cta_email_notification

loggers = setup_new_logging(__name__)
router = APIRouter()

# Custom JSON encoder to handle ObjectId and datetime serialization
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

# Function to recursively convert ObjectId to string in nested dictionaries
def convert_objectid_to_str(data):
    if isinstance(data, ObjectId):
        return str(data)
    elif isinstance(data, dict):
        return {k: convert_objectid_to_str(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_objectid_to_str(item) for item in data]
    elif isinstance(data, datetime):
        return data.isoformat()
    return data

from src.v2.dashboard.cta.models import CTA


@router.get("/email_service")
async def get_email_service_status(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get the status of the email service.

    Args:
        current_user: Current user with database access

    Returns:
        JSON response with success status
    """
    email_setting=  current_user.db.settings.find_one({"name": "email_notifications"}).get("enabled")
    return {"status": "success", "email_service_status": email_setting}


@router.put("/email_service")
async def update_email_service_status(
    status: bool,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update the status of the email service.

    Args:
        status: The new status
        current_user: Current user with database access

    Returns:
        JSON response with success status
    """
    try:
        # update
        uupdate=current_user.db.settings.update_one({"name": "email_notifications"}, {"$set": {"enabled": status}})
       
        return JSONResponse(content="Email service status updated successfully", status_code=200)
    except Exception as e:
        loggers.error(f"Error updating email service status: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.post("/send_email")
async def send_email_notification(
    request: CTA,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Testing to send an email notification.

    Args:
        request: Email request details
        current_user: Current user with database access

    Returns:
        Response from the email service
    """

    # Build CTA TEsting
    Dummy_cta_dict =request.formatted_email_context
    return await send_cta_email_notification(Dummy_cta_dict, current_user)

# @router.post("/configure_email_notifications")
# async def configure_email_notifications(
#     config: EmailNotificationConfig,
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ):
#     """
#     Configure email notification settings for CTA events.

#     Args:
#         config: Email notification configuration
#         current_user: Current user with database access

#     Returns:
#         JSON response with success status
#     """
#     email_service = EmailConfigService(current_user)
#     return await email_service.update_email_config(config)



@router.get("/email_config")
async def get_email_config(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get the email configuration as a JSON object for frontend display.

    Args:
        current_user: Current user with database access

    Returns:
        Email configuration as JSON
    """
    try:
        email_service = EmailConfigService(current_user)
        config = await email_service.get_email_config()

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_config = convert_objectid_to_str(config)

        # Return a JSONResponse with the sanitized config
        return JSONResponse(content=sanitized_config)
    except Exception as e:
        loggers.error(f"Error getting email config: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.get("/email_status/{email_id}", status_code=200)
async def get_email_status(email_id: str, current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get the current status of a specific email recipient (enabled/disabled).

    Args:
        email_id: The ID of the email recipient
        current_user: Current user with database access

    Returns:
        Email recipient status
    """
    try:
        email_service = EmailConfigService(current_user)
        result = await email_service.get_email_status(email_id)

        # Set the appropriate status code
        status_code = result.get("status_code", 200)
        if not result.get("success", True):
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", "Email status retrieved successfully"),
                "data": sanitized_result.get("data", {"enable": False})
            },
            status_code=status_code
        )
    except Exception as e:
        loggers.error(f"Error getting email status: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.post("/email_recipient", status_code=201)
async def add_email_recipient(
    email: EmailRecipient,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Add a new email recipient to the notification list.

    Args:
        email: The email recipient to add
        current_user: Current user with database access

    Returns:
        Success status and message with the added email
    """
    try:
        email_service = EmailConfigService(current_user)
        result = await email_service.add_email_recipient(email)

        # Set the appropriate status code
        status_code = result.get("status_code", 201)
        if not result.get("success", False):
            # For 409 Conflict, return the data along with the error
            if status_code == 409 and "data" in result:
                # Use our custom function to recursively convert all ObjectId instances to strings
                sanitized_data = convert_objectid_to_str(result)

                # Return a JSONResponse with the sanitized data
                return JSONResponse(
                    content={
                        "success": False,
                        "message": sanitized_data.get("message", "Email already exists"),
                        "data": sanitized_data.get("data", {})
                    },
                    status_code=status_code
                )
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", "Email recipient added successfully"),
                "data": sanitized_result.get("data", {})
            },
            status_code=status_code
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        loggers.error(f"Error adding email recipient: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.delete("/email_recipient/{email_id}", status_code=200)
async def delete_email_recipient(
    email_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Delete an email recipient from the notification list.

    Args:
        email_id: The ID of the email recipient to delete
        current_user: Current user with database access

    Returns:
        Success status and message
    """
    try:
        email_service = EmailConfigService(current_user)
        result = await email_service.delete_email_recipient(email_id)

        # Set the appropriate status code
        status_code = result.get("status_code", 200)
        if not result.get("success", False):
            # For 404 Not Found, we'll still raise an exception but with the proper status code
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", "Email recipient deleted successfully"),
                "data": sanitized_result.get("data", {})
            },
            status_code=status_code
        )
    except Exception as e:
        loggers.error(f"Error deleting email recipient: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.put("/email_status", status_code=200)
async def update_email_status(
    status: EmailStatus,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update the email notification status (enable/disable).

    Args:
        status: The new status
        current_user: Current user with database access

    Returns:
        Success status and message
    """
    try:
        email_service = EmailConfigService(current_user)
        result = await email_service.update_email_status(status.status)

        # Set the appropriate status code
        status_code = result.get("status_code", 200)
        if not result.get("success", False):
            # For 409 Conflict, return the data along with the error
            if status_code == 409 and "data" in result:
                # Use our custom function to recursively convert all ObjectId instances to strings
                sanitized_data = convert_objectid_to_str(result)

                # Return a JSONResponse with the sanitized data
                return JSONResponse(
                    content={
                        "success": False,
                        "message": sanitized_data.get("message", "Status already set"),
                        "data": sanitized_data.get("data", {})
                    },
                    status_code=status_code
                )
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", f"Email notifications {status.status}"),
                "data": sanitized_result.get("data", {"status": status.status})
            },
            status_code=status_code
        )
    except Exception as e:
        loggers.error(f"Error updating email status: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

# @router.put("/cta_type", status_code=200)
# async def update_cta_type(
#     cta_type: CTATypeConfig,
#     current_user: UserTenantDB = Depends(get_tenant_info)
# ):
#     """
#     Update a CTA type configuration (enable/disable) at the global level.
#     This is deprecated in favor of per-email CTA type configuration.

#     Args:
#         cta_type: The CTA type configuration to update
#         current_user: Current user with database access

#     Returns:
#         Success status and message
#     """
#     email_service = EmailConfigService(current_user)
#     result = await email_service.update_cta_type(cta_type)

#     # Set the appropriate status code
#     status_code = result.get("status_code", 200)
#     if not result.get("success", False):
#         # For 409 Conflict, return the data along with the error
#         if status_code == 409 and "data" in result:
#             return {
#                 "success": False,
#                 "message": result.get("message", "CTA type already in requested state"),
#                 "data": result.get("data")
#             }
#         raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

#     return {
#         "success": True,
#         "message": result.get("message", f"CTA type '{cta_type.name}' updated successfully"),
#         "data": result.get("data", None)
#     }



@router.put("/email_recipient/{email_id}/status", status_code=200)
async def update_email_recipient_status(
    email_id: str,
    status: EmailRecipientStatus,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update the main status of a specific email recipient (enable/disable).
    This only updates the main enable/disable status, not the CTA type configurations.

    Args:
        email_id: The ID of the email recipient
        status: The new status (enabled/disabled)
        current_user: Current user with database access

    Returns:
        Success status and message with the updated email
    """
    try:
        email_service = EmailConfigService(current_user)
        # Convert status string to boolean
        enable = status.status == "enabled"
        result = await email_service.update_email_recipient_status(email_id, enable)

        # Set the appropriate status code
        status_code = result.get("status_code", 200)
        if not result.get("success", False):
            # For 409 Conflict, return the data along with the error
            if status_code == 409 and "data" in result:
                # Use our custom function to recursively convert all ObjectId instances to strings
                sanitized_data = convert_objectid_to_str(result)

                # Return a JSONResponse with the sanitized data
                return JSONResponse(
                    content={
                        "success": False,
                        "message": sanitized_data.get("message", f"Email recipient already {status.status}"),
                        "data": sanitized_data.get("data", {})
                    },
                    status_code=status_code
                )
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", f"Email recipient {status.status} successfully"),
                "data": sanitized_result.get("data", {})
            },
            status_code=status_code
        )
    except Exception as e:
        loggers.error(f"Error updating email recipient status: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

@router.put("/email_recipient/{email_id}/cta_type", status_code=200)
async def update_email_cta_type(
    email_id: str,
    cta_type: CTATypeConfig,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update a CTA type configuration for a specific email recipient.

    Args:
        email_id: The ID of the email recipient
        cta_type: The CTA type configuration to update
        current_user: Current user with database access

    Returns:
        Success status and message with the updated email
    """
    try:
        email_service = EmailConfigService(current_user)
        result = await email_service.update_email_cta_type(email_id, cta_type)

        # Set the appropriate status code
        status_code = result.get("status_code", 200)
        if not result.get("success", False):
            # For 409 Conflict, return the data along with the error
            if status_code == 409 and "data" in result:
                # Use our custom function to recursively convert all ObjectId instances to strings
                sanitized_data = convert_objectid_to_str(result)

                # Return a JSONResponse with the sanitized data
                return JSONResponse(
                    content={
                        "success": False,
                        "message": sanitized_data.get("message", "CTA type already in requested state"),
                        "data": sanitized_data.get("data", {})
                    },
                    status_code=status_code
                )
            raise HTTPException(status_code=status_code, detail=result.get("message", "Unknown error"))

        # Use our custom function to recursively convert all ObjectId instances to strings
        sanitized_result = convert_objectid_to_str(result)

        # Return a JSONResponse with the sanitized data
        return JSONResponse(
            content={
                "success": True,
                "message": sanitized_result.get("message", f"CTA type '{cta_type.name}' updated for email recipient"),
                "data": sanitized_result.get("data", {})
            },
            status_code=status_code
        )
    except Exception as e:
        loggers.error(f"Error updating CTA type for email recipient: {e}")
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
