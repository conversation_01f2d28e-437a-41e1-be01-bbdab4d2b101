"""
Email configuration service for managing email notification settings.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from bson import ObjectId
from fastapi import HTTPException

from src.v2.dashboard.cta.models import CTAType
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.whatsapp_webhook.email_sender.email_models import (
    EmailRecipient,
    CTATypeConfig,
    EmailNotificationConfig,
)

loggers = setup_new_logging(__name__)


class EmailConfigService:
    """Service for managing email notification configurations."""

    def __init__(self, current_user: UserTenantDB):
        """Initialize with database connection."""
        self.db = current_user.db
        self.current_user = current_user

    async def get_email_config(self) -> Dict[str, Any]:
        """
        Get the current email notification configuration.
        If no configuration exists, creates a default one with proper IDs.

        Returns:
            Dict[str, Any]: The email configuration as a dictionary with proper ID structure
        """
        try:
            # Get email notification settings
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            if not email_settings:
                # Create default configuration with proper IDs
                default_config = self._create_default_config()

                # Add user tracking information
                default_config["created_by"] = self.current_user.user.id
                default_config["updated_by"] = self.current_user.user.id
                default_config["created_at"] = datetime.now()
                default_config["updated_at"] = datetime.now()

                # Insert into database
                result = self.db.settings.insert_one(default_config)

                if not result.inserted_id:
                    raise HTTPException(
                        status_code=500,
                        detail="Failed to create default email configuration",
                    )

                # Get the newly created configuration
                email_settings = self.db.settings.find_one({"_id": result.inserted_id})

            # Create a copy to avoid modifying the original
            config_response = dict(email_settings)

            # Convert ObjectId to string for JSON serialization
            if "_id" in config_response:
                config_response["id"] = str(config_response["_id"])
                del config_response["_id"]

            # Convert ObjectId to string in emails array
            if "emails" in config_response and config_response["emails"]:
                for email in config_response["emails"]:
                    if "_id" in email:
                        email["id"] = str(email["_id"])
                        del email["_id"]

            # Convert ObjectId to string in cta_types array
            if "cta_types" in config_response and config_response["cta_types"]:
                for cta_type in config_response["cta_types"]:
                    if "_id" in cta_type:
                        cta_type["id"] = str(cta_type["_id"])
                        del cta_type["_id"]

            # Convert created_by and updated_by to strings if they exist
            if "created_by" in config_response and config_response["created_by"]:
                config_response["created_by"] = str(config_response["created_by"])

            if "updated_by" in config_response and config_response["updated_by"]:
                config_response["updated_by"] = str(config_response["updated_by"])

            return config_response
        except Exception as e:
            loggers.error(f"Error getting email notification config: {e}")
            raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

    async def get_email_status(self, email_id: str) -> Dict[str, Any]:
        """
        Get the current status of a specific email recipient (enabled/disabled).

        Args:
            email_id: The ID of the email recipient

        Returns:
            Dict[str, Any]: The status of the email recipient with success flag
        """
        try:
            # Find the email recipient by ID
            email_info = self.db.settings.find_one(
                {"name": "email_notifications", "emails._id": ObjectId(email_id)},
                {
                    "emails.$": 1  # This projects only the matched element from the array
                },
            )

            if not email_info or "emails" not in email_info or not email_info["emails"]:
                return {
                    "success": False,
                    "message": f"Email with ID {email_id} not found",
                    "status_code": 404,  # Not Found
                }

            # Get the email recipient data
            email_recipient = email_info["emails"][0]

            # Convert ObjectId to string for JSON serialization
            if "_id" in email_recipient:
                email_recipient["id"] = str(email_recipient["_id"])
                del email_recipient["_id"]

            # Convert ObjectIds in CTA types if they exist
            if "cta_types" in email_recipient and email_recipient["cta_types"]:
                for ct in email_recipient["cta_types"]:
                    if "_id" in ct:
                        ct["id"] = str(ct["_id"])
                        del ct["_id"]

            return {
                "success": True,
                "message": "Email status retrieved successfully",
                "data": {
                    "email": email_recipient.get("email"),
                    "enable": email_recipient.get("enable", False),
                    "id": email_recipient.get("id")
                },
                "status_code": 200
            }
        except Exception as e:
            loggers.error(f"Error getting email notification status: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def update_email_config(
        self, config: EmailNotificationConfig
    ) -> Dict[str, Any]:
        """
        Update the email notification configuration.

        Args:
            config: The new email notification configuration

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email settings already exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            # Prepare emails with proper ObjectId handling
            emails_data = []
            for email in config.emails:
                email_data = email.model_dump()
                # If id exists, convert it to _id for MongoDB
                if "id" in email_data:
                    email_data["_id"] = ObjectId(email_data.pop("id"))
                emails_data.append(email_data)

            # Prepare cta_types with proper ObjectId handling
            cta_types_data = []
            for cta_type in config.cta_types:
                cta_type_data = cta_type.model_dump()
                # If id exists, convert it to _id for MongoDB
                if "id" in cta_type_data:
                    cta_type_data["_id"] = ObjectId(cta_type_data.pop("id"))
                cta_types_data.append(cta_type_data)

            if email_settings:
                # Update existing settings
                result = self.db.settings.update_one(
                    {"name": "email_notifications"},
                    {
                        "$set": {
                            "emails": emails_data,
                            "cta_types": cta_types_data,
                            "enabled": config.enabled,
                            "updated_at": datetime.now(),
                            "updated_by": self.current_user.user.id,
                        }
                    },
                )
                if result.modified_count == 0:
                    return {
                        "success": False,
                        "message": "Failed to update email notification settings",
                    }
            else:
                # Create new settings
                result = self.db.settings.insert_one(
                    {
                        "name": "email_notifications",
                        "emails": emails_data,
                        "cta_types": cta_types_data,
                        "enabled": config.enabled,
                        "created_at": datetime.now(),
                        "updated_at": datetime.now(),
                        "created_by": self.current_user.user.id,
                        "updated_by": self.current_user.user.id,
                    }
                )
                if not result.inserted_id:
                    return {
                        "success": False,
                        "message": "Failed to create email notification settings",
                    }

            return {
                "success": True,
                "message": "Email notification settings saved successfully",
            }
        except Exception as e:
            loggers.error(f"Error configuring email notifications: {e}")
            return {"success": False, "message": f"Error: {str(e)}"}

    async def add_email_recipient(self, email: EmailRecipient) -> Dict[str, Any]:
        """
        Add a new email recipient to the notification list.

        Args:
            email: The email recipient to add

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email already exists
            existing_email = self.db.settings.find_one(
                {"name": "email_notifications", "emails.email": email.email}
            )
            if existing_email:
                # Find the existing email data to return
                for email_item in existing_email.get("emails", []):
                    if email_item.get("email") == email.email:
                        # Convert ObjectId to string for JSON serialization
                        if "_id" in email_item:
                            email_item["id"] = str(email_item["_id"])
                            del email_item["_id"]

                        # Convert ObjectIds in CTA types if they exist
                        if "cta_types" in email_item and email_item["cta_types"]:
                            for ct in email_item["cta_types"]:
                                if "_id" in ct:
                                    ct["id"] = str(ct["_id"])
                                    del ct["_id"]

                        return {
                            "success": False,
                            "message": f"Email {email.email} already exists in notification list",
                            "data": email_item,
                            "status_code": 409,  # Conflict
                        }

                # If we couldn't find the specific email item, just return a generic response
                return {
                    "success": False,
                    "message": f"Email {email.email} already exists in notification list",
                    "status_code": 409,  # Conflict
                }
            # CREATE NEW email data

            email_data = email.model_dump()
            # Remove id if it exists, we'll create a new one
            if "id" in email_data:
                del email_data["id"]

            # Add a new ObjectId for this email
            email_data["_id"] = ObjectId()

            # Add CTA types configuration if not provided
            if "cta_types" not in email_data or not email_data["cta_types"]:
                email_data["cta_types"] = [
                    {
                        "name": cta_type,
                        "enable": False,
                        "_id": ObjectId(),
                    }  # Default to disabled
                    for cta_type in CTAType
                ]
            # add to db
            result = self.db.settings.update_one(
                {"name": "email_notifications"},
                {"$push": {"emails": email_data}},
                upsert=True,
            )
            if not result.matched_count:
                return {
                    "success": False,
                    "message": "Failed to add email recipient",
                    "status_code": 500,
                }
            # Get the inserted document to return the new email with ID
            new_email = self.db.settings.find_one(
                {"name": "email_notifications", "emails._id": email_data["_id"]},
                {"emails.$": 1},
            )
            if new_email and "emails" in new_email and len(new_email["emails"]) > 0:
                new_email = new_email["emails"][0]
                if "_id" in new_email:
                    new_email["id"] = str(new_email["_id"])
                    del new_email["_id"]

            return {
                "success": True,
                "message": "Email notification settings created with recipient",
                "data": new_email,
                "status_code": 201,  # Created
            }
        except Exception as e:
            import traceback

            traceback.print_exc()
            loggers.error(f"Error adding email recipient: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def update_email_status(self, status: str) -> Dict[str, Any]:
        """
        Update the email notification status (enable/disable).

        Args:
            status: The new status ("enabled" or "disabled")

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email settings already exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            enabled = status == "enabled"

            # Check if status is already set to the requested value
            if email_settings and email_settings.get("enabled") == enabled:
                return {
                    "success": False,
                    "message": f"Email notifications already {status}",
                    "data": {"status": status},  # Include the existing status data
                    "status_code": 409,  # Conflict
                }

            # Update the status in the database
            result = self.db.settings.update_one(
                {"name": "email_notifications"},
                {
                    "$set": {
                        "enabled": enabled,
                        "updated_at": datetime.now(),
                        "updated_by": self.current_user.user.id,
                    }
                },
                upsert=True,
            )

            if result.modified_count == 0 and result.upserted_id is None:
                return {
                    "success": False,
                    "message": "Failed to update email notification status",
                    "status_code": 500,
                }

            return {
                "success": True,
                "message": f"Email notifications {status}",
                "data": {"status": status},
                "status_code": 200,
            }
        except Exception as e:
            loggers.error(f"Error updating email notification status: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def delete_email_recipient(self, email_id: str) -> Dict[str, Any]:
        """
        Delete an email recipient from the notification list.

        Args:
            email_id: The ID of the email recipient to delete

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email settings exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            if (
                not email_settings
                or "emails" not in email_settings
                or not email_settings["emails"]
            ):
                return {
                    "success": False,
                    "message": "No email recipients found",
                    "status_code": 404,  # Not Found
                }

            # Convert string ID to ObjectId
            try:
                object_id = ObjectId(email_id)
            except Exception as e:
                loggers.error(f"Invalid email ID format: {email_id}, error: {e}")
                return {
                    "success": False,
                    "message": f"Invalid email ID format: {email_id}",
                    "status_code": 400,  # Bad Request
                }

            # Find the email to delete
            email_to_delete = None
            for email in email_settings["emails"]:
                if "_id" in email and email["_id"] == object_id:
                    email_to_delete = email
                    break

            if not email_to_delete:
                return {
                    "success": False,
                    "message": f"Email with ID {email_id} not found",
                    "status_code": 404,  # Not Found
                }

            # Remove the email from the list
            result = self.db.settings.update_one(
                {"name": "email_notifications"},
                {
                    "$pull": {"emails": {"_id": object_id}},
                    "$set": {
                        "updated_at": datetime.now(),
                        "updated_by": self.current_user.user.id,
                    },
                },
            )

            if result.modified_count == 0:
                return {
                    "success": False,
                    "message": "Failed to delete email recipient",
                    "status_code": 500,
                }

            return {
                "success": True,
                "message": f"Email recipient deleted successfully",
                "data": {"deleted_id": email_id},
                "status_code": 200,
            }
        except Exception as e:
            loggers.error(f"Error deleting email recipient: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def update_cta_type(self, cta_type: CTATypeConfig) -> Dict[str, Any]:
        """
        Update a CTA type configuration.

        Args:
            cta_type: The CTA type configuration to update

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email settings already exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            cta_type_data = cta_type.model_dump()

            if email_settings:
                # Check if this CTA type already exists
                existing_cta_types = email_settings.get("cta_types", [])
                found = False
                updated_cta_type = None

                for i, existing_type in enumerate(existing_cta_types):
                    if existing_type["name"] == cta_type_data["name"]:
                        # Check if status is already set to the requested value
                        if existing_type["enable"] == cta_type_data["enable"]:
                            # Create a copy of the existing CTA type data to return
                            existing_data = dict(existing_type)

                            # Convert ObjectId to string for JSON serialization
                            if "_id" in existing_data:
                                existing_data["id"] = str(existing_data["_id"])
                                del existing_data["_id"]

                            return {
                                "success": False,
                                "message": f"CTA type '{cta_type.name}' already {'enabled' if cta_type_data['enable'] else 'disabled'}",
                                "data": existing_data,  # Include the existing CTA type data
                                "status_code": 409,  # Conflict
                            }

                        # Update existing CTA type
                        existing_cta_types[i]["enable"] = cta_type_data["enable"]
                        updated_cta_type = existing_cta_types[i]
                        found = True
                        break

                if not found:
                    # Add new CTA type with a new ObjectId
                    if "id" in cta_type_data:
                        del cta_type_data["id"]  # Remove id for new entries

                    # Add a new ObjectId for this CTA type
                    cta_type_data["_id"] = ObjectId()

                    existing_cta_types.append(cta_type_data)
                    updated_cta_type = cta_type_data

                # Update the database
                result = self.db.settings.update_one(
                    {"name": "email_notifications"},
                    {
                        "$set": {
                            "cta_types": existing_cta_types,
                            "updated_at": datetime.now(),
                            "updated_by": self.current_user.user.id,
                        }
                    },
                )

                if result.modified_count == 0:
                    return {
                        "success": False,
                        "message": "Failed to update CTA type",
                        "status_code": 500,
                    }

                # Get the updated document to return the CTA type with ID
                updated_settings = self.db.settings.find_one(
                    {"name": "email_notifications"}
                )
                for cta in updated_settings.get("cta_types", []):
                    if cta.get("name") == cta_type_data["name"]:
                        if "_id" in cta:
                            cta["id"] = str(cta["_id"])
                            del cta["_id"]
                        updated_cta_type = cta
                        break

                return {
                    "success": True,
                    "message": f"CTA type '{cta_type.name}' updated successfully",
                    "data": updated_cta_type,
                    "status_code": 200,
                }
            else:
                # Create new settings with default config and this CTA type
                default_config = self._create_default_config()

                # Replace the CTA type with the same name, preserving the ObjectId
                updated_cta_type = None
                for i, default_type in enumerate(default_config["cta_types"]):
                    if default_type["name"] == cta_type_data["name"]:
                        # Keep the existing ObjectId but update the enable status
                        default_config["cta_types"][i]["enable"] = cta_type_data[
                            "enable"
                        ]
                        updated_cta_type = default_config["cta_types"][i]
                        break

                # Add created_at, updated_at, and user tracking
                default_config["created_at"] = datetime.now()
                default_config["updated_at"] = datetime.now()
                default_config["created_by"] = self.current_user.user.id
                default_config["updated_by"] = self.current_user.user.id

                # Insert the new configuration
                result = self.db.settings.insert_one(default_config)

                if not result.inserted_id:
                    return {
                        "success": False,
                        "message": "Failed to create email notification settings",
                        "status_code": 500,
                    }

                # Get the inserted document to return the CTA type with ID
                new_settings = self.db.settings.find_one({"_id": result.inserted_id})
                for cta in new_settings.get("cta_types", []):
                    if cta.get("name") == cta_type_data["name"]:
                        if "_id" in cta:
                            cta["id"] = str(cta["_id"])
                            del cta["_id"]
                        updated_cta_type = cta
                        break

                return {
                    "success": True,
                    "message": f"CTA type '{cta_type.name}' updated successfully",
                    "data": updated_cta_type,
                    "status_code": 201,  # Created
                }
        except Exception as e:
            loggers.error(f"Error updating CTA type: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def update_email_recipient_status(self, email_id: str, enable: bool) -> Dict[str, Any]:
        """
        Update the main status of a specific email recipient (enable/disable).
        This only updates the main enable/disable status, not the CTA type configurations.

        Args:
            email_id: The ID of the email recipient
            enable: The new status (True for enabled, False for disabled)

        Returns:
            Dict[str, Any]: Success status and message with the updated email
        """
        try:
            # Check if email settings exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            if (
                not email_settings
                or "emails" not in email_settings
                or not email_settings["emails"]
            ):
                return {
                    "success": False,
                    "message": "No email recipients found",
                    "status_code": 404,  # Not Found
                }

            # Convert string ID to ObjectId
            try:
                email_object_id = ObjectId(email_id)
            except Exception as e:
                loggers.error(f"Invalid email ID format: {email_id}, error: {e}")
                return {
                    "success": False,
                    "message": f"Invalid email ID format: {email_id}",
                    "status_code": 400,  # Bad Request
                }

            # Find the email recipient
            email_recipient = None
            email_index = -1
            for i, email in enumerate(email_settings["emails"]):
                if "_id" in email and email["_id"] == email_object_id:
                    email_recipient = email
                    email_index = i
                    break

            if not email_recipient:
                return {
                    "success": False,
                    "message": f"Email with ID {email_id} not found",
                    "status_code": 404,  # Not Found
                }

            # Check if status is already set to the requested value
            if email_recipient.get("enable") == enable:
                # Create a copy of the email recipient to return
                email_data = dict(email_recipient)

                # Convert ObjectId to string for JSON serialization
                if "_id" in email_data:
                    email_data["id"] = str(email_data["_id"])
                    del email_data["_id"]

                # Convert ObjectIds in CTA types if they exist
                if "cta_types" in email_data and email_data["cta_types"]:
                    for ct in email_data["cta_types"]:
                        if "_id" in ct:
                            ct["id"] = str(ct["_id"])
                            del ct["_id"]

                return {
                    "success": False,
                    "message": f"Email recipient already {'enabled' if enable else 'disabled'}",
                    "data": email_data,
                    "status_code": 409,  # Conflict
                }

            # If disabling the email, also disable all CTA types
            update_dict = {
                f"emails.{email_index}.enable": enable,
                "updated_at": datetime.now(),
                "updated_by": self.current_user.user.id,
            }
            
            # If disabling the email, also disable all CTA types
            if not enable and "cta_types" in email_recipient:
                for i, ct_type in enumerate(email_recipient["cta_types"]):
                    update_dict[f"emails.{email_index}.cta_types.{i}.enable"] = False
            
            result = self.db.settings.update_one(
                {"name": "email_notifications", f"emails.{email_index}._id": email_object_id},
                {"$set": update_dict},
            )

            if result.modified_count == 0:
                return {
                    "success": False,
                    "message": "Failed to update email recipient status",
                    "status_code": 500,
                }

            # Get the updated document to return the email with updated status
            updated_settings = self.db.settings.find_one(
                {"name": "email_notifications", "emails._id": email_object_id},
                {"emails.$": 1}
            )

            if not updated_settings or "emails" not in updated_settings or not updated_settings["emails"]:
                return {
                    "success": True,
                    "message": f"Email recipient {'enabled' if enable else 'disabled'} successfully",
                    "data": {"id": email_id, "enable": enable},
                    "status_code": 200,
                }

            # Get the updated email recipient
            updated_email = updated_settings["emails"][0]

            # Convert ObjectId to string for JSON serialization
            if "_id" in updated_email:
                updated_email["id"] = str(updated_email["_id"])
                del updated_email["_id"]

            # Convert ObjectIds in CTA types if they exist
            if "cta_types" in updated_email and updated_email["cta_types"]:
                for ct in updated_email["cta_types"]:
                    if "_id" in ct:
                        ct["id"] = str(ct["_id"])
                        del ct["_id"]

            return {
                "success": True,
                "message": f"Email recipient {'enabled' if enable else 'disabled'} successfully",
                "data": updated_email,
                "status_code": 200,
            }
        except Exception as e:
            loggers.error(f"Error updating email recipient status: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    async def update_email_cta_type(
        self, email_id: str, cta_type: CTATypeConfig
    ) -> Dict[str, Any]:
        """
        Update a CTA type configuration for a specific email recipient.

        Args:
            email_id: The ID of the email recipient
            cta_type: The CTA type configuration to update

        Returns:
            Dict[str, Any]: Success status and message
        """
        try:
            # Check if email settings exist
            email_settings = self.db.settings.find_one({"name": "email_notifications"})

            if (
                not email_settings
                or "emails" not in email_settings
                or not email_settings["emails"]
            ):
                return {
                    "success": False,
                    "message": "No email recipients found",
                    "status_code": 404,  # Not Found
                }

            # Convert string ID to ObjectId
            try:
                email_object_id = ObjectId(email_id)
            except Exception as e:
                loggers.error(f"Invalid email ID format: {email_id}, error: {e}")
                return {
                    "success": False,
                    "message": f"Invalid email ID format: {email_id}",
                    "status_code": 400,  # Bad Request
                }

            # Find the email recipient
            email_recipient = None
            email_index = -1
            for i, email in enumerate(email_settings["emails"]):
                if "_id" in email and email["_id"] == email_object_id:
                    email_recipient = email
                    email_index = i
                    break

            if not email_recipient:
                return {
                    "success": False,
                    "message": f"Email with ID {email_id} not found",
                    "status_code": 404,  # Not Found
                }

            # Get CTA type data
            cta_type_data = cta_type.model_dump()
            if "id" in cta_type_data:
                del cta_type_data["id"]  # We'll use the existing ID or create a new one

            # Check if this email has CTA types configuration
            if "cta_types" not in email_recipient or not email_recipient["cta_types"]:
                # Create CTA types configuration for this email
                email_recipient["cta_types"] = []
                for ct_type in CTAType:
                    # For the matching CTA type, use the provided configuration
                    if ct_type.value == cta_type_data["name"]:
                        cta_type_data["_id"] = ObjectId()  # Create a new ID
                        email_recipient["cta_types"].append(cta_type_data)
                    else:
                        # For other CTA types, create default entries
                        email_recipient["cta_types"].append(
                            {
                                "_id": ObjectId(),
                                "name": ct_type.value,
                                "enable": False,  # Default to disabled
                            }
                        )
            else:
                # Update existing CTA type configuration
                found = False
                for i, ct_type in enumerate(email_recipient["cta_types"]):
                    if ct_type["name"] == cta_type_data["name"]:
                        # Keep the existing ID
                        if "_id" in ct_type:
                            cta_type_data["_id"] = ct_type["_id"]
                        else:
                            cta_type_data["_id"] = (
                                ObjectId()
                            )  # Create a new ID if missing

                        # Update the CTA type
                        email_recipient["cta_types"][i] = cta_type_data
                        found = True
                        break

                if not found:
                    # Add new CTA type
                    cta_type_data["_id"] = ObjectId()  # Create a new ID
                    email_recipient["cta_types"].append(cta_type_data)

            # Update the email recipient in the database
            result = self.db.settings.update_one(
                {"name": "email_notifications"},
                {
                    "$set": {
                        f"emails.{email_index}": email_recipient,
                        "updated_at": datetime.now(),
                        "updated_by": self.current_user.user.id,
                    }
                },
            )

            if result.modified_count == 0:
                return {
                    "success": False,
                    "message": "Failed to update CTA type for email recipient",
                    "status_code": 500,
                }

            # Get the updated document to return the email with updated CTA type
            updated_settings = self.db.settings.find_one(
                {"name": "email_notifications"}
            )
            updated_email = None
            for email in updated_settings.get("emails", []):
                if "_id" in email and email["_id"] == email_object_id:
                    # Create a copy to avoid modifying the original
                    updated_email = dict(email)
                    # Convert ObjectId to string for JSON serialization
                    updated_email["id"] = str(updated_email["_id"])
                    del updated_email["_id"]

                    # Convert ObjectIds in CTA types
                    if "cta_types" in updated_email and updated_email["cta_types"]:
                        for ct in updated_email["cta_types"]:
                            if "_id" in ct:
                                ct["id"] = str(ct["_id"])
                                del ct["_id"]
                    break

            return {
                "success": True,
                "message": f"CTA type '{cta_type.name}' updated for email recipient",
                "data": updated_email,
                "status_code": 200,
            }
        except Exception as e:
            loggers.error(f"Error updating CTA type for email recipient: {e}")
            return {"success": False, "message": f"Error: {str(e)}", "status_code": 500}

    def _create_default_config(self) -> Dict[str, Any]:
        """
        Create a default email configuration.

        Returns:
            Dict[str, Any]: Default email configuration
        """
        # Return the default configuration without inserting it
        # The calling methods will handle insertion if needed
        # No default CTA types at the global level - each email will have its own configuration
        return {
            "name": "email_notifications",
            "emails": [],
            "cta_types": [],  # Empty list - no global CTA types
            "enabled": False,
        }
