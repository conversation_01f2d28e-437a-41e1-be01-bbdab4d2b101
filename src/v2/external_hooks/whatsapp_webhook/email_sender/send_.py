from typing import Dict, Any, List
import asyncio
from src.models.user import UserTenantDB
from src.v2.external_hooks.whatsapp_webhook.email_sender.email_service import EmailConfigService
from src.helper.logger import setup_new_logging
from src.v2.external_hooks.whatsapp_webhook.email_sender.email_models import (
    EmailRequest,
)
from src.v2.external_hooks.whatsapp_webhook.email_sender.template import html_
from fastapi import Depends
from src.core.security import get_tenant_info
import aiohttp
from src.v2.dashboard.cta.models import CTA

loggers = setup_new_logging(__name__)

async def send_cta_email_notification(cta_data: Dict[str, Any], current_user: UserTenantDB) -> None:
    """
    Send email notification for a new CTA to all eligible recipients in parallel

    Args:
        cta_data (Dict[str, Any]): CTA data
        current_user (UserTenantDB): Current user with database access
    """

    try:
        if isinstance(cta_data, CTA):
            cta_data = cta_data.model_dump()
        email_service = EmailConfigService(current_user)


        email_config = await email_service.get_email_config()
        if not email_config.get("enabled", False):
            loggers.info("Email notifications are disabled")
            return

        emails = email_config.get("emails", [])
        current_cta_type = cta_data.get("type")
        loggers.log_dict_as_table(cta_data, "Email Configuration")

        # List to store email tasks for parallel execution
        email_tasks = []

        for email in emails:
            if not email.get("enable", False):
                loggers.info(f"Email {email.get('email')} is disabled")
                continue

            cta_types = email.get("cta_types", [])

            # Check if the current CTA type is enabled for this email
            for cta_type in cta_types:
                if cta_type.get("name") == current_cta_type and cta_type.get("enable", False):
                    loggers.info(f"CTA type {current_cta_type} is enabled for email {email.get('email')}")

                    # Map the CTA data to the EmailRequest model
                    # First check direct fields, then extra_info
                    extra_info = cta_data.get("extra_info", {}) or {}  # Ensure extra_info is a dict, not None

                    # Get customer name from the most specific source available
                    customer_name = cta_data.get("customer_name") or extra_info.get("customer_name", "Not Available")

                    # Get phone number from the most specific source available
                    phone_number = cta_data.get("customer_phone_number") or extra_info.get("whatsapp_number", "Not Available")
                    if phone_number:
                        if phone_number.startswith("whatsapp:"):
                            # split the phone number to remove the whatsapp: prefix
                            phone_number = phone_number.split(":")[1]
                    else:
                        phone_number = "Not Available"

                    # Get email from the most specific source available
                    email_address = cta_data.get("customer_email") or extra_info.get("email", "Not Available")

                    # Get assigned agent name
                    assigned_to_name = cta_data.get("assigned_to_name", "Unassigned")

                    email_request_data = {
                        "TO": email.get("email"),
                        "FULL_NAME": customer_name,
                        "EMAIL": email_address,
                        "PHONE_NUMBER": phone_number,
                        "CHANNEL": cta_data.get("channel", "Not Available"),
                        "DESCRIPTION": cta_data.get("description", "Not Available"),
                        "ASSIGNED_TO": assigned_to_name,
                        "type": current_cta_type
                    }

                    # Create email request and add to tasks list
                    email_request = EmailRequest(**email_request_data)
                    email_tasks.append(send_email(email_request, current_user))
                    loggers.info(f"Queued email to {email.get('email')} for CTA type {current_cta_type}")

        # If we have email tasks, execute them all in parallel
        if email_tasks:
            loggers.info(f"Sending {len(email_tasks)} emails in parallel")
            # Use gather to run all email sending tasks concurrently
            results = await asyncio.gather(*email_tasks, return_exceptions=True)

            # Log results
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    loggers.error(f"Email {i+1} failed: {str(result)}")
                else:
                    loggers.info(f"Email {i+1} sent successfully: {result}")
        else:
            loggers.info("No eligible email recipients found for this CTA type")

    except Exception as e:
        import traceback
        traceback.print_exc()
        loggers.error(f"Error sending CTA email notification: {str(e)}")


# HTTP headers for email API
headers = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:139.0) Gecko/20100101 Firefox/139.0",
    "Accept": "application/json, text/plain, /",
    "Accept-Language": "en-US,en;q=0.5",
    "Content-Type": "application/json",
    "Authorization": "Bearer {token}",
    "X-Requested-With": "XMLHttpRequest",
    "Origin": "https://beautyai.sociair.io",
    "Connection": "keep-alive",
    "Referer": "https://beautyai.sociair.io/",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "cross-site",
    "DNT": "1",
    "Sec-GPC": "1",
    "Priority": "u=0",
}


async def send_email(request: EmailRequest, current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Send an email using the configured email service.

    Args:
        request: Email request details
        current_user: Current user with database access

    Returns:
        Response from the email service
    """
    recipient = request.TO
    loggers.info(f"Preparing to send email to {recipient}")
    try:
        # Generate subject based on CTA type if not provided
        subject = request.SUBJECT
        if not subject:
            if request.type == "ticket":
                subject = f"New Support Ticket : {request.FULL_NAME}"
            elif request.type == "booking":
                subject = f"New Booking Request : {request.FULL_NAME}"
            else:
                subject = f"New CTA Notification: {request.type}"

        placeholders = {
            "SUBJECT": subject,
            "FULL_NAME": request.FULL_NAME,
            "EMAIL": request.EMAIL,
            "PHONE": request.PHONE_NUMBER,
            "CHANNEL": request.CHANNEL,
            "DESCRIPTION": request.DESCRIPTION,
            "ASSIGNED_TO": request.ASSIGNED_TO,
        }

        # Get email token from settings
        env_settings = current_user.db.settings.find_one({"name": "env"})
        if not env_settings or "email" not in env_settings or "token" not in env_settings.get("email", {}):
            error_msg = "Email token not found in settings"
            loggers.error(f"{error_msg} for recipient {recipient}")
            return {"status": "error", "message": error_msg, "recipient": recipient}

        token = env_settings.get("email", {}).get("token")
        auth_header = headers["Authorization"].format(token=token)

        # Create a copy of headers to avoid modifying the global headers
        email_headers = headers.copy()
        email_headers["Authorization"] = auth_header

        # Replace placeholders in HTML template
        email_html = html_
        for key, value in placeholders.items():
            email_html = email_html.replace(f"{{{{{key}}}}}", str(value) if value is not None else "Not Available")

        json_data = {
            "subject": subject,
            "to_email": recipient,
            "content": email_html,
        }

        loggers.info(f"Sending email to {recipient} with subject: {subject}")

        # Use aiohttp for async HTTP requests
        async with aiohttp.ClientSession() as session:
            
            async with session.post(
                "https://new-central-api.sociair.com/api/smm/microsoft/conversations/1/send-new-message",
                headers=email_headers,
                json=json_data,
                timeout=30  # Add timeout to prevent hanging requests
            ) as response:
                status = response.status
                response_text = await response.text()
                loggers.info(f"Email response: {response_text}")
                if 200 <= status < 300:
                    loggers.info(f"Email sent successfully to {recipient} - Status: {status}")
                    return {
                        "status": "success",
                        "http_status": status,
                        "response": response_text,
                        "recipient": recipient,
                        "subject": subject
                    }
                else:
                    
                    loggers.error(f"Failed to send email to {recipient} - Status: {status} - Response: {response_text}")
                    loggers.log_dict_as_table(json_data, "Email Request")
                    return {
                        "status": "error",
                        "http_status": status,
                        "response": response_text,
                        "recipient": recipient,
                        "subject": subject
                    }
    except aiohttp.ClientError as e:
        error_msg = f"HTTP client error when sending email to {recipient}: {str(e)}"
        loggers.error(error_msg)
        return {"status": "error", "message": error_msg, "recipient": recipient}
    except asyncio.TimeoutError:
        error_msg = f"Timeout when sending email to {recipient}"
        loggers.error(error_msg)
        return {"status": "error", "message": error_msg, "recipient": recipient}
    except Exception as e:
        error_msg = f"Error sending email to {recipient}: {str(e)}"
        loggers.error(error_msg)
        return {"status": "error", "message": error_msg, "recipient": recipient}


