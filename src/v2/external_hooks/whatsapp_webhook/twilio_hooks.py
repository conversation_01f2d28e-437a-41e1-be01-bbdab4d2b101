from fastapi import APIRouter, Depends, HTTPException
import requests
from src.core.database import get_db_from_tenant_id
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info

log = setup_new_logging(__name__)
hook_router = APIRouter(prefix="/twilio", tags=["Twilio WebHooks"])


@hook_router.get("/get_templates")
async def get_templates(current_user: UserTenantDB = Depends(get_tenant_info)):
    settings = await current_user.async_db.settings.find_one({"name": "whatsapp_env"})
    if not settings:
        raise HTTPException(status_code=404, detail="WhatsApp environment settings not found")

    account_sid = settings.get("ACCOUNT_SID")
    auth_token = settings.get("AUTH_TOKEN")
    template_url = settings.get("TEMPLATE_URL")

    try:
        response = requests.get(template_url, auth=(account_sid, auth_token))
        response.raise_for_status()
    except requests.RequestException as e:
        log.error(f"Failed to fetch templates: {e}")
        # raise HTTPException(status_code=502, detail="Error fetching templates from external service")
        return []

    templates = response.json().get("contents")
    if not templates:
        # raise HTTPException(status_code=404, detail="No templates found")
        return []

    results = []
    for template in templates:
        sid=template.get("sid")
        template_name = template.get("friendly_name")
        created_at = template.get("date_created")
        updated_at = template.get("date_updated")
        language = template.get("language")
        variables = template.get("variables")

        types = template.get("types", {})
        for type_key, type_info in types.items():
            text = type_info.get("body") or type_info.get("text") or ""
            results.append({
                "template_name": template_name,
                "created_at": created_at,
                "updated_at": updated_at,
                "language": language,
                "type": type_key,
                "text": text,
                "variables": variables,
                "sid":sid
            })

    return results
