from pydantic import BaseModel
from typing import List,Optional

class TwilioRequest(BaseModel):
    AccountSid: str
    ApiVersion: str
    Body: Optional[str]=""
    From: str
    MessageSid: str
    MessageType: str
    NumMedia: int
    NumSegments: int
    ProfileName: str
    ReferralNumMedia: int
    SmsMessageSid: str
    SmsSid: str
    SmsStatus: str
    To: str
    WaId: str
    MediaUrl0: Optional[str] = None