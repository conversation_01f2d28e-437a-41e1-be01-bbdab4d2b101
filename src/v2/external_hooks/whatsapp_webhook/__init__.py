
# from manish import Ma<PERSON>ish
# from fastapi import APIRouter, Request
# from src.core.database import get_db_from_tenant_id
# from datetime import datetime

# # imports for customer related models
# from src.models.customers import CustomerModel

# # Imports for /final call
# from src.models.model_run import ModelRunRequest
# from src.models.user import UserTenantDB
# from src.core.security import get_tenant_info
# from src.routes.respond import final
# from src.models.chat_history import ChatHistMessage
# from src.helper.logger import setup_new_logging

# logger = setup_new_logging(__name__)

# # Imports for /login call
# from src.routes.dashboard.users import login
# from src.models.security import OAuth2PasswordRequestFormWithClientID


# async def handle_whatsapp_text(
#     username: str,
#     message: str,
#     mobile: str,
#     whatsapp_instance: MaNish,
#     ekobot_access_token: str,
#     db
# ):  
#     print(ekobot_access_token)
#     whatsapp_instance.send_message(f"Hi {username}, nice to connect with you", mobile)
#     return

#     # Format the data according to how /final accepts whatsapp data

#     # call /final with the data

#     # let's find the user_id using the mobile number or the username of the user
#     # if the phone number exists, we will use that if not we will create a new user with the username and phone number.

#     customer = CustomerModel.from_phone_number(mobile, db)
#     last_6_chat_history = customer.aggregate_chat_history() if customer else []
#     last_6_chat_history.append({
#         "role": "user",
#         "data": [{"sender": username, "created_at": datetime.now(), "message": message}]
#     })

#     final_response = await final(ModelRunRequest(
#         user_id=customer.customer_id,
#         message=None,
#         chat_data_format="role_data",
#         chat_data=last_6_chat_history,
#         username=username,
#         mobile=mobile,
#         whatsapp_instance=whatsapp_instance,
#         ekobot_access_token=ekobot_access_token
#     ))


# router = APIRouter(tags=["Whatsapp webhook"], prefix="/whatsapp_webhook")

# @router.get("/{tenant_id}/", include_in_schema=True)
# async def verify(tenant_id:str, request: Request):

#     # Get this tenant and get the whatsapp_env from it's settings
#     try:
#         db = get_db_from_tenant_id(tenant_id)
#         whatsapp_env = db.settings.find_one({"name": "whatsapp_env"})
#         VERIFY_TOKEN = whatsapp_env.get("VERIFY_TOKEN")
#     except Exception as e:
#         logger.error(f"Error getting whatsapp env from db: {e}")
#         return "Error getting tenant" + str(e), 500

#     print(request.query_params)
#     if request.query_params.get('hub.mode') == "subscribe" and request.query_params.get("hub.challenge"):
#         if not request.query_params.get('hub.verify_token') == VERIFY_TOKEN: #os.environ["VERIFY_TOKEN"]:
#             return "Verification token mismatch", 403
#         print("HERE", VERIFY_TOKEN)
#         return int(request.query_params.get('hub.challenge'))
#     return "Hello world", 200


# @router.post("/{tenant_id}/", include_in_schema=True)
# async def webhook(tenant_id:str, request: Request):
#     logger.info(tenant_id)
#     # print("aayoo")
#     try:
#         db = get_db_from_tenant_id(tenant_id)
#         whatsapp_env = db.settings.find_one({"name": "whatsapp_env"})
#         VERIFY_TOKEN = whatsapp_env.get("VERIFY_TOKEN")
#         TOKEN = whatsapp_env.get("TOKEN")
#         PHONE_NUMBER_ID = whatsapp_env.get("PHONE_NUMBER_ID")
#         WHATSAPP_EKO_BOT_ACESS_TOKEN = whatsapp_env.get("WHATSAPP_EKO_BOT_ACESS_TOKEN")
#     except Exception as e:
#         logger.error(f"Error getting whatsapp env from db: {e}")
#         return "Error getting tenant" + str(e), 500
    
#     ekobot_acess_token = WHATSAPP_EKO_BOT_ACESS_TOKEN

#     manish = MaNish(TOKEN, phone_number_id=PHONE_NUMBER_ID)

#     data = await request.json()
#     changed_field = manish.changed_field(data)
#     if changed_field == "messages":
#         new_message = manish.get_mobile(data)
#         if new_message:
#             mobile = manish.get_mobile(data)
#             name = manish.get_name(data)
#             message_type = manish.get_message_type(data)
#             logger.info(
#                 f"New Message; sender:{mobile} name:{name} type:{message_type}"
#             )
#             if message_type == "text":
#                 message = manish.get_message(data)
#                 name = manish.get_name(data)
#                 logger.info("Message: %s", message)
#                 # manish.send_message(f"Hi {name}, nice to connect with you yessssss sir", mobile)

#                 handle_whatsapp_text(
#                     username=name,
#                     message=message,
#                     mobile=mobile,
#                     whatsapp_instance=manish,
#                     ekobot_access_token=ekobot_acess_token,
#                     db=db
#                 )

#             elif message_type == "interactive":
#                 message_response = manish.get_interactive_response(data)
#                 intractive_type = message_response.get("type")
#                 message_id = message_response[intractive_type]["id"]
#                 message_text = message_response[intractive_type]["title"]
#                 logger.info(f"Interactive Message; {message_id}: {message_text}")

#             elif message_type == "location":
#                 message_location = manish.get_location(data)
#                 message_latitude = message_location["latitude"]
#                 message_longitude = message_location["longitude"]
#                 logger.info("Location: %s, %s", message_latitude, message_longitude)

#             elif message_type == "image":
#                 print("IMAGE")
#                 image = manish.get_image(data)
#                 image_id, mime_type = image["id"], image["mime_type"]
#                 image_url = manish.query_media_url(image_id)
#                 image_filename = manish.download_media(image_url, mime_type)
#                 logger.info(f"{mobile} sent image {image_filename}")
#                 # manish.send_image(image_filename, mobile)

#             elif message_type == "video":
#                 video = manish.get_video(data)
#                 video_id, mime_type = video["id"], video["mime_type"]
#                 video_url = manish.query_media_url(video_id)
#                 video_filename = manish.download_media(video_url, mime_type)
#                 logger.info(f"{mobile} sent video {video_filename}")

#             elif message_type == "audio":
#                 audio = manish.get_audio(data)
#                 audio_id, mime_type = audio["id"], audio["mime_type"]
#                 audio_url = manish.query_media_url(audio_id)
#                 audio_filename = manish.download_media(audio_url, mime_type)
#                 logger.info(f"{mobile} sent audio {audio_filename}")
#                 manish.send_audio(audio_filename, mobile)

#             elif message_type == "file":
#                 file = manish.get_file(data)
#                 file_id, mime_type = file["id"], file["mime_type"]
#                 file_url = manish.query_media_url(file_id)
#                 file_filename = manish.download_media(file_url, mime_type)
#                 logger.info(f"{mobile} sent file {file_filename}")

#             else:
#                 logger.info(f"{mobile} sent {message_type} ")
#                 logger.info(data)
#         else:
#             delivery = manish.get_delivery(data)
#             if delivery:
#                 logger.info(f"Message : {delivery}")
#             else:
#                 logger.info("No new message")
#     return "ok"
