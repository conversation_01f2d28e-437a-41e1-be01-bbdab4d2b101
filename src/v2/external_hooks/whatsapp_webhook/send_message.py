from twilio.rest import Client
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from typing import Optional, List
from pydantic import BaseModel
from fastapi import APIRouter, Depends
from src.core.security import get_tenant_info
from src.models.chat_hist import ChatHistMessage
from datetime import datetime
from bson import ObjectId
import json

router = APIRouter()

loggers = setup_new_logging(__name__)


class WhatsAppConfig(BaseModel):
    """Configuration for WhatsApp messaging"""

    account_sid: str
    auth_token: str
    phone_number: str


class WhatsAppMessage(BaseModel):
    """Message parameters for WhatsApp"""

    from_: str
    body: str
    to: str
    media_url: Optional[str] = None
    force_delivery: Optional[bool] = True


async def get_whatsapp_config(current_user: UserTenantDB) -> WhatsAppConfig:
    """Retrieve WhatsApp configuration from database"""

    env_var = await current_user.async_db.settings.find_one({"name": "whatsapp_env"})
    return WhatsAppConfig(
        account_sid=env_var.get("ACCOUNT_SID"),
        auth_token=env_var.get("AUTH_TOKEN"),
        phone_number=env_var.get("TWILIO_PHONE_NUMBER"),
    )


async def get_user_phone_number(current_user: UserTenantDB, user_id: str) -> str:
    """Retrieve user's phone number from database"""

    user_info = await current_user.async_db.customers.find_one({"customer_id": user_id})
    return user_info.get("phone_number")


@router.post("/send_whatsapp_message")
async def send_whatsapp_message(
    request,
    minio,

    current_user: UserTenantDB = Depends(get_tenant_info),
    template_id: Optional[str] = None,
    template_vars: Optional[dict] = None
) -> str:
    """
    Send a WhatsApp message using Twilio

    Args:
        user_id (str): ID of the recipient user
        message (str): Message content to send
        media_url (str, optional): URL of media to send with message
        current_user (UserTenantDB): Current user context with database access

    Returns:
        str: Message SID if successful

    Raises:
        Exception: If message sending fails
    """
    try:
        # Get configuration and user details
        config = await get_whatsapp_config(current_user)
        
        customer_whatsapp_number = current_user.db.customers.find_one(
            {"customer_id": request.user_id}
        ).get("whatsapp_number")

        # Initialize Twilio client
        client = Client(config.account_sid, config.auth_token)
        loggers.info(f"Recived request: \n{request}")

        if template_id:
            print(template_id)
            message_kwargs = {
                "from_": f"whatsapp:{config.phone_number}",
                "content_sid": template_id,
                "to": customer_whatsapp_number,
                "force_delivery": True,
            }
          
            if template_vars:
                print(template_vars)
                message_kwargs["content_variables"] =json.dumps( template_vars  )
            message = client.messages.create(**message_kwargs)
            loggers.info(f"WhatsApp message sent successfully with template: {message.sid}")
        else:

            # Prepare message parameters
            if (not request.message) and (not request.media_url):
                raise Exception("Message content or media is required")
            # Check if there's only one media URL and a message
            if request.message and request.media_url and len(request.media_url) == 1:
                # Send message with single media URL in one API call
                message = client.messages.create(
                    from_=f"whatsapp:{config.phone_number}",
                    to=customer_whatsapp_number,
                    body=request.message,
                    media_url=request.media_url[0].get("url"),
                    force_delivery=True,
                )
                loggers.info(f"WhatsApp message with media sent successfully: {message.sid}")
            else:
                # Handle text and media separately as before
                if request.message:
                    if len(request.message) < 5:
                        request.message = request.message + " " * (5 - len(request.message))
                        
                    loggers.info(f"Sending message: {request.message}")
                    message = client.messages.create(
                        from_=f"whatsapp:{config.phone_number}",
                        to=customer_whatsapp_number,
                        body=request.message,
                        force_delivery=True,
                    )
                    loggers.info(f"WhatsApp message sent successfully: {message.sid}")

                if request.media_url:
                    for item in request.media_url:
                        message = client.messages.create(
                            from_=f"whatsapp:{config.phone_number}",
                            to=customer_whatsapp_number,
                            media_url=item.get("url"),
                        )

            # Add to chat history
        agent_reply_ = ChatHistMessage(
            role="assistant",
            content=request.message,
            sender=current_user.user.id,
            created_at=datetime.now(),
            user_id=request.user_id,
            summary_id=None,
            id=None,
            verified=False,
            message_id=str(request.ai_response_id),
        )


        agent_reply=agent_reply_.model_dump_mongo()
        #dont make it nested
        if request.media_url:
            media_idss=[item.get("name") for item in request.media_url]
        else:
            media_idss=[]
        #check if it is nested


        agent_reply["media_ids"]=flatten_deep(media_idss)
        current_user.db.chat_messages.insert_one(agent_reply)
        if isinstance(request.ai_response_id, ObjectId):
            respone_id=request.ai_response_id
        else:
            respone_id=ObjectId(request.ai_response_id)
        if isinstance(request.ai_response_id, str):
            request.ai_response_id = ObjectId(request.ai_response_id)
        current_user.db.ai_response.update_one(
            {"_id": respone_id},
            {
                "$addToSet": {
                    "response.chat_data": agent_reply,
                    "response.chat_ids": agent_reply_.id,

                },
                "$set": {"created_at": datetime.now()},

            },
        )

        return message.sid

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise Exception(f"Failed to send WhatsApp message: {str(e)}")


def flatten_deep(data):
    result = []
    for item in data:
        if isinstance(item, list):
            result.extend(flatten_deep(item))
        else:
            result.append(item)
    return result