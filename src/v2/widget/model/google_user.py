from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional

class GoogleUser(BaseModel):
    customer_id: str = Field(example="1234567890")
    email: str = Field(example="<EMAIL>")
    name: str = <PERSON>(example="<PERSON>")
    profile_picture: str = Field(example="https://example.com/photo.jpg")
    tenant_id: str = Field(example="6799f0f28ea613739afe8dd8")
    created_at: Optional[datetime] = datetime.now()

    class Config:
        schema_extra = {
            "example": {
                "customer_id": "1234567890",
                "email": "<EMAIL>",
                "name": "<PERSON>",
                "profile_picture": "https://example.com/photo.jpg",
                "tenant_id": "6799f0f28ea613739afe8dd8"
            }
        }

    
    
    def model_dump_mongo(self,**kwargs):
        """
        Dumps the model data into a dictionary for serialization.

        Returns:
            dict: Serialized model data
        """
        dump_ = super().model_dump(**kwargs)

        # Handle None values
        if not dump_.get("created_at"):
            dump_["created_at"] = datetime.now().isoformat()

        dump_["customer_name"]=dump_.pop("name")
        
        
        return dump_
