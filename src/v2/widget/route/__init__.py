from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTP<PERSON>x<PERSON>, Query
from fastapi.responses import JSONResponse
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.v2.widget.model.google_user import GoogleUser
from src.core.database import get_db_from_tenant_id
from src.core.security import create_access_token
# import modelrunreques
from src.models.chat_hist import ModelRunRequest
from src.v2.widget.chat import handle_chat_widget
loggers = setup_new_logging(__name__)

widget_router = APIRouter(
    prefix="/widget",
    responses={404: {"description": "Not found"}},
)



@widget_router.post("/auth")
async def auth_widget(google_user: GoogleUser):
    tenant_db = get_db_from_tenant_id(google_user.tenant_id)
    

    # Check if user exists
    user = tenant_db.customers.find_one({"customer_id": google_user.customer_id, "email": google_user.email})
    if not user:
        # Exclude tenant_id when inserting
        user_data = google_user.model_dump_mongo(exclude={"tenant_id"})
        tenant_db.customers.insert_one(user_data)
        
        
    access_token = tenant_db.settings.find_one({"name": "env"}).get("widget_token")
    if not access_token:
        raise HTTPException(status_code=401, detail="Access token not found")

    return {"message": "User authenticated", "access_token": access_token, "username": google_user.name, "profile_picture": google_user.profile_picture,"user_id": google_user.customer_id}





@widget_router.post("/chat")
async def chat_widget(
    chat_request: ModelRunRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    return await handle_chat_widget(chat_request, current_user)  