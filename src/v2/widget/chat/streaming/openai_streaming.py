"""
OpenAI-based streaming response generator for chat widget.

This module provides the main entry point for generating streaming responses
using OpenAI's API with tool calling capabilities. It coordinates the various
components needed for a complete streaming response.

This implementation focuses on:
1. Accurate and efficient parallel processing of tool calls
2. Proper handling of complex object serialization
3. Robust error handling and recovery
4. Clean, maintainable code structure
"""
from typing import AsyncGenerator, Dict, Any, List, Set
import json
import openai
import asyncio
from datetime import datetime
from bson import ObjectId

from src.models.chat_hist import CurrentUserContext
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ModelRunRequest, ChatHistMessage
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.reply.minio_client import MinIOClient
from src.v2.widget.chat.utils.sse_utils import (
    format_and_send_sse,
    create_token_payload,
    create_source_nodes_payload,
    create_final_payload
)
from src.v2.widget.chat.utils.db_operations import (
    save_user_message,
    save_initial_response,
    update_message_references
)
from src.v2.widget.chat.utils.serialization import (
    serialize_message
)

# Import from local modules
from src.v2.widget.chat.streaming.api_client import (
    initialize_openai_client,
    call_openai_api
)
from src.v2.widget.chat.streaming.message_utils import (
    detect_language_and_product,
    get_system_prompt,
    prepare_messages
)
from src.v2.widget.chat.streaming.tool_handlers import (
    get_available_tools
)
from src.v2.widget.chat.streaming.db_operations import save_reply_to_database
from src.v2.widget.chat.processors.node_processor import SourceNodeProcessor

# Initialize logging
loggers = setup_new_logging(__name__)


async def generate_openai_sse(
    chat_request: ModelRunRequest,
    qd_client: Qdrant_Call,
    minio: MinIOClient,
    current_user: UserTenantDB
) -> AsyncGenerator[str, None]:
    """
    Generate Server-Sent Events (SSE) for streaming chat responses using OpenAI.

    This implementation:
    1. Performs language detection and product identification
    2. Formats the system prompt with this information
    3. Handles tool calls (like database search, booking management, etc.)
    4. Streams the response tokens back to the client
    5. Returns source nodes and other metadata with the final response

    Args:
        chat_request: The chat request containing the user's message
        qd_client: Qdrant client instance
        minio: MinIO client instance
        current_user: Current user tenant database

    Yields:
        SSE formatted strings containing response data
    """
    request_time = datetime.now()
    # Initialize variables for tracking
    image_names: Set[str] = set()
    chat_ids: List[str] = []
    CurrentUserContext.set_current_user(current_user)

    try:
        # Get formatted messages and image processing metadata
        chat_hist, latest_message, image_process_metadata = await chat_request.get_formatted_messages(current_user)

        # Ensure the latest message is properly handled
        loggers.info(f"Latest message content: {latest_message.content}")

        # Save user message
        inserted_message_id = await save_user_message(latest_message, current_user)
        request_msg_id = await save_initial_response(chat_request, latest_message, current_user)
        chat_ids.append(str(inserted_message_id))

        # Initialize variables
        source_nodes = []
        tool_calls = None
        tool_calls_results = []
        full_response = ""

        # Detect language and identify product
        detection_result = await detect_language_and_product(latest_message.content, current_user)
        language = detection_result["language"]
        identified_product = detection_result["identified_product"]
        lang_usage = detection_result["lang_usage"]
        prod_usage = detection_result["prod_usage"]
        # First yield - send initial metadata
        initial_metadata = {
            "type": "metadata",
            "identified_product": identified_product,
            "language": language
        }
        yield await format_and_send_sse(initial_metadata)

        # Get system prompt
        sys_prompt = await get_system_prompt(current_user, identified_product, language,chat_request.mode)
        if not sys_prompt:
            error_payload = {
                "type": "error",
                "message": "System prompt not found in database"
            }
            yield await format_and_send_sse(error_payload)
            return

        # Initialize OpenAI client
        openai_client = await initialize_openai_client(current_user)

        # Prepare messages for OpenAI
        response_mode_str="Provide a detailed and explained answer in a couple of sentences."
        PROMPT=sys_prompt["text"].format(identified_product=identified_product, language=language,response_mode_str=response_mode_str)

        # Log the latest message content before preparing messages
        loggers.info(f"Preparing messages with latest message: {latest_message.content}")

        messages = await prepare_messages(PROMPT, chat_hist, latest_message)

        # Log the number of messages and the last message content
        if messages:
            loggers.info(f"Prepared {len(messages)} messages for OpenAI API")
            loggers.info(f"Last message in conversation: {messages[-1]['content']}")

        # Get available tools
        from pprint import pprint
        loggers.info("Message structure for OpenAI API:")
        pprint(messages)

        tools = get_available_tools()

        # Make initial API call with streaming
        response = await call_openai_api(
            openai_client=openai_client,
            model=sys_prompt["model"],
            messages=messages,
            tools=tools,
            tool_choice="auto",
            parallel_tool_calls=True
        )

        if isinstance(response, dict) and response.get("type") == "error":
            yield await format_and_send_sse(response)
            return

        # Track token usage
        token_usage = {
            "model": sys_prompt["model"],
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0,
            "lang_usage": lang_usage,
            "identify_prod_usage": prod_usage
        }


        # Process the streaming response
        current_tool_calls = []
        current_content = ""

        # Stream initial response
        for chunk in response:
            delta = chunk.choices[0].delta

            # Handle content updates
            if delta.content:
                current_content += delta.content
                full_response += delta.content
                token_payload = await create_token_payload(delta.content, full_response)
                yield await format_and_send_sse(token_payload)

            # Handle tool calls
            if delta.tool_calls:
                for tool_call_delta in delta.tool_calls:
                    # Get the tool call index
                    index = tool_call_delta.index

                    # Ensure we have enough elements in our list
                    while len(current_tool_calls) <= index:
                        current_tool_calls.append({
                            "id": None,
                            "type": "function",
                            "function": {"name": "", "arguments": ""}
                        })

                    # Update the tool call with new information
                    if tool_call_delta.id:
                        current_tool_calls[index]["id"] = tool_call_delta.id

                    if tool_call_delta.function:
                        if tool_call_delta.function.name:
                            current_tool_calls[index]["function"]["name"] = tool_call_delta.function.name

                        if tool_call_delta.function.arguments:
                            current_tool_calls[index]["function"]["arguments"] += tool_call_delta.function.arguments

        # Process any tool calls that were received
        if current_tool_calls:
            tool_calls = current_tool_calls

            # Add the assistant's message with tool calls to the conversation
            assistant_message = {
                "role": "assistant",
                "content": current_content,
                "tool_calls": current_tool_calls
            }
            messages.append(assistant_message)

            # Process all tool calls in parallel for better performance
            try:
                # Import here to avoid circular import
                from src.v2.widget.chat.streaming.tool_handlers import process_tool_call

                # Create tasks for parallel execution
                tasks = []
                for tool_call in current_tool_calls:
                    task = process_tool_call(
                        tool_call=tool_call,
                        qd_client=qd_client,
                        minio=minio,
                        current_user=current_user,
                        chat_request=chat_request
                    )
                    tasks.append(task)

                # Execute all tasks in parallel
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Process results
                all_source_nodes = []

                for i, result_data in enumerate(results):
                    # Handle exceptions
                    if isinstance(result_data, Exception):
                        loggers.error(f"Error processing tool call: {str(result_data)}")
                        result = {"error": f"Error processing tool call: {str(result_data)}"}
                        function_name = current_tool_calls[i]["function"]["name"]
                        function_args = {}
                        processed_source_nodes = []
                    else:
                        result, function_name, function_args, processed_source_nodes = result_data

                    # Update source nodes if available
                    if processed_source_nodes:
                        all_source_nodes.extend(processed_source_nodes)

                    # Store the tool call result
                    tool_calls_results.append({
                        "result": result,
                        "function_name": function_name,
                        "function_args": function_args
                    })

                    # Add the tool result to the conversation
                    messages.append({
                        "role": "tool",
                        "name": function_name,
                        "content": json.dumps(result),
                        "tool_call_id": current_tool_calls[i]["id"]
                    })

                # If we have source nodes, process and send them
                if all_source_nodes:
                    # Get Qdrant config from database
                    env_var = current_user.db.settings.find_one({"name": "env"})
                    if env_var:
                        qd_config = env_var.get("qdrant_config", {})

                        # Create the processor
                        processor = SourceNodeProcessor(
                            qd_client=qd_client,
                            qd_config=qd_config,
                            minio=minio,
                            current_user=current_user
                        )

                        # Process source nodes
                        processed_result = await processor.process(all_source_nodes)
                        if processed_result and processed_result.processing_status == "completed":
                            # Update source_nodes with processed nodes
                            source_nodes = processed_result.source_nodes
                            loggers.info(f"Successfully processed {len(source_nodes)} source nodes for streaming")
                        else:
                            source_nodes = all_source_nodes
                            loggers.warning("Source node processing did not complete successfully for streaming")
                    else:
                        source_nodes = all_source_nodes
                        loggers.warning("Environment configuration not found, using unprocessed source nodes for streaming")

                    # Send source nodes to client
                    source_nodes_payload = await create_source_nodes_payload(source_nodes)
                    yield await format_and_send_sse(source_nodes_payload)

            except Exception as e:
                loggers.error(f"Error in parallel tool processing: {str(e)}")
                # Fall back to sequential processing if parallel fails
                for tool_call in current_tool_calls:

                    from src.v2.widget.chat.streaming.tool_handlers import process_tool_call
                    result, function_name, function_args, processed_source_nodes = await process_tool_call(
                        tool_call=tool_call,
                        qd_client=qd_client,
                        minio=minio,
                        current_user=current_user,
                        chat_request=chat_request
                    )

                    if processed_source_nodes:
                        # Get Qdrant config from database
                        env_var = current_user.db.settings.find_one({"name": "env"})
                        if env_var:
                            qd_config = env_var.get("qdrant_config", {})

                            # Create the processor
                            processor = SourceNodeProcessor(
                                qd_client=qd_client,
                                qd_config=qd_config,
                                minio=minio,
                                current_user=current_user
                            )

                            # Process source nodes
                            processed_result = await processor.process(processed_source_nodes)
                            if processed_result and processed_result.processing_status == "completed":
                                # Update source_nodes with processed nodes
                                source_nodes = processed_result.source_nodes
                                loggers.info(f"Successfully processed {len(source_nodes)} source nodes in fallback mode")
                            else:
                                source_nodes = processed_source_nodes
                                loggers.warning("Source node processing did not complete successfully in fallback mode")
                        else:
                            source_nodes = processed_source_nodes
                            loggers.warning("Environment configuration not found, using unprocessed source nodes in fallback mode")

                        # Send source nodes to client
                        source_nodes_payload = await create_source_nodes_payload(source_nodes)
                        yield await format_and_send_sse(source_nodes_payload)

                    tool_calls_results.append({
                        "result": result,
                        "function_name": function_name,
                        "function_args": function_args
                    })

                    messages.append({
                        "role": "tool",
                        "name": function_name,
                        "content": json.dumps(result),
                        "tool_call_id": tool_call["id"]
                    })

            # Make a follow-up call to get the final response
            # Don't include tools or tool_choice for the follow-up call
            follow_up_response = await call_openai_api(
                openai_client=openai_client,
                model=sys_prompt["model"],
                messages=messages,
                tools=tools,  # No tools for follow-up
                tool_choice="auto",  # No tool_choice when tools is None
                parallel_tool_calls=True  # No parallel tool calls when tools is None
            )

            if isinstance(follow_up_response, dict) and follow_up_response.get("type") == "error":
                yield await format_and_send_sse(follow_up_response)
                return

            # Reset the full response for the follow-up
            full_response = ""

            # Stream the follow-up response
            for chunk in follow_up_response:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    token_payload = await create_token_payload(content, full_response)
                    yield await format_and_send_sse(token_payload)

        # Save assistant reply
        try:
            inserted_reply_id = await save_reply_to_database(
                full_response=full_response,
                image_names=image_names,
                latest_message=latest_message,
                current_user=current_user
            )
            chat_ids.append(str(inserted_reply_id))
        except Exception as e:
            loggers.error(f"Error saving reply or updating database: {str(e)}")

        # Final yield - complete response with all metadata
        final_payload = await create_final_payload(
            full_response=full_response,
            source_nodes=source_nodes,  # This now contains processed source nodes
            metadata=["url"]
        )

        # Process source nodes with SourceNodeProcessor
        try:
            # Get Qdrant config from database
            env_var = current_user.db.settings.find_one({"name": "env"})
            if env_var:
                qd_config = env_var.get("qdrant_config", {})

                # Process source nodes if they exist
                if source_nodes:
                    loggers.info(f"Processing {len(source_nodes)} source nodes before database update")

                    # Create the processor
                    processor = SourceNodeProcessor(
                        qd_client=qd_client,
                        qd_config=qd_config,
                        minio=minio,
                        current_user=current_user
                    )

                    # Process source nodes
                    processed_result = await processor.process(source_nodes)
                    if processed_result and processed_result.processing_status == "completed":
                        # Update source_nodes with processed nodes
                        source_nodes = processed_result.source_nodes
                        loggers.info(f"Successfully processed {len(source_nodes)} source nodes")
                    else:
                        loggers.warning("Source node processing did not complete successfully")
            else:
                loggers.warning("Environment configuration not found, skipping source node processing")

            # Format tool_calls_results for information_gathering field
            information_gathering = []
            for tool_call in tool_calls_results:
                information_gathering.append({
                    "function_name": tool_call.get("function_name", ""),
                    "function_args": tool_call.get("function_args", {}),
                    "result": tool_call.get("result", {})
                })

            # Create call_to_action list from tool_calls_results
            call_to_action = []
            for tool_call in tool_calls_results:
                function_name = tool_call.get("function_name", "")
                function_args = tool_call.get("function_args", {})

                if function_name == "create_issue_tickets":
                    call_to_action.append({
                        "id": function_args.get("cta_id", ""),
                        "type": function_args.get("issue_type", ""),
                        "status": "open"
                    })
                elif function_name == "handle_booking":
                    call_to_action.append({
                        "id": function_args.get("cta_id", ""),
                        "type": function_args.get("issue_type", "CTAType.BOOKING"),
                        "status": "open"
                    })
            # Prepare chat data for the response

            # Prepare chat data with both user message and assistant reply
            latest_message_dump = latest_message.model_dict_format() if hasattr(latest_message, "model_dict_format") else serialize_message(latest_message)

            # Create assistant reply message
            assistant_reply = ChatHistMessage.format_message(
                role="assistant",
                content=full_response,
                media_ids=[],
                media_values="",
                user_id=latest_message.user_id
            )
            assistant_reply_dump = assistant_reply.model_dict_format() if hasattr(assistant_reply, "model_dict_format") else serialize_message(assistant_reply)

            # Combine user message and assistant reply in chat_data
            chat_data = [latest_message_dump, assistant_reply_dump]

            # Log chat data for debugging
            loggers.info(f"Chat data prepared with {len(chat_data)} messages")


            # Create the final response object
            final_response_obj = {
                "request": chat_request.model_dump() if hasattr(chat_request, "model_dump") else chat_request,
                "response": {
                    "request_time": request_time,
                    "processing_time": (datetime.now() - request_time).total_seconds(),
                    "reply": full_response,
                    "identified_product": identified_product,
                    "information_gathering": information_gathering,
                    "chat_ids": chat_ids,
                    "chat_data": chat_data,  # Use the combined chat data
                    "latest_message": latest_message.content,
                    "metadata": [],
                    "language": language,
                    "background_processing": True,
                    "background_processing_completed": False,
                    "reply_urls": [],
                    "source_nodes": source_nodes,
                    "call_to_action": call_to_action,
                    "usage": {"prompt_tokens": token_usage,"image_process_cost": image_process_metadata},
                },
                "created_at": datetime.now(),
            }
            loggers.log_dict_as_table(
            final_response_obj, f"Results for UserID: {chat_request.user_id}: "
        )

            # Update the response in the database
            current_user.db.ai_response.update_one(
                {"_id": ObjectId(request_msg_id)},
                {"$set": final_response_obj}
            )

            # Update CTA records with response_id
            for tool_call in tool_calls_results:
                if tool_call.get("function_name") == "create_issue_tickets":
                    cta_id = tool_call.get("function_args", {}).get("cta_id")
                    if cta_id:
                        current_user.db.cta.update_one(
                            {"_id": ObjectId(cta_id)},
                            {"$set": {"response_id": str(request_msg_id)}}
                        )
                        loggers.info(f"Updated CTA {cta_id} with response_id {request_msg_id}")
                elif tool_call.get("function_name") == "handle_booking":
                    cta_id = tool_call.get("function_args", {}).get("cta_id")
                    if cta_id:
                        current_user.db.cta.update_one(
                            {"_id": ObjectId(cta_id)},
                            {"$set": {"response_id": str(request_msg_id)}}
                        )
                        loggers.info(f"Updated booking CTA {cta_id} with response_id {request_msg_id}")

            loggers.info(f"Updated final response with {len(information_gathering)} information gathering items and {len(call_to_action)} call to action items")
        except Exception as e:
            loggers.error(f"Error serializing data for database update: {str(e)}")
            # Continue execution even if database update fails

        # Add additional metadata to final payload
        final_payload["tool_calls"] = tool_calls
        final_payload["tool_calls_results"] = tool_calls_results
        final_payload["identified_product"] = identified_product
        final_payload["language"] = language
        final_payload["token_usage"] = token_usage
        final_payload["information_gathering"] = information_gathering
        final_payload["call_to_action"] = call_to_action
        final_payload["image_process_cost"] = image_process_metadata

        # Send final payload
        yield await format_and_send_sse(final_payload)


        # Update message references
        await update_message_references(
            inserted_reply_id,
            inserted_message_id,
            request_msg_id,
            current_user
        )
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        loggers.error(f"Unexpected error in generate_openai_sse: {str(e)}\n{error_trace}")

        # Create a more informative error payload
        error_payload = {
            "type": "error",
            "message": f"Unexpected error: {str(e)}",
            "error_type": e.__class__.__name__,
            "context": "Error occurred during response generation"
        }

        # Try to provide more specific error information
        if "cannot encode object" in str(e):
            error_payload["message"] = "Error serializing data. This is likely due to a complex object that cannot be converted to JSON."
            error_payload["context"] = "JSON serialization error"
        elif "connection" in str(e).lower():
            error_payload["message"] = "Connection error when communicating with external services."
            error_payload["context"] = "Network error"

        yield await format_and_send_sse(error_payload)



