"""
Tool handlers for OpenAI streaming chat.

This module defines the available tools for OpenAI API and provides
functions to process tool calls and execute the appropriate functions.
"""
from typing import Any, Dict, List, Tuple
import json

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ModelRunRequest
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.reply.minio_client import MinIOClient
from src.v2.widget.chat.processors.node_processor import SourceNodeProcessor
from src.v2.dashboard.cta.models import CTAType
from src.streamlitdemo.chatdemo.respond import MsgRequest
from src.streamlitdemo.chatdemo.simulate_tools import (
    initial_address_information,
    create_issue_tickets,
    handle_booking
)
from src.streamlitdemo.chatdemo.gen_query_n_reply import generate_answer_from_query

# Initialize logging
loggers = setup_new_logging(__name__)


def get_available_tools() -> List[Dict[str, Any]]:
    """
    Define available tools for OpenAI API.

    Returns:
        List of tool definitions
    """
    return [
        {
            "type": "function",
            "function": {
                "name": "search_database",
                "description": "Retrieve answer from the database based on a given query.",
                "strict": True,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query to find relevant data in the database."
                        }
                    },
                    "required": ["query"],
                    "additionalProperties": False
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "initial_address_information",
                "description": "Information to greet the user with in the beginning of a new conversation.",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "handle_booking",
                "description": "Handle booking-related queries.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "customer_name": {
                            "type": "string",
                            "description": "The name of the customer to book the appointment under.",
                        },
                        "customer_phone_no": {
                            "type": "string",
                            "description": "The phone number of the customer to book the appointment for.",
                        },
                        "customer_age": {
                            "type": "string",
                            "description": "The age of the customer to book the appointment under.",
                        },
                        "customer_medical_history": {
                            "type": "string",
                            "description": "The medical history of the customer.",
                        },
                        "description": {
                            "type": "string",
                            "description": "Detailed Description of the booking request.",
                        },
                    },
                    "required": ["customer_name", "customer_phone_no", "customer_age",
                                "customer_medical_history", "description"],
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "create_issue_tickets",
                "description": "Create a new issue ticket. If issue_type is not specified, it will default to 'ticket'.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "Short title for the issue"
                        },
                        "issue_type": {
                            "type": "string",
                            "enum": [i.value for i in CTAType],
                            "description": "Type of issue (optional, defaults to 'ticket' if not provided)"
                        },
                        "description": {
                            "type": "string",
                            "description": "Detailed description of the issue"
                        },
                    },
                    "required": ["name", "description"]
                }
            }
        },
    ]


async def process_tool_call(
    tool_call: Dict[str, Any],
    qd_client: Qdrant_Call,
    minio: MinIOClient,
    current_user: UserTenantDB,
    chat_request: ModelRunRequest
) -> Tuple[Any, str, Dict[str, Any], List[Any]]:
    """
    Process a tool call and execute the appropriate function.

    Args:
        tool_call: The tool call to process
        qd_client: Qdrant client instance
        minio: MinIO client instance
        current_user: Current user tenant database
        chat_request: The chat request containing the user's message

    Returns:
        Tuple containing:
        - result: The result of the function call
        - function_name: The name of the function that was called
        - function_args: The arguments that were passed to the function
        - source_nodes: Any source nodes that were retrieved (for search_database)
    """
    function_name = tool_call["function"]["name"]
    try:
        function_args = json.loads(tool_call["function"]["arguments"])
    except json.JSONDecodeError:
        loggers.error(f"Invalid JSON in tool call arguments: {tool_call['function']['arguments']}")
        function_args = {}

    # Initialize result and source_nodes
    result = None
    source_nodes = None

    # Execute the appropriate function based on the tool call
    if function_name == "search_database":
        result, nodes = generate_answer_from_query(
            query=function_args.get("query", ""),
            qd_client=qd_client,
            current_user=current_user
        )
        source_nodes = nodes

        # Process source nodes if available
        if source_nodes:
            try:
                # Get Qdrant config from database
                qd_config = current_user.db.settings.find_one({"name": "env"}).get("qdrant_config")

                # Create the processor
                processor = SourceNodeProcessor(
                    qd_client=qd_client,
                    qd_config=qd_config,
                    minio=minio,
                    current_user=current_user
                )

                # Process source nodes
                processed_result = await processor.process(source_nodes)
                if processed_result and processed_result.processing_status == "completed":
                    source_nodes = processed_result.source_nodes
            except Exception as e:
                loggers.error(f"Error processing source nodes: {str(e)}")

    elif function_name == "initial_address_information":
        result = initial_address_information(db=current_user.db)

    elif function_name == "create_issue_tickets":
        # Create a MsgRequest object from chat_request
        msg_request = MsgRequest(
            customer_id=chat_request.user_id,
            message=chat_request.message,
            message_media_values=chat_request.media_values if hasattr(chat_request, 'media_values') else None,
            channel=chat_request.channel
        )

        result = create_issue_tickets(
            function_args.get("name", ""),
            function_args.get("issue_type", ""),
            function_args.get("description", ""),
            msg_request,  # Pass the properly formatted MsgRequest object
            current_user,
            chat_request.channel
        )
        function_args["cta_id"] = result.get("cta_id")

    elif function_name == "handle_booking":
        # Create a MsgRequest object from chat_request
        msg_request = MsgRequest(
            customer_id=chat_request.user_id,
            message=chat_request.message,
            message_media_values=chat_request.media_values if hasattr(chat_request, 'media_values') else None,
            channel=chat_request.channel
        )

        result, data = handle_booking(
            function_args.get("customer_name"),
            function_args.get("customer_phone_no"),
            function_args.get("customer_age"),
            function_args.get("customer_medical_history"),
            function_args.get("description"),
            msg_request,  # Pass the properly formatted MsgRequest object
            current_user,
        )
        function_args["cta_id"] = data.get("cta_id")
        function_args["issue_type"] = CTAType.BOOKING.value

    else:
        result = {"error": f"Unknown function: {function_name}"}

    return result, function_name, function_args, source_nodes
