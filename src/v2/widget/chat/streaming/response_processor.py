"""
Response processor for OpenAI streaming chat.

This module provides functions to process streaming responses from OpenAI API,
including handling tool calls and formatting responses.
"""
from typing import Dict, Any, List

from src.helper.logger import setup_new_logging
from src.v2.widget.chat.utils.sse_utils import (
    format_and_send_sse,
    create_token_payload,
    create_source_nodes_payload,
    create_final_payload
)

# Initialize logging
loggers = setup_new_logging(__name__)


async def create_initial_metadata_payload(identified_product: str, language: str) -> Dict[str, Any]:
    """
    Create initial metadata payload for SSE.
    
    Args:
        identified_product: Identified product from user message
        language: Detected language from user message
        
    Returns:
        Metadata payload for SSE
    """
    return {
        "type": "metadata",
        "identified_product": identified_product,
        "language": language
    }


async def create_token_stream_payload(content: str, full_response: str) -> Dict[str, Any]:
    """
    Create token stream payload for SSE.
    
    Args:
        content: Current token content
        full_response: Full response so far
        
    Returns:
        Token payload for SSE
    """
    return await create_token_payload(content, full_response)


async def create_source_nodes_stream_payload(source_nodes: List[Any]) -> Dict[str, Any]:
    """
    Create source nodes payload for SSE.
    
    Args:
        source_nodes: Source nodes to include in the payload
        
    Returns:
        Source nodes payload for SSE
    """
    return await create_source_nodes_payload(source_nodes)


async def create_complete_response_payload(
    full_response: str,
    source_nodes: List[Any],
    tool_calls: Any,
    tool_calls_results: List[Dict[str, Any]],
    identified_product: str,
    language: str,
    token_usage: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Create complete response payload for SSE.
    
    Args:
        full_response: Full response text
        source_nodes: Source nodes to include in the payload
        tool_calls: Tool calls made during the conversation
        tool_calls_results: Results of tool calls
        identified_product: Identified product from user message
        language: Detected language from user message
        token_usage: Token usage information
        
    Returns:
        Complete response payload for SSE
    """
    # Create base payload
    final_payload = await create_final_payload(
        full_response=full_response,
        source_nodes=source_nodes,
        metadata=["url"]
    )
    
    # Add additional metadata
    final_payload["tool_calls"] = tool_calls
    final_payload["tool_calls_results"] = tool_calls_results
    final_payload["identified_product"] = identified_product
    final_payload["language"] = language
    final_payload["token_usage"] = token_usage
    
    return final_payload
