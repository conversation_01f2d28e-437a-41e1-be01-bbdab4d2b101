"""
Database operations for OpenAI streaming chat.

This module provides functions to save and retrieve data from the database,
including saving user messages and assistant replies.
"""
from typing import Set

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ChatHistMessage
from src.v2.widget.chat.utils.db_operations import save_assistant_reply

# Initialize logging
loggers = setup_new_logging(__name__)


async def save_reply_to_database(
    full_response: str,
    image_names: Set[str],
    latest_message: ChatHistMessage,
    current_user: UserTenantDB
) -> str:
    """
    Save assistant reply to database.
    
    Args:
        full_response: The full text response from the assistant
        image_names: Set of image names to include with the reply
        latest_message: The latest user message
        current_user: Current user tenant database
        
    Returns:
        ID of the inserted reply
    """
    # Format the reply message
    reply = ChatHistMessage.format_message(
        role="assistant",
        content=full_response,
        media_ids=list(image_names),
        media_values="",
        user_id=latest_message.user_id,
    )
    
    # Save the reply to the database
    inserted_reply_id = await save_assistant_reply(reply, image_names, current_user)
    
    return inserted_reply_id
