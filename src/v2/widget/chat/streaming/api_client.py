"""
OpenAI API client for streaming chat.

This module provides functions to initialize the OpenAI client
and make API calls with error handling.
"""
from typing import Any, Dict, List

import openai

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB

# Initialize logging
loggers = setup_new_logging(__name__)


async def initialize_openai_client(current_user: UserTenantDB) -> openai.OpenAI:
    """
    Initialize OpenAI client with API key from database.

    Args:
        current_user: Current user tenant database

    Returns:
        Initialized OpenAI client
    """
    env = current_user.db.settings.find_one({"name": "env"})
    api_keys = env.get("config")
    return openai.OpenAI(api_key=api_keys.get("OPENAI_API_KEY"))


async def call_openai_api(
    openai_client: openai.OpenAI,
    model: str,
    messages: List[Dict[str, str]],
    tools: List[Dict[str, Any]] = None,
    tool_choice: str = "auto",
    parallel_tool_calls: bool = True
) -> Any:
    """
    Call OpenAI API with error handling.

    Args:
        openai_client: Initialized OpenAI client
        model: Model to use for completion
        messages: List of messages for the conversation
        tools: List of available tools
        tool_choice: Tool choice strategy
        parallel_tool_calls: Whether to allow parallel tool calls

    Returns:
        API response or error payload
    """
    try:
        # Build parameters dictionary
        params = {
            "model": model,
            "messages": messages,
            "stream": True  # Enable streaming
        }

        # Only add tools and tool_choice if tools are provided
        if tools:
            params["tools"] = tools
            params["tool_choice"] = tool_choice

            # Only add parallel_tool_calls if tools are provided
            if parallel_tool_calls:
                params["parallel_tool_calls"] = parallel_tool_calls

        # Make the API call with the constructed parameters
        return openai_client.chat.completions.create(**params)
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        loggers.error(f"Error calling OpenAI API: {str(e)}\n{error_trace}")
        return {
            "type": "error",
            "message": f"Error calling OpenAI API: {str(e)}"
        }
