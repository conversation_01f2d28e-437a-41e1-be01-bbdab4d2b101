"""
Parallel tool processor for OpenAI streaming chat.

This module provides utilities for processing tool calls in parallel,
improving performance when multiple tools need to be executed.
"""
from typing import Any, Dict, List, Tuple, Optional
import json
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ModelRunRequest
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.reply.minio_client import MinIOClient
from src.v2.widget.chat.streaming.tool_handlers import process_tool_call

# Initialize logging
loggers = setup_new_logging(__name__)


async def process_tool_calls_parallel(
    tool_calls: List[Dict[str, Any]],
    qd_client: Qdrant_Call,
    minio: MinIOClient,
    current_user: UserTenantDB,
    chat_request: ModelRunRequest
) -> Tuple[List[Dict[str, Any]], List[Any]]:
    """
    Process multiple tool calls in parallel.
    
    Args:
        tool_calls: List of tool calls to process
        qd_client: Qdrant client instance
        minio: MinIO client instance
        current_user: Current user tenant database
        chat_request: The chat request containing the user's message
        
    Returns:
        Tuple containing:
        - List of tool call results
        - List of source nodes from all tool calls
    """
    if not tool_calls:
        return [], []
    
    # Create tasks for all tool calls
    tasks = []
    for tool_call in tool_calls:
        task = process_tool_call(
            tool_call=tool_call,
            qd_client=qd_client,
            minio=minio,
            current_user=current_user,
            chat_request=chat_request
        )
        tasks.append(task)
    
    # Execute all tasks in parallel
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    tool_call_results = []
    all_source_nodes = []
    
    for i, (result, function_name, function_args, source_nodes) in enumerate(results):
        # Handle exceptions
        if isinstance(result, Exception):
            loggers.error(f"Error processing tool call: {str(result)}")
            result = {"error": f"Error processing tool call: {str(result)}"}
            function_name = tool_calls[i]["function"]["name"]
            function_args = {}
            source_nodes = []
        
        # Store the tool call result
        tool_call_results.append({
            "result": result,
            "function_name": function_name,
            "function_args": function_args
        })
        
        # Collect source nodes
        if source_nodes:
            all_source_nodes.extend(source_nodes)
    
    return tool_call_results, all_source_nodes


def create_tool_messages(
    tool_calls: List[Dict[str, Any]],
    tool_call_results: List[Dict[str, Any]]
) -> List[Dict[str, Any]]:
    """
    Create tool messages for the conversation history.
    
    Args:
        tool_calls: List of tool calls
        tool_call_results: List of tool call results
        
    Returns:
        List of tool messages
    """
    tool_messages = []
    
    for i, result_data in enumerate(tool_call_results):
        if i >= len(tool_calls):
            continue
            
        tool_call = tool_calls[i]
        result = result_data["result"]
        function_name = result_data["function_name"]
        
        tool_messages.append({
            "role": "tool",
            "name": function_name,
            "content": json.dumps(result),
            "tool_call_id": tool_call.get("id")
        })
    
    return tool_messages
