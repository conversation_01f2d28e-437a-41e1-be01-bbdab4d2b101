"""
Example of using the OpenAI streaming response generator.

This module demonstrates how to use the OpenAI streaming response generator
in a FastAPI route handler.
"""
from fastapi import APIRouter, Depends, Request
from fastapi.responses import StreamingResponse
from typing import Dict, Any

from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.chat_hist import ModelRunRequest
from src.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.reply.minio_client import MinIOClient, MinIOConfig
from src.v2.widget.chat.streaming.openai_streaming import generate_openai_sse
from src.helper.logger import setup_new_logging

# Initialize logging
loggers = setup_new_logging(__name__)

# Create an APIRouter instance
openai_streaming_router = APIRouter(
    prefix="/openai-streaming",
    tags=["OpenAI Streaming"],
)


@openai_streaming_router.post("/chat")
async def openai_streaming_chat(
    request: Request,
    chat_request: ModelRunRequest,
    current_user: UserTenantDB = Depends(get_tenant_info),
) -> StreamingResponse:
    """
    Handle chat requests using OpenAI streaming response generator.
    
    Args:
        request: The HTTP request
        chat_request: The chat request containing the user's message
        current_user: Current user tenant database
        
    Returns:
        StreamingResponse with Server-Sent Events
    """
    try:
        # Get environment variables from database
        env = current_user.db.settings.find_one({"name": "env"})
        if not env:
            return StreamingResponse(
                content=iter([b"data: {\"error\": \"Environment not found\"}\n\n"]),
                media_type="text/event-stream",
            )
        
        # Get Qdrant configuration
        qdrant_config = env.get("qdrant_config")
        if not qdrant_config:
            return StreamingResponse(
                content=iter([b"data: {\"error\": \"Qdrant configuration not found\"}\n\n"]),
                media_type="text/event-stream",
            )
        
        # Get MinIO configuration
        minio_config = env.get("minio_config")
        if not minio_config:
            return StreamingResponse(
                content=iter([b"data: {\"error\": \"MinIO configuration not found\"}\n\n"]),
                media_type="text/event-stream",
            )
        
        # Initialize Qdrant client
        qd_client = Qdrant_Call(
            config=QdrantConfig(
                host=qdrant_config.get("host"),
                port=qdrant_config.get("port"),
                coll_name=qdrant_config.get("sentence_collection"),
            )
        )
        
        # Initialize MinIO client
        minio = MinIOClient(
            MinIOConfig(
                endpoint=minio_config.get("endpoint"),
                access_key=minio_config.get("access_key"),
                secret_key=minio_config.get("secret_key"),
                secure=minio_config.get("secure"),
            )
        )
        
        # Generate streaming response
        return StreamingResponse(
            content=generate_openai_sse(
                chat_request=chat_request,
                qd_client=qd_client,
                minio=minio,
                current_user=current_user,
            ),
            media_type="text/event-stream",
        )
    
    except Exception as e:
        loggers.error(f"Error in openai_streaming_chat: {str(e)}")
        return StreamingResponse(
            content=iter([f"data: {{\"error\": \"{str(e)}\"}}\n\n".encode()]),
            media_type="text/event-stream",
        )


# Example of how to include this router in a main application
"""
from fastapi import FastAPI
from src.v2.widget.chat.examples.openai_streaming_example import openai_streaming_router

app = FastAPI()
app.include_router(openai_streaming_router)
"""
