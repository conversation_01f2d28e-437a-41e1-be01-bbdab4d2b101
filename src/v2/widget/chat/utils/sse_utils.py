"""
Utility functions for Server-Sent Events (SSE) in the chat widget.
"""
import json
from typing import Any, Dict


async def sse_event(data: str, event: str = None) -> str:
    """
    Format a string as an SSE event.

    Args:
        data: The data to include in the event
        event: Optional event type

    Returns:
        Formatted SSE event string
    """
    msg = ""
    if event:
        msg += f"event: {event}\n"
    msg += f"data: {data}\n\n"
    return msg


async def create_token_payload(token: str, full_response: str) -> Dict[str, Any]:
    """
    Create a payload for a token update event.

    Args:
        token: The new token to send
        full_response: The full response so far

    Returns:
        Dictionary containing the token payload
    """
    return {
        "type": "token",
        "token": token,
        "response": full_response
    }


async def create_source_nodes_payload(source_nodes: list) -> Dict[str, Any]:
    """
    Create a payload for source nodes update event.

    Args:
        source_nodes: List of source nodes

    Returns:
        Dictionary containing the source nodes payload
    """
    return {
        "type": "source_nodes",
        "source_nodes": source_nodes
    }


async def create_final_payload(
    full_response: str,
    source_nodes: list = None,
    metadata: list = None
) -> Dict[str, Any]:
    """
    Create a payload for the final response event.

    Args:
        full_response: The complete response text
        source_nodes: Optional list of source nodes
        metadata: Optional list of metadata fields

    Returns:
        Dictionary containing the final payload
    """
    return {
        "response": full_response,
        "source_nodes": source_nodes or [],
        "metadata": metadata or ["url"]
    }


async def format_and_send_sse(payload: Dict[str, Any], event: str = "update") -> str:
    """
    Format a payload as JSON and send it as an SSE event.

    Args:
        payload: The payload to send
        event: The event type

    Returns:
        Formatted SSE event string
    """
    try:
        json_data = json.dumps(payload)
        return await sse_event(json_data, event)
    except Exception as e:
        # Fallback to a simpler payload if JSON serialization fails
        error_payload = {
            "type": "error",
            "message": f"Error formatting payload: {str(e)}"
        }
        return await sse_event(json.dumps(error_payload), event)
