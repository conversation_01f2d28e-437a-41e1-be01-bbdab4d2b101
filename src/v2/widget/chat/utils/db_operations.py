"""
Database operations for the chat widget.

This module provides functions for saving and updating chat-related data in the database.
It includes robust error handling and serialization of complex objects.
"""
from datetime import datetime
import traceback
import json
from typing import Dict, List, Any, Optional, Union, Set
from bson import ObjectId

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ChatHistMessage, ModelRunRequest

# Initialize logging
loggers = setup_new_logging(__name__)


async def save_initial_response(
    chat_request: ModelRunRequest,
    latest_message: ChatHistMessage,
    current_user: UserTenantDB
) -> ObjectId:
    """
    Save the initial response to the database.

    Args:
        chat_request: The chat request
        latest_message: The latest message from the user
        current_user: The current user tenant

    Returns:
        The ObjectId of the inserted response
    """
    try:
        request_time = datetime.now()
        latest_message_dump = latest_message.model_dump_mongo()
        latest_message_dump["_id"]=str(latest_message_dump["_id"])
        latest_message_dump.pop("image_process_metadata",{})
        # Create initial response object
        response = {
            "request": chat_request.model_dump(),
            "response": {
                "request_time": request_time,
                "processing_time": 0.0,
                "reply": " ",
                "identified_product": "",
                "information_gathering": [],
                "chat_ids": [latest_message_dump],
                "chat_data": [],
                "latest_message": latest_message.content,
                "metadata": [],
                "identified_product": [],
                "language": " ",
                "reply_urls": [],
                "source_nodes": [],
                "call_to_action": [],
            },
            "created_at": datetime.now(),
        }

        # Insert response and return ID
        pre_save = current_user.db.ai_response.insert_one(response)
        request_msg_id = pre_save.inserted_id
        loggers.info(f"Saved initial response with ID: {request_msg_id}")

        return request_msg_id
    except Exception as e:
        loggers.error(f"Error saving initial response: {str(e)}")
        raise


async def save_user_message(
    latest_message: ChatHistMessage,
    current_user: UserTenantDB
) -> ObjectId:
    """
    Save the user's message to the database.

    Args:
        latest_message: The latest message from the user
        current_user: The current user tenant

    Returns:
        The ObjectId of the inserted message
    """
    try:
        # Try to use the model_dump_mongo method
        inserted_message = current_user.db.chat_messages.insert_one(
            latest_message.model_dump_mongo()
        )
        loggers.info(f"Saved user message with ID: {inserted_message.inserted_id}")
        return inserted_message.inserted_id
    except Exception as e:
        error_trace = traceback.format_exc()
        loggers.error(f"Error saving user message: {str(e)}\n{error_trace}")

        # Fallback approach
        try:
            # Try to create a serializable dictionary
            if hasattr(latest_message, "model_dump"):
                message_dict = latest_message.model_dump()
            elif hasattr(latest_message, "__dict__"):
                message_dict = {k: v for k, v in latest_message.__dict__.items() if not k.startswith('_')}
            else:
                # Create a minimal dictionary with required fields
                message_dict = {
                    "role": "user",
                    "content": getattr(latest_message, "content", ""),
                    "user_id": getattr(latest_message, "user_id", ""),
                    "created_at": datetime.now()
                }

            # Try to serialize to JSON and back to ensure it's fully serializable
            json_str = json.dumps(message_dict, default=lambda o: str(o) if isinstance(o, (datetime, ObjectId)) else o.__dict__ if hasattr(o, "__dict__") else str(o))
            message_dict = json.loads(json_str)

            # Insert into database
            inserted_message = current_user.db.chat_messages.insert_one(message_dict)
            loggers.info(f"Saved user message with fallback method: {inserted_message.inserted_id}")
            return inserted_message.inserted_id
        except Exception as nested_e:
            nested_trace = traceback.format_exc()
            loggers.error(f"Error in fallback save for user message: {str(nested_e)}\n{nested_trace}")

            # Last resort fallback - create a minimal document
            try:
                minimal_message = {
                    "role": "user",
                    "content": str(getattr(latest_message, "content", "Error retrieving content")),
                    "user_id": str(getattr(latest_message, "user_id", "")),
                    "created_at": datetime.now(),
                    "error": "Created from fallback due to serialization error"
                }
                inserted_message = current_user.db.chat_messages.insert_one(minimal_message)
                loggers.info(f"Saved minimal user message: {inserted_message.inserted_id}")
                return inserted_message.inserted_id
            except Exception as final_e:
                loggers.error(f"Critical error in minimal fallback for user message: {str(final_e)}")
                raise


async def save_assistant_reply(
    reply: ChatHistMessage,
    image_names: Set[str],
    current_user: UserTenantDB
) -> ObjectId:
    """
    Save the assistant's reply to the database.

    Args:
        reply: The assistant's reply message
        image_names: Set of image names to include in the reply
        current_user: The current user tenant

    Returns:
        The ObjectId of the inserted reply
    """
    try:
        # Set media IDs
        reply.media_ids = list(image_names)

        # Insert reply
        inserted_reply = current_user.db.chat_messages.insert_one(
            reply.model_dump_mongo()
        )
        loggers.info(f"Saved assistant reply with ID: {inserted_reply.inserted_id}")

        return inserted_reply.inserted_id
    except Exception as e:
        error_trace = traceback.format_exc()
        loggers.error(f"Error saving assistant reply: {str(e)}\n{error_trace}")

        # Fallback approach if the model_dump_mongo method fails
        try:
            # Try to create a serializable dictionary
            if hasattr(reply, "model_dump_mongo"):
                reply_dict = reply.model_dump_mongo()
            elif hasattr(reply, "model_dump"):
                reply_dict = reply.model_dump()
            elif hasattr(reply, "__dict__"):
                reply_dict = {k: v for k, v in reply.__dict__.items() if not k.startswith('_')}
            else:
                # Create a minimal dictionary with required fields
                reply_dict = {
                    "role": "assistant",
                    "content": getattr(reply, "content", ""),
                    "user_id": getattr(reply, "user_id", ""),
                    "created_at": datetime.now()
                }

            # Ensure media_ids is set
            reply_dict["media_ids"] = list(image_names)

            # Try to serialize to JSON and back to ensure it's fully serializable
            json_str = json.dumps(reply_dict, default=lambda o: str(o) if isinstance(o, (datetime, ObjectId)) else o.__dict__ if hasattr(o, "__dict__") else str(o))
            reply_dict = json.loads(json_str)

            # Insert into database
            inserted_reply = current_user.db.chat_messages.insert_one(reply_dict)
            loggers.info(f"Saved assistant reply with fallback method: {inserted_reply.inserted_id}")
            return inserted_reply.inserted_id
        except Exception as nested_e:
            nested_trace = traceback.format_exc()
            loggers.error(f"Error in fallback save: {str(nested_e)}\n{nested_trace}")

            # Last resort fallback - create a minimal document
            try:
                minimal_reply = {
                    "role": "assistant",
                    "content": str(getattr(reply, "content", "Error retrieving content")),
                    "user_id": str(getattr(reply, "user_id", "")),
                    "created_at": datetime.now(),
                    "media_ids": list(image_names),
                    "error": "Created from fallback due to serialization error"
                }
                inserted_reply = current_user.db.chat_messages.insert_one(minimal_reply)
                loggers.info(f"Saved minimal assistant reply: {inserted_reply.inserted_id}")
                return inserted_reply.inserted_id
            except Exception as final_e:
                loggers.error(f"Critical error in minimal fallback: {str(final_e)}")
                raise


async def update_final_response(
    request_msg_id: ObjectId,
    chat_request: ModelRunRequest,
    chat_ids: List[str],
    chat_data: List[Dict[str, Any]],
    latest_message: Dict[str, Any],  # Changed from ChatHistMessage to Dict
    full_response: str,
    source_nodes: List[Dict[str, Any]],
    request_time: datetime,
    current_user: UserTenantDB
) -> None:
    """
    Update the final response in the database.

    Args:
        request_msg_id: The ID of the request message
        chat_request: The chat request
        chat_ids: List of chat IDs
        chat_data: List of chat data (already serialized)
        latest_message: The latest message from the user (already serialized)
        full_response: The full response text
        source_nodes: List of source nodes (already serialized)
        request_time: The request time
        current_user: The current user tenant
    """
    try:
        # Safely get the content from latest_message
        latest_message_content = (
            latest_message.get("content") if isinstance(latest_message, dict)
            else getattr(latest_message, "content", "") if hasattr(latest_message, "content")
            else ""
        )

        # Ensure chat_request is properly serialized
        request_data = {}
        try:
            if hasattr(chat_request, "model_dump"):
                request_data = chat_request.model_dump()
            elif hasattr(chat_request, "__dict__"):
                request_data = {k: v for k, v in chat_request.__dict__.items() if not k.startswith('_')}
            else:
                request_data = chat_request
        except Exception as e:
            loggers.error(f"Error serializing chat_request: {str(e)}")
            # Use a minimal representation if serialization fails
            request_data = {"error": "Failed to serialize request data"}

        # Create the final response object
        final_response = {
            "request": request_data,
            "response": {
                "request_time": datetime.now(),
                "processing_time": (datetime.now() - request_time).total_seconds(),
                "reply": full_response,
                "identified_product": "",
                "information_gathering": [],
                "chat_ids": chat_ids,
                "chat_data": chat_data,
                "latest_message": latest_message_content,
                "metadata": [],
                "identified_product": "",
                "language": "",
                "background_processing": True,
                "background_processing_completed": False,
                "reply_urls": [],
                "source_nodes": source_nodes,
                "call_to_action": []
            },
            "created_at": datetime.now(),
        }

        # Update the response
        current_user.db.ai_response.update_one(
            {"_id": ObjectId(request_msg_id)},
            {"$set": final_response}
        )
        loggers.info(f"Updated final response for ID: {request_msg_id}")
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        loggers.error(f"Error updating final response: {str(e)}\n{error_trace}")
        # Don't raise the exception to allow the function to continue
        # This prevents the error from propagating and breaking the response flow


async def update_message_references(
    inserted_reply_id: Union[ObjectId, str],
    inserted_message_id: Union[ObjectId, str],
    request_msg_id: Union[ObjectId, str],
    current_user: UserTenantDB
) -> None:
    """
    Update message references in the database.

    Args:
        inserted_reply_id: The ID of the inserted reply
        inserted_message_id: The ID of the inserted message
        request_msg_id: The ID of the request message
        current_user: The current user tenant
    """
    try:
        # Convert IDs to ObjectId if they are strings
        reply_id = ObjectId(inserted_reply_id) if isinstance(inserted_reply_id, str) else inserted_reply_id
        message_id = ObjectId(inserted_message_id) if isinstance(inserted_message_id, str) else inserted_message_id
        req_msg_id = str(request_msg_id)  # Ensure request_msg_id is a string for storage

        # Update the reply message with the request message ID
        current_user.db.chat_messages.update_one(
            {"_id": reply_id},
            {"$set": {"message_id": req_msg_id}}
        )

        # Update the user message with the request message ID
        current_user.db.chat_messages.update_one(
            {"_id": message_id},
            {"$set": {"message_id": req_msg_id}}
        )

        loggers.info(f"Updated message references for request ID: {req_msg_id}")
    except Exception as e:
        error_trace = traceback.format_exc()
        loggers.error(f"Error updating message references: {str(e)}\n{error_trace}")

        # Don't raise the exception to allow the function to continue
        # This prevents the error from propagating and breaking the response flow
