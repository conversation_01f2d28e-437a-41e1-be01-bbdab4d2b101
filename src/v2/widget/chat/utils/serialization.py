"""
Serialization utilities for chat widget.

This module provides utilities for serializing complex objects to JSON,
particularly for handling objects that are not natively JSON serializable.
"""
from typing import Any, Dict, List
import json
from datetime import datetime
from bson import ObjectId
from enum import Enum

from src.models.chat_hist import ChatHistMessage, MessageRole


class EnhancedJSONEncoder(json.JSONEncoder):
    """
    Enhanced JSON encoder that can handle complex types like:
    - datetime objects
    - ObjectId
    - Enum values
    - Custom model objects with model_dump method
    """
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, Enum):
            return obj.value
        if hasattr(obj, 'model_dump'):
            return obj.model_dump()
        if hasattr(obj, '__dict__'):
            return {k: v for k, v in obj.__dict__.items() if not k.startswith('_')}
        return super().default(obj)


def serialize_to_json(obj: Any) -> str:
    """
    Serialize an object to JSON string, handling complex types.
    
    Args:
        obj: The object to serialize
        
    Returns:
        JSON string representation of the object
    """
    return json.dumps(obj, cls=EnhancedJSONEncoder)


def serialize_message(message: ChatHistMessage) -> Dict[str, Any]:
    """
    Serialize a ChatHistMessage to a dictionary suitable for JSON serialization.
    
    Args:
        message: The ChatHistMessage to serialize
        
    Returns:
        Dictionary representation of the message
    """
    if not message:
        return {}
        
    result = {
        "role": message.role.value if isinstance(message.role, MessageRole) else message.role,
        "content": message.content,
        "sender": message.sender,
        "created_at": message.created_at.isoformat() if message.created_at else None,
        "user_id": message.user_id,
    }
    
    # Add optional fields if they exist
    if hasattr(message, 'id') and message.id:
        result["id"] = str(message.id)
    if hasattr(message, 'media_ids') and message.media_ids:
        result["media_ids"] = message.media_ids
    if hasattr(message, 'media_values') and message.media_values:
        result["media_values"] = message.media_values
        
    return result
