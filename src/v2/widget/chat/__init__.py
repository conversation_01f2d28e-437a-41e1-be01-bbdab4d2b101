"""
Main module for the chat widget functionality.

This module provides the main entry point for the chat widget,
delegating to specialized modules for handling different aspects
of the chat functionality.
"""
from src.helper.logger import setup_new_logging
from src.v2.widget.chat.handlers.chat_handler import handle_chat_widget
from src.v2.widget.chat.streaming.openai_streaming import generate_openai_sse

# Initialize logging
loggers = setup_new_logging(__name__)

# Re-export the main handler function and streaming functions
__all__ = ["handle_chat_widget",  "generate_openai_sse"]


