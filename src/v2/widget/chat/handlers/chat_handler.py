"""
Handler for chat widget requests.
"""
from fastapi.responses import StreamingResponse

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.models.chat_hist import ModelRunRequest
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from src.reply.minio_client import MinIOClient, MinIOConfig
from src.v2.widget.chat.streaming.openai_streaming import generate_openai_sse

# Initialize logging
loggers = setup_new_logging(__name__)


async def handle_chat_widget(
    chat_request: ModelRunRequest,
    current_user: UserTenantDB
) -> StreamingResponse:
    """
    Handle chat widget requests and return a streaming response.

    This function initializes the necessary clients and returns a streaming
    response that sends events to the client as they become available.

    Args:
        chat_request: The chat request containing the user's message
        current_user: Current user tenant database

    Returns:
        StreamingResponse with Server-Sent Events
    """
    try:
        # Initialize Qdrant client
        qd_config = current_user.db.settings.find_one({"name": "env"}).get("qdrant_config")
        if not qd_config:
            loggers.error("Qdrant configuration not found in database")
            raise ValueError("Qdrant configuration not found")

        qd_client = Qdrant_Call(config=QdrantConfig(**qd_config))

        # Initialize MinIO client
        if not hasattr(current_user, 'minio_config') or not current_user.minio_config:
            loggers.error("MinIO configuration not found in user tenant")
            raise ValueError("MinIO configuration not found")

        minio = MinIOClient(config=MinIOConfig(**current_user.minio_config))

        # Return streaming response
        return StreamingResponse(
            generate_openai_sse(chat_request, qd_client, minio, current_user),
            media_type="text/event-stream"
        )
    except Exception as e:
        loggers.error(f"Error handling chat widget request: {str(e)}")
        # Return an error response
        async def error_generator():
            yield f"data: {{\"type\":\"error\",\"message\":\"Error processing request: {str(e)}\"}}\n\n"

        return StreamingResponse(
            content=error_generator(),
            status_code=500,
            media_type="text/event-stream"
        )
