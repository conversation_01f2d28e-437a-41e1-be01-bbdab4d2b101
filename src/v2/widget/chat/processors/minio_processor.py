"""
MinIO processor for generating presigned URLs for source files and images.
"""
from typing import Dict, List, Optional, Any

from src.helper.logger import setup_new_logging
from src.reply.minio_client import MinIOClient
from src.v2.widget.chat.models.node_models import SourceNode
from src.v2.widget.chat.processors.base_processor import NodeProcessor

# Initialize logging
loggers = setup_new_logging(__name__)


class MinioProcessor(NodeProcessor):
    """Processor for generating MinIO presigned URLs."""
    
    def __init__(self, minio_client: MinIOClient):
        """
        Initialize the MinIO processor.
        
        Args:
            minio_client: MinIO client instance
        """
        self.minio = minio_client
    
    async def process_node(self, node: SourceNode) -> SourceNode:
        """
        Process a source node to add presigned URLs for source files and images.
        
        Args:
            node: The source node to process
            
        Returns:
            The processed source node with added URLs
        """
        # Process source file URL
        if node.source:
            try:
                source_url = self.minio.get_presigned_url(node.source, "Files")
                if source_url:
                    node.metadata["source_url"] = source_url
            except Exception as e:
                await self.handle_exception(e, f"getting source URL for {node.source}")
        
        # Process image URLs
        if node.images:
            try:
                images_url = []
                for image in node.images:
                    image_url = self.minio.get_presigned_url(
                        image, f"Images/{node.source}" if node.source else "Images"
                    )
                    if image_url:
                        images_url.append(image_url)
                
                if images_url:
                    node.metadata["images_url"] = images_url
            except Exception as e:
                await self.handle_exception(e, f"getting image URLs for {node.source}")
        
        return node
    
    async def get_minio_url(self, object_name: str, folder: str = "Files") -> Optional[str]:
        """
        Get a presigned URL for a MinIO object.
        
        Args:
            object_name: Name of the object in MinIO
            folder: Folder path in MinIO
            
        Returns:
            Presigned URL for the object or None if an error occurs
        """
        try:
            return self.minio.get_presigned_url(object_name, folder)
        except Exception as e:
            await self.handle_exception(e, f"getting URL for {object_name} in {folder}")
            return None
