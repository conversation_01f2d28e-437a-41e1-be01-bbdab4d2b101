"""
Sentence processor for retrieving sentence data from Qdrant.
"""
from typing import Dict, List, Optional, Any

from qdrant_client import QdrantClient
from qdrant_client import models

from src.helper.logger import setup_new_logging
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.v2.widget.chat.models.node_models import SourceNode
from src.v2.widget.chat.processors.base_processor import NodeProcessor

# Initialize logging
loggers = setup_new_logging(__name__)


class SentenceProcessor(NodeProcessor):
    """Processor for retrieving sentence data from Qdrant."""
    
    def __init__(self, qd_client: Qdrant_Call, qd_config: Dict[str, Any]):
        """
        Initialize the sentence processor.
        
        Args:
            qd_client: Qdrant client instance
            qd_config: Qdrant configuration
        """
        self.qd_client = qd_client
        self.qd_config = qd_config
        self.client = qd_client.client
    
    async def process_node(self, node: SourceNode) -> SourceNode:
        """
        Process a source node to add sentence data.
        
        Args:
            node: The source node to process
            
        Returns:
            The processed source node with added sentence data
        """
        if not node.sent_id:
            return node
        
        try:
            sentence_data = await self.get_sentence_node(node.sent_id)
            if sentence_data:
                node.metadata["sentence_data"] = sentence_data
        except Exception as e:
            await self.handle_exception(e, f"getting sentence data for {node.sent_id}")
        
        return node
    
    async def get_sentence_node(self, sent_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve sentence node from Qdrant.
        
        Args:
            sent_id: Sentence ID
            
        Returns:
            Sentence data or None if not found
        """
        try:
            sentence_collection = self.qd_config.get("sentence_collection")
            if not sentence_collection:
                loggers.warning("Sentence collection not specified in Qdrant config")
                return None
            
            # Try to get by ID first
            try:
                sentence_point = self.client.retrieve(
                    collection_name=sentence_collection,
                    ids=[sent_id]
                )
                
                if sentence_point:
                    point = sentence_point[0]
                    return {
                        "id": str(point.id),
                        "text": point.payload.get("text", ""),
                        "metadata": {
                            k: v for k, v in point.payload.items() 
                            if k not in ["text", "vector"]
                        }
                    }
            except Exception:
                pass
            
            # If ID retrieval fails, try with filter
            sentence_filter = models.Filter(
                must=[
                    models.FieldCondition(key="sent_id", match=models.MatchValue(value=sent_id)),
                ]
            )
            
            results, _ = self.client.scroll(
                collection_name=sentence_collection,
                scroll_filter=sentence_filter,
                limit=1
            )
            
            if not results:
                return None
            
            # Process the sentence data
            sentence_point = results[0]
            sentence_data = {
                "id": str(sentence_point.id),
                "text": sentence_point.payload.get("text", ""),
                "metadata": {
                    k: v for k, v in sentence_point.payload.items() 
                    if k not in ["text", "vector"]
                }
            }
            
            return sentence_data
        
        except Exception as e:
            await self.handle_exception(e, f"retrieving sentence node for {sent_id}")
            return None
