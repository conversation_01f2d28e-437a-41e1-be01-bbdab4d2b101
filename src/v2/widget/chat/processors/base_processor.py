"""
Base processor class for source node processing.
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic

from src.helper.logger import setup_new_logging
from src.v2.widget.chat.models.node_models import SourceNode, ProcessedNode

# Initialize logging
loggers = setup_new_logging(__name__)

T = TypeVar('T')


class BaseProcessor(Generic[T], ABC):
    """Base class for all processors in the chat widget."""
    
    @abstractmethod
    async def process(self, data: T) -> T:
        """
        Process the input data and return processed data.
        
        Args:
            data: Input data to process
            
        Returns:
            Processed data
        """
        pass
    
    async def handle_exception(self, e: Exception, context: str = "") -> None:
        """
        Handle exceptions during processing.
        
        Args:
            e: The exception that occurred
            context: Additional context about where the exception occurred
        """
        loggers.error(f"Error in {self.__class__.__name__}{' ' + context if context else ''}: {str(e)}")


class NodeProcessor(BaseProcessor[SourceNode]):
    """Base class for processors that work with source nodes."""
    
    @abstractmethod
    async def process_node(self, node: SourceNode) -> SourceNode:
        """
        Process a single source node.
        
        Args:
            node: The source node to process
            
        Returns:
            The processed source node
        """
        pass
    
    async def process(self, node: SourceNode) -> SourceNode:
        """
        Process a source node, handling any exceptions.
        
        Args:
            node: The source node to process
            
        Returns:
            The processed source node
        """
        try:
            return await self.process_node(node)
        except Exception as e:
            await self.handle_exception(e, f"processing node {node.node_id}")
            return node
