"""
Main processor for handling source nodes in the chat widget.
"""
import async<PERSON>
import json
from typing import Dict, List, Optional, Any

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.reply.minio_client import MinIO<PERSON>lient
from src.v2.widget.chat.models.node_models import SourceNode, NodeGroup, ProcessingResult
from src.v2.widget.chat.processors.base_processor import BaseProcessor
from src.v2.widget.chat.processors.minio_processor import MinioProcessor
from src.v2.widget.chat.processors.page_processor import PageProcessor
from src.v2.widget.chat.processors.sentence_processor import SentenceProcessor

# Initialize logging
loggers = setup_new_logging(__name__)


class SourceNodeProcessor(BaseProcessor[List[Dict[str, Any]]]):
    """Main processor for source nodes in the chat widget."""

    def __init__(
        self,
        qd_client: Qdrant_Call,
        qd_config: Dict[str, Any],
        minio: MinIOClient,
        current_user: UserTenantDB
    ):
        """
        Initialize the source node processor.

        Args:
            qd_client: Qdrant client instance
            qd_config: Qdrant configuration
            minio: MinIO client instance
            current_user: Current user tenant database
        """
        self.qd_client = qd_client
        self.qd_config = qd_config
        self.minio = minio
        self.current_user = current_user

        # Initialize sub-processors
        self.minio_processor = MinioProcessor(minio)
        self.page_processor = PageProcessor(qd_client, qd_config)
        self.sentence_processor = SentenceProcessor(qd_client, qd_config)

    async def process(self, source_nodes: List[Dict[str, Any]]) -> ProcessingResult:
        """
        Process source nodes in parallel.

        Args:
            source_nodes: List of source nodes to process

        Returns:
            ProcessingResult containing processed data
        """
        try:
            # Log the input
            loggers.info(f"Processing {len(source_nodes)} source nodes")
            if not source_nodes:
                loggers.warning("No source nodes to process")
                return ProcessingResult(
                    source_data={},
                    final_data=[],
                    processing_status="completed"
                )

            # Log the structure of the first node for debugging
            if source_nodes:
                try:
                    # Try to convert to JSON for logging
                    if hasattr(source_nodes[0], '__dict__'):
                        # It's an object, convert its attributes to a dictionary
                        node_dict = {k: str(v) for k, v in source_nodes[0].__dict__.items()}
                        loggers.info(f"First node structure (object): {json.dumps(node_dict)[:500]}...")
                    elif isinstance(source_nodes[0], dict):
                        # It's already a dictionary
                        loggers.info(f"First node structure (dict): {json.dumps(source_nodes[0], default=str)[:500]}...")
                    else:
                        # It's something else, just convert to string
                        loggers.info(f"First node structure (other): {str(source_nodes[0])[:500]}...")
                except Exception as e:
                    # Fallback to simple string representation
                    loggers.info(f"First node structure (fallback): {str(source_nodes[0])[:500]}...")

            # Convert raw nodes to SourceNode objects with error handling
            nodes = []
            for node in source_nodes:
                try:
                    # Check if it's a NodeWithScore object
                    if hasattr(node, 'node') and hasattr(node, 'score'):
                        loggers.info(f"Processing NodeWithScore object: score={node.score}")
                        # It's a NodeWithScore object, convert it directly
                        nodes.append(SourceNode.from_node_dict(node))
                    else:
                        # Try standard conversion
                        nodes.append(SourceNode.from_node_dict(node))
                except Exception as e:
                    import traceback
                    error_trace = traceback.format_exc()
                    loggers.error(f"Error converting node to SourceNode: {str(e)}")
                    loggers.error(f"Error trace: {error_trace}")

                    # Log node data in a safer way
                    try:
                        if hasattr(node, '__dict__'):
                            node_str = str({k: str(v) for k, v in node.__dict__.items()})
                        else:
                            node_str = str(node)
                        loggers.error(f"Node data: {node_str[:500]}...")
                    except:
                        loggers.error("Could not convert node to string for logging")

            if not nodes:
                loggers.warning("No valid nodes after conversion")
                return ProcessingResult(
                    source_nodes=[],
                    metadata=["url"],
                    processing_status="completed"
                )

            # Group nodes by source and hash_id
            node_groups = self._group_nodes(nodes)
            loggers.info(f"Grouped into {len(node_groups)} node groups")

            if not node_groups:
                loggers.warning("No node groups to process")
                # Return the original nodes in the desired format
                source_nodes = []
                for node in nodes:
                    node_data = {
                        "id_": node.node_id,
                        "extra_info": {
                            "page_number": node.page_number,
                            "images": node.images,
                            "source": node.source,
                            "hash_id": node.hash_id,
                            **{k: v for k, v in node.metadata.items() if k not in ["images", "source", "hash_id"]}
                        },
                        "text": node.text,
                        "score": node.score,
                        "sentence": [],
                        "created_at": node.metadata.get("created_at", ""),
                        "updated_at": node.metadata.get("updated_at", ""),
                        "updated_by": node.metadata.get("updated_by", "")
                    }
                    source_nodes.append(node_data)

                return ProcessingResult(
                    source_nodes=source_nodes,
                    metadata=["url"],
                    processing_status="completed"
                )

            # Process each group in parallel
            processing_tasks = [
                self._process_node_group(group)
                for group in node_groups.values()
            ]

            # Process each group and handle errors
            processed_groups = []
            for task in processing_tasks:
                try:
                    result = await task
                    if result:
                        processed_groups.append(result)
                except Exception as e:
                    await self.handle_exception(e, "awaiting node group processing task")

            # Combine results into the desired format
            source_nodes = []
            metadata = ["url"]  # Default metadata

            for group_result in processed_groups:
                source_nodes.extend(group_result)

            # Sort by score in descending order
            source_nodes.sort(key=lambda x: x.get("score", 0), reverse=True)

            loggers.info(f"Processed {len(source_nodes)} nodes")

            result = ProcessingResult(
                source_nodes=source_nodes,
                metadata=metadata
            )

            return result

        except Exception as e:
            await self.handle_exception(e, "processing source nodes")
            return ProcessingResult(
                source_nodes=[],
                metadata=["url"],
                processing_status="error"
            )

    def _group_nodes(self, nodes: List[SourceNode]) -> Dict[str, NodeGroup]:
        """
        Group nodes by source and hash_id.

        Args:
            nodes: List of source nodes

        Returns:
            Dictionary of node groups
        """
        node_groups = {}
        ungrouped_nodes = []

        for node in nodes:
            # Check if node has both hash_id and source
            if node.hash_id and node.source:
                key = f"{node.source}:{node.hash_id}"

                if key not in node_groups:
                    node_groups[key] = NodeGroup(
                        hash_id=node.hash_id,
                        source=node.source,
                        images=node.images,
                        metadata=node.metadata,
                        sent_ids=set(),
                        nodes=[]
                    )

                # Add sent_id to the set if it exists
                if node.sent_id:
                    node_groups[key].sent_ids.add(node.sent_id)

                # Add the node to the group
                node_groups[key].nodes.append(node)
            else:
                # Keep track of nodes that can't be grouped
                ungrouped_nodes.append(node)

        # Handle ungrouped nodes by creating individual groups
        for i, node in enumerate(ungrouped_nodes):
            # Generate a unique key for this node
            key = f"ungrouped:{i}:{node.node_id}"

            # Use node_id as hash_id if not available
            hash_id = node.hash_id or node.node_id

            # Use a default source if not available
            source = node.source or "unknown_source"

            node_groups[key] = NodeGroup(
                hash_id=hash_id,
                source=source,
                images=node.images,
                metadata=node.metadata,
                sent_ids=set([node.sent_id]) if node.sent_id else set(),
                nodes=[node]
            )

        return node_groups

    async def _process_node_group(self, group: NodeGroup) -> Optional[List[Dict[str, Any]]]:
        """
        Process a group of nodes with the same source and hash_id.

        Args:
            group: Group of nodes to process

        Returns:
            List of processed nodes in the desired format
        """
        try:
            loggers.info(f"Processing node group: {group.source}:{group.hash_id}")

            # Initialize tasks list and run all tasks in parallel
            tasks = {}

            # Start source URL task if source is available
            if group.source and group.source != "unknown_source":
                tasks["source_url"] = asyncio.create_task(
                    self.minio_processor.get_minio_url(group.source, "Files")
                )

            # Start page data task
            tasks["page_data"] = asyncio.create_task(
                self._get_page_data_for_group(group)
            )

            # Create individual tasks for sentence data
            sentence_tasks = []
            if group.sent_ids:
                for sent_id in group.sent_ids:
                    task_key = f"sentence_{sent_id}"
                    tasks[task_key] = asyncio.create_task(
                        self.sentence_processor.get_sentence_node(sent_id)
                    )
                    sentence_tasks.append(task_key)

            # Create individual tasks for image URLs
            image_tasks = []
            if group.images:
                for i, image in enumerate(group.images):
                    task_key = f"image_{i}"
                    tasks[task_key] = asyncio.create_task(
                        self.minio_processor.get_minio_url(
                            image, f"Images/{group.source}" if group.source != "unknown_source" else "Images"
                        )
                    )
                    image_tasks.append(task_key)

            # Wait for all tasks to complete and get results
            results = {}
            for key, task in tasks.items():
                try:
                    results[key] = await task
                except Exception as e:
                    await self.handle_exception(e, f"awaiting task {key}")
                    results[key] = None

            # Extract results
            source_url = results.get("source_url")
            page_data = results.get("page_data")

            # Collect sentence data from individual tasks
            sentence_data = []
            for task_key in sentence_tasks:
                if task_key in results and results[task_key] is not None:
                    sentence_data.append(results[task_key])

            # Collect image URLs from individual tasks
            image_urls = []
            for task_key in image_tasks:
                if task_key in results and results[task_key] is not None:
                    image_urls.append(results[task_key])

            # Log results
            loggers.info(f"Group {group.source}:{group.hash_id} - Source URL: {source_url is not None}, " +
                        f"Page data: {page_data is not None}, " +
                        f"Sentence data: {len(sentence_data)}, " +
                        f"Image URLs: {len(image_urls)}")

            # Prepare the source_url object in the desired format
            source_url_obj = None
            if source_url:
                source_url_obj = {
                    "name": group.source,
                    "url": source_url
                }

            # Prepare the extra_info object with all metadata
            processed_nodes = []

            for node in group.nodes:
                # Create the extra_info object
                extra_info = {
                    "page_number": node.page_number,
                    "images": node.images,
                    "source": node.source,
                    "hash_id": node.hash_id,
                    "updated_by": node.metadata.get("updated_by", ""),
                    "created_at": node.metadata.get("created_at", ""),
                    "updated_at": node.metadata.get("updated_at", ""),
                    "title": node.metadata.get("title", ""),
                    "section_title": node.metadata.get("section_title", ""),
                    "_node_type": node.metadata.get("_node_type", "Document"),
                    "document_id": node.metadata.get("document_id", "None"),
                    "doc_id": node.metadata.get("doc_id", "None"),
                    "ref_doc_id": node.metadata.get("ref_doc_id", "None"),
                    "resource_url": node.metadata.get("resource_url", []),
                }

                # Add sentence data if available
                if sentence_data:
                    extra_info["sentence"] = [s.get("text", "") for s in sentence_data if s.get("text")]

                # Add image URLs if available
                if image_urls:
                    extra_info["images_url"] = image_urls

                # Add source URL if available
                if source_url_obj:
                    extra_info["source_url"] = source_url_obj

                # Create the final node object
                node_data = {
                    "id_": node.hash_id or node.node_id,
                    "extra_info": extra_info,
                    "text": node.text,
                    "score": node.score,
                    "sentence": extra_info.get("sentence", []),
                    "created_at": extra_info.get("created_at", ""),
                    "updated_at": extra_info.get("updated_at", ""),
                    "updated_by": extra_info.get("updated_by", "")
                }

                processed_nodes.append(node_data)

            return processed_nodes

        except Exception as e:
            await self.handle_exception(e, f"processing node group {group.source}:{group.hash_id}")

            # Return basic data even if processing failed
            processed_nodes = []

            for node in group.nodes:
                node_data = {
                    "id_": node.hash_id or node.node_id,
                    "extra_info": {
                        "page_number": node.page_number,
                        "images": node.images,
                        "source": node.source,
                        "hash_id": node.hash_id,
                        "error": str(e)
                    },
                    "text": node.text,
                    "score": node.score,
                    "sentence": [],
                    "created_at": node.metadata.get("created_at", ""),
                    "updated_at": node.metadata.get("updated_at", ""),
                    "updated_by": node.metadata.get("updated_by", "")
                }

                processed_nodes.append(node_data)

            return processed_nodes

    async def _get_page_data_for_group(self, group: NodeGroup) -> Optional[Dict[str, Any]]:
        """
        Get page data for a node group.

        Args:
            group: Node group

        Returns:
            Page data or None if not found
        """
        # Find a node with page_number
        for node in group.nodes:
            if node.page_number is not None:
                return await self.page_processor.get_page_node(
                    node.source, node.page_number
                )

        return None
