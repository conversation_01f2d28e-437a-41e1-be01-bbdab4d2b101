"""
Page processor for retrieving page data from Qdrant.
"""
from typing import Dict, List, Optional, Any

from qdrant_client import QdrantClient
from qdrant_client import models

from src.helper.logger import setup_new_logging
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call
from src.v2.widget.chat.models.node_models import SourceNode
from src.v2.widget.chat.processors.base_processor import NodeProcessor

# Initialize logging
loggers = setup_new_logging(__name__)


class PageProcessor(NodeProcessor):
    """Processor for retrieving page data from Qdrant."""
    
    def __init__(self, qd_client: Qdrant_Call, qd_config: Dict[str, Any]):
        """
        Initialize the page processor.
        
        Args:
            qd_client: Qdrant client instance
            qd_config: Qdrant configuration
        """
        self.qd_client = qd_client
        self.qd_config = qd_config
        self.client = qd_client.client
    
    async def process_node(self, node: SourceNode) -> SourceNode:
        """
        Process a source node to add page data.
        
        Args:
            node: The source node to process
            
        Returns:
            The processed source node with added page data
        """
        if not node.source or node.page_number is None:
            return node
        
        try:
            page_data = await self.get_page_node(node.source, node.page_number)
            if page_data:
                node.metadata["page_data"] = page_data
        except Exception as e:
            await self.handle_exception(e, f"getting page data for {node.source}:{node.page_number}")
        
        return node
    
    async def get_page_node(self, source: str, page_number: int) -> Optional[Dict[str, Any]]:
        """
        Retrieve page node from Qdrant.
        
        Args:
            source: Source file name
            page_number: Page number
            
        Returns:
            Page data or None if not found
        """
        try:
            page_collection = self.qd_config.get("page_collection")
            if not page_collection:
                loggers.warning("Page collection not specified in Qdrant config")
                return None
            
            page_filter = models.Filter(
                must=[
                    models.FieldCondition(key="source", match=models.MatchValue(value=source)),
                    models.FieldCondition(key="page_number", match=models.MatchValue(value=page_number)),
                ]
            )
            
            results, _ = self.client.scroll(
                collection_name=page_collection,
                scroll_filter=page_filter,
                limit=1
            )
            
            if not results:
                return None
            
            # Process the page data
            page_point = results[0]
            page_data = {
                "id": str(page_point.id),
                "text": page_point.payload.get("text", ""),
                "metadata": {
                    k: v for k, v in page_point.payload.items() 
                    if k not in ["text", "vector"]
                }
            }
            
            return page_data
        
        except Exception as e:
            await self.handle_exception(e, f"retrieving page node for {source}:{page_number}")
            return None
