"""
Data models for node processing in the chat widget.
"""
from typing import Dict, List, Optional, Any, Set
from pydantic import BaseModel, Field


class SourceNode(BaseModel):
    """Model representing a source node with its metadata."""
    node_id: str = Field(default="")
    hash_id: Optional[str] = None
    sent_id: Optional[str] = None
    source: Optional[str] = None
    page_number: Optional[int] = None
    text: Optional[str] = None
    score: Optional[float] = None
    images: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    @classmethod
    def from_node_dict(cls, node_dict: Dict[str, Any]) -> "SourceNode":
        """Create a SourceNode from a node dictionary or NodeWithScore object."""
        # Check if node_dict is a NodeWithScore object
        if hasattr(node_dict, 'node') and hasattr(node_dict, 'score'):
            # It's a NodeWithScore object
            try:
                # Extract data from NodeWithScore
                metadata = getattr(node_dict.node, 'metadata', {})
                text = getattr(node_dict.node, 'text', '')
                score = node_dict.score
                node_id = str(getattr(node_dict.node, 'id_', '') or getattr(node_dict.node, 'id', ''))
            except Exception as e:
                import traceback
                print(f"Error extracting data from NodeWithScore: {str(e)}")
                print(traceback.format_exc())
                # Fallback to default values
                metadata = {}
                text = str(node_dict)
                score = 0.0
                node_id = ""
        # Handle dictionary formats
        elif isinstance(node_dict, dict):
            if "node" in node_dict:
                # Format: {"node": {"metadata": {...}, "text": "..."}, "score": 0.8}
                metadata = node_dict.get("node", {}).get("metadata", {})
                text = node_dict.get("node", {}).get("text", "")
                score = node_dict.get("score", 0.0)
                node_id = str(node_dict.get("node", {}).get("id", ""))
            elif "metadata" in node_dict:
                # Format: {"metadata": {...}, "text": "...", "score": 0.8}
                metadata = node_dict.get("metadata", {})
                text = node_dict.get("text", "")
                score = node_dict.get("score", 0.0)
                node_id = str(node_dict.get("id", ""))
            else:
                # Try to extract metadata from the node_dict itself
                metadata = {k: v for k, v in node_dict.items()
                        if k not in ["text", "score", "id"]}
                text = node_dict.get("text", "")
                score = node_dict.get("score", 0.0)
                node_id = str(node_dict.get("id", ""))
        else:
            # Handle other object types by extracting attributes
            try:
                metadata = getattr(node_dict, 'metadata', {})
                text = getattr(node_dict, 'text', str(node_dict))
                score = getattr(node_dict, 'score', 0.0)
                node_id = str(getattr(node_dict, 'id_', '') or getattr(node_dict, 'id', ''))
            except Exception as e:
                import traceback
                print(f"Error extracting data from unknown object type: {str(e)}")
                print(traceback.format_exc())
                # Fallback to default values
                metadata = {}
                text = str(node_dict)
                score = 0.0
                node_id = ""

        # Extract hash_id and sent_id from metadata
        hash_id = metadata.get("hash_id")
        sent_id = metadata.get("sent_id")
        source = metadata.get("source")
        page_number = metadata.get("page_number")
        images = metadata.get("images", [])

        # Generate a hash_id if not available
        if not hash_id and text:
            import hashlib
            hash_id = hashlib.sha256(text.encode()).hexdigest()

        # Use a default source if not available
        if not source:
            source = "unknown_source"

        return cls(
            node_id=node_id or hash_id or "unknown_id",
            hash_id=hash_id,
            sent_id=sent_id,
            source=source,
            page_number=page_number,
            text=text,
            score=score,
            images=images,
            metadata=metadata
        )


class ProcessedNode(BaseModel):
    """Model representing a processed node with enriched data."""
    original_node: SourceNode
    source_url: Optional[str] = None
    images_url: List[str] = Field(default_factory=list)
    page_data: Optional[Dict[str, Any]] = None
    sentence_data: List[Dict[str, Any]] = Field(default_factory=list)

    class Config:
        arbitrary_types_allowed = True


class NodeGroup(BaseModel):
    """Model for grouping nodes by source and hash_id."""
    hash_id: str
    source: str
    images: List[str] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)
    sent_ids: Set[str] = Field(default_factory=set)
    nodes: List[SourceNode] = Field(default_factory=list)

    class Config:
        arbitrary_types_allowed = True


class ProcessingResult(BaseModel):
    """Model representing the result of node processing."""
    response: str = ""
    source_nodes: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: List[str] = Field(default_factory=list)
    processing_status: str = "completed"
