from src.v2.settings.ai_enable_disable import router as ai_status_router
from src.v2.settings.credits import credits_router
from src.v2.settings.user_acess import router as user_access_router
from src.v2.settings.others.route import router as others_router
from fastapi import APIRouter
settings_router = APIRouter()

settings_router.include_router(ai_status_router)
settings_router.include_router(credits_router)
settings_router.include_router(user_access_router)
settings_router.include_router(others_router)