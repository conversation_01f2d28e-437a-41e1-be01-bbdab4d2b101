from bson import ObjectId
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any, Union
from src.helper.filter_access import get_filter_access
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from src.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)
router = APIRouter(prefix="/user_access", tags=["User Access"])


# class RawUserData(BaseModel):
#     role: str
#     data: Dict[str, Any] 

#     config_schema={
#         ""
#     }




class RawUserData(BaseModel):
    role: str
    filter: Dict[str, Any]


@router.post("/add_access")
async def add_user_access(
    request: RawUserData,
    current_user: UserTenantDB = Depends(get_tenant_info)
):

    if current_user.user.role != "admin":
        raise HTTPException(status_code=403, detail="You are not authorized to access this information")


    formatted_request = {
        request.role: request.filter    
    }

    check = current_user.db.settings.find_one({"name": "nav_access"})

    if check:
        result = current_user.db.settings.update_one(
            {"_id": check["_id"]},
            {"$set": formatted_request}
        )
        if result.modified_count > 0:
            loggers.info("User access updated")
            return {"message": "User access updated"}
        else:
            loggers.warning("No changes made to user access")
            return {"message": "No changes made to user access"}
    else:
        document = {"name": "nav_access"}
        document.update(formatted_request)
        
        insert_result = current_user.db.settings.insert_one(document)
        if insert_result.inserted_id:
            loggers.info("User access inserted")
            return {"message": "User access inserted"}

    raise HTTPException(status_code=500, detail="Failed to update or insert user access")


@router.post("/user_access")
async def get_user_access(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    user_role = current_user.user.role
    data = current_user.db.settings.find_one({"name": "nav_access"})
    
    if not data:
        raise HTTPException(status_code=404, detail="Access configuration not found")

    data.pop("_id", None)  # Clean up response
    access_data = data.get(user_role)

    if access_data:
        return access_data
    else:
        raise HTTPException(status_code=404, detail="User access not found")


@router.get("/granted_item")
async def permissions(
    current_user: UserTenantDB = Depends(get_tenant_info),
    filter_route: Optional[str] = None,
):
    """
    Get CTA filters, including assigned_to user information.
    """
    try:
        # Safely get filters or initialize as empty dict
        filters = get_filter_access(current_user, filter_route, True) or {}
        
        if filter_route == "CTA":
            try:
                assigned_to_ids = await current_user.async_db.cta.distinct("assigned_to")
                valid_ids = [ObjectId(id_str) for id_str in assigned_to_ids if id_str]

                if valid_ids:
                    users = await current_user.async_db.users.find(
                        {"_id": {"$in": valid_ids}},
                        {"_id": 1, "username": 1}
                    ).to_list(length=None)

                    # Format into id-name pairs
                    assigned_to_list = [
                        {"id": str(user["_id"]), "name": user.get("username", "")}
                        for user in users
                    ]
                else:
                    assigned_to_list = []
                filters["assigned_to"] = assigned_to_list

            except Exception as e:
                filters["assigned_to"] = []

        return JSONResponse(content=filters)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
