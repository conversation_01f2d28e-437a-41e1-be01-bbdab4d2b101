from fastapi import APIRouter, Depends, HTTPException, Query
from datetime import datetime, time
from bson import ObjectId
from typing import List, Optional

from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.credit import (
    CreditSystem,
    AddCreditsRequest,
    CreditResponse,
    CreditBalance,
    CreditTransaction,
    TransactionResponse,
    CreditManager
)
from src.helper.logger import setup_new_logging

# Initialize logging
loggers = setup_new_logging(__name__)

# Create an APIRouter instance
credits_router = APIRouter(prefix="/credits", tags=["Credits"])

@credits_router.post("/add", response_model=CreditResponse)
async def add_credits(
    request: AddCreditsRequest,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Add credits to the user's account

    Args:
        request (AddCreditsRequest): The request containing the amount of credits to add
        current_user (UserTenantDB): The current user's details

    Returns:
        CreditResponse: Response with success status, message, and updated balance
    """
    try:
        # Use the CreditManager to add credits
        credit_manager = CreditManager(current_user.db)
        result = credit_manager.add_credits(
            amount=request.amount,
            description=request.description,
            source=request.source,
            currency=request.currency
        )

        if not result["success"]:
            return CreditResponse(
                success=False,
                message=result["message"]
            )

        # Create a CreditTransaction object from the result
        # Ensure transaction_id is an ObjectId
        transaction_id = result["transaction"]["transaction_id"]
        if isinstance(transaction_id, str):
            transaction_id = ObjectId(transaction_id)

        transaction = CreditTransaction(
            transaction_id=transaction_id,
            transaction_type="addition",
            amount=result["transaction"]["amount"],
            original_amount=result["transaction"].get("original_amount", request.amount),
            currency=result["transaction"].get("currency", request.currency),
            timestamp=result["transaction"]["timestamp"],
            description=request.description,
            source=request.source
        )

        # Create a balance object
        balance = CreditBalance(
            total_credits=result["total_credits"],
            remaining=result["remaining"],
            last_updated=result["transaction"]["timestamp"]
        )

        return CreditResponse(
            success=True,
            message=f"Added {request.amount} credits successfully",
            balance=balance,
            transaction=transaction
        )

    except Exception as e:
        loggers.exception("Error in add_credits")
        raise HTTPException(status_code=500, detail=str(e))

@credits_router.get("/balance", response_model=CreditResponse)
async def get_credit_balance(
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Get the current credit balance

    Args:
        current_user (UserTenantDB): The current user's details

    Returns:
        CreditResponse: Response with success status, message, and current balance
    """
    try:
        # Use the CreditManager to get the balance
        credit_manager = CreditManager(current_user.db)
        result = credit_manager.get_balance()

        if not result["success"]:
            return CreditResponse(
                success=False,
                message=result["message"]
            )

        Used_Credits = result["balance"]["total_credits"] - result["balance"]["remaining"]

        cost=current_user.db.settings.find_one({"name": "credit"})["cost_division"]
        Used_Division={

            "ai_cost": cost["ai_cost"]/100*Used_Credits,
            "whatsapp_cost": cost["whatsapp_cost"]/100*Used_Credits,
        }

        # Create a balance object
        balance = CreditBalance(
            total_credits=result["balance"]["total_credits"],
            remaining=result["balance"]["remaining"],
            last_updated=result["balance"]["last_updated"],
            cost_division=Used_Division
        )

        return CreditResponse(
            success=True,
            message="Credit balance retrieved successfully",
            balance=balance
        )

    except Exception as e:
        loggers.exception("Error in get_credit_balance")
        raise HTTPException(status_code=500, detail=str(e))

@credits_router.get("/history", response_model=TransactionResponse)
async def get_credit_history(
    current_user: UserTenantDB = Depends(get_tenant_info),
    start_date: Optional[datetime] = Query(default_factory=lambda: datetime.now().min, description="Start date for filtering transactions"),
    end_date: Optional[datetime] = Query(default_factory=datetime.now, description="End date for filtering transactions"),
    transaction_type: Optional[str] = Query(None, description="Filter by transaction type (addition or usage)"),
    page: int = Query(1, description="Page number (starts from 1)"),
    limit: int = Query(50, description="Number of transactions per page")
):
    """
    Get credit transaction history with optional filtering

    Args:
        current_user (UserTenantDB): The current user's details
        start_date (datetime): Start date for filtering transactions
        end_date (datetime): End date for filtering transactions
        transaction_type (str, optional): Filter by transaction type (addition or usage)
        page (int): Page number (starts from 1)
        limit (int): Number of transactions per page

    Returns:
        TransactionResponse: Response with list of transactions and pagination info
    """
    try:
        start_date = datetime.combine(start_date.date(), time.min)
        end_date = datetime.combine(end_date.date(), time.max)

        # Calculate skip value from page and limit
        skip = (page - 1) * limit

        # Use the CreditManager to get the history
        credit_manager = CreditManager(current_user.db)
        result = credit_manager.get_history(
            transaction_type=transaction_type,
            start_date=start_date,
            end_date=end_date,
            limit=limit,
            skip=skip
        )

        if not result["success"]:
            return TransactionResponse(
                transactions=[],
                total=0,
                page=1,
                limit=limit,
                total_pages=1,
                has_more=False,
                start_date=start_date,
                end_date=end_date,
                transaction_type=transaction_type
            )

        # Convert dict transactions to CreditTransaction objects
        transaction_objects = []
        for t in result["transactions"]:
            try:
                # Convert transaction_id to ObjectId if it's a string
                transaction_id = t.get("transaction_id", ObjectId())
                if isinstance(transaction_id, str):
                    transaction_id = ObjectId(transaction_id)

                # Create a dictionary with all fields from the transaction
                transaction_data = {
                    "transaction_id": transaction_id,
                    "transaction_type": t.get("transaction_type", "unknown"),
                    "amount": t.get("amount", 0),
                    "timestamp": t.get("timestamp", datetime.now()),
                    "description": t.get("description", ""),
                }

                # Add optional fields if they exist
                for field in ["message_id", "source", "original_amount", "currency",
                              "cost_breakdown", "customer_id", "customer_name", "channel"]:
                    if field in t:
                        transaction_data[field] = t[field]

                # Create the transaction object
                transaction = CreditTransaction(**transaction_data)
                transaction_objects.append(transaction)
            except Exception as e:
                loggers.warning(f"Error converting transaction: {e}")
                continue

        # Calculate pagination information
        total_count = result["total"]
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1
        has_more = page < total_pages

        return TransactionResponse(
            transactions=transaction_objects,
            total=total_count,
            page=page,
            limit=limit,
            total_pages=total_pages,
            has_more=has_more,
            total_cost=result["total_cost"],
            start_date=start_date,
            end_date=end_date,
            transaction_type=transaction_type
        )

    except Exception as e:
        loggers.exception("Error in get_credit_history")
        raise HTTPException(status_code=500, detail=str(e))



@credits_router.post("/deduct", response_model=CreditResponse)
async def deduct_credits(
    amount: int = Query(2, description="Amount of credits to deduct"),
    message_id: Optional[str] = Query(None, description="ID of the message associated with this deduction"),
    description: str = Query("Message processing", description="Description of the credit usage"),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Deduct credits from the user's account

    Args:
        amount (int): Amount of credits to deduct (default: 2)
        message_id (str, optional): ID of the message associated with this deduction
        description (str): Description of the credit usage
        current_user (UserTenantDB): The current user's details

    Returns:
        CreditResponse: Response with success status, message, and updated balance
    """
    try:
        # Use the CreditManager to deduct credits
        credit_manager = CreditManager(current_user.db)
        result = credit_manager.deduct_credits(
            amount=amount,
            message_id=message_id,
            description=description
        )

        if not result["success"]:
            return CreditResponse(
                success=False,
                message=result["message"]
            )

        # Create a CreditTransaction object from the result
        # Ensure transaction_id is an ObjectId
        transaction_id = result["transaction"]["transaction_id"]
        if isinstance(transaction_id, str):
            transaction_id = ObjectId(transaction_id)

        transaction = CreditTransaction(
            transaction_id=transaction_id,
            transaction_type="usage",
            amount=amount,
            timestamp=result["transaction"]["timestamp"],
            description=description,
            message_id=message_id,
            cost_breakdown=result["transaction"].get("cost_breakdown", {})
        )

        # Calculate cost division for the balance
        used_credits = result["total_credits"] - result["remaining"]
        cost_division = {}

        # Get cost division from credit manager
        credit_doc = current_user.db.settings.find_one({"name": "credit"})
        if credit_doc and "cost_division" in credit_doc:
            for cost_type, percentage in credit_doc["cost_division"].items():
                try:
                    # Convert percentage to float if it's a string
                    if isinstance(percentage, str):
                        percentage = float(percentage)
                    # Calculate the cost for this type
                    cost_division[cost_type] = (percentage / 100) * used_credits
                except (ValueError, TypeError):
                    # Skip if conversion fails
                    continue

        # Create a balance object
        balance = CreditBalance(
            total_credits=result["total_credits"],
            remaining=result["remaining"],
            last_updated=result["transaction"]["timestamp"],
            cost_division=cost_division
        )

        return CreditResponse(
            success=True,
            message=f"Deducted {amount} credits successfully",
            balance=balance,
            transaction=transaction
        )

    except Exception as e:
        loggers.exception("Error in deduct_credits")
        raise HTTPException(status_code=500, detail=str(e))
