"""
AI Status Utility Functions.

This module provides utility functions for checking AI status.
"""

from src.v2.settings.ai_enable_disable.config import get_ai_status_config, is_ai_currently_enabled

async def check_ai_enabled(db, channel: str = None) -> bool:
    """
    Check if AI is currently enabled for the given database and channel.

    Args:
        db: MongoDB database connection
        channel: Optional channel to check (if None, only checks global status)

    Returns:
        bool: True if AI is enabled, False otherwise
    """
    # Get AI status configuration from database
    config = await get_ai_status_config(db)

    # Check if AI is currently enabled for the specified channel
    return is_ai_currently_enabled(config, channel)
