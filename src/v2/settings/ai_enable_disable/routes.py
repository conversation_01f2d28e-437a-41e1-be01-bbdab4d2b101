"""
AI Status API Routes.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, Body
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Optional

from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.v2.settings.ai_enable_disable.model import AI<PERSON>tatusConfig, ChannelType, ChannelSchedule
from src.v2.settings.ai_enable_disable.config import (
    get_ai_status_config,
    update_ai_status_config,
    is_ai_currently_enabled
)

# Initialize router
router = APIRouter(
    prefix="/ai",
    tags=["AI Status"],
)

@router.get("/status")
async def get_ai_status(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get the current AI status configuration.

    Returns:
        The current AI status configuration and whether AI is currently enabled
    """
    # Get AI status configuration from database
    config = await get_ai_status_config(current_user.async_db)

    # Check if AI is currently enabled
    is_enabled = is_ai_currently_enabled(config)

    return {
        "success": True,
        "enabled": config.enabled,
        "schedule": config.schedule,
        "start_time": config.start_time,
        "end_time": config.end_time,
        "channels_config": config.channels_config,
        "is_currently_enabled": is_enabled
    }


@router.put("/toggle")
async def toggle_ai_status(
    enabled: bool = Body(..., embed=True),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Toggle the main AI enabled/disabled status.

    Args:
        enabled: Whether to enable (True) or disable (False) AI

    Returns:
        Success message and current status
    """
    # Get current configuration
    config = await get_ai_status_config(current_user.async_db)

    # Update enabled status
    config.enabled = enabled

    # Save to database
    success = await update_ai_status_config(
        current_user.async_db,
        config,
        current_user.user.id
    )

    if not success:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"Failed to {'enable' if enabled else 'disable'} AI"}
        )

    # Check if AI is currently enabled based on new configuration
    is_currently_enabled = is_ai_currently_enabled(config)

    return {
        "success": True,
        "message": f"AI {'enabled' if enabled else 'disabled'} successfully",
        "enabled": enabled,
        "is_currently_enabled": is_currently_enabled
    }


@router.put("/schedule")
async def update_schedule(
    schedule: Optional[str] = Body(None),
    start_time: Optional[str] = Body(None),
    end_time: Optional[str] = Body(None),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update the AI schedule configuration.

    Args:
        schedule: Schedule type ("enable", "disable", or "delete" to remove schedule)
        start_time: Start time in HH:MM format (required for "enable" and "disable")
        end_time: End time in HH:MM format (required for "enable" and "disable")

    Returns:
        Success message and updated configuration
    """
    # Get current configuration
    config = await get_ai_status_config(current_user.async_db)

    # Check if AI is enabled
    if not config.enabled:
        return JSONResponse(
            status_code=400,
            content={"success": False, "message": "Enable AI before setting a schedule"}
        )

    # Handle schedule deletion
    if schedule == "delete":
        config.schedule = None
        config.start_time = None
        config.end_time = None

        # Save to database and return response
        success = await update_ai_status_config(
            current_user.async_db,
            config,
            current_user.user.id
        )

        if not success:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": "Failed to delete AI schedule"}
            )

        return {
            "success": True,
            "message": "AI schedule deleted successfully",
            "enabled": config.enabled,
            "schedule": None,
            "start_time": None,
            "end_time": None,
            "is_currently_enabled": is_ai_currently_enabled(config)
        }

    # Handle schedule update
    if schedule in ["enable", "disable"]:
        # Validate times
        if not start_time or not end_time:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Start time and end time are required when setting a schedule"}
            )

        # Update config
        try:
            # Create a temporary config to validate the values
            temp_config = AIStatusConfig(
                enabled=config.enabled,
                schedule=schedule,
                start_time=start_time,
                end_time=end_time
            )

            # If validation passes, update the config
            config.schedule = temp_config.schedule
            config.start_time = temp_config.start_time
            config.end_time = temp_config.end_time
        except ValueError as e:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": str(e)}
            )
    else:
        return JSONResponse(
            status_code=400,
            content={"success": False, "message": "Schedule must be 'enable', 'disable', or 'delete'"}
        )

    # Save to database
    success = await update_ai_status_config(
        current_user.async_db,
        config,
        current_user.user.id
    )

    if not success:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": "Failed to update AI schedule"}
        )

    # Check if AI is currently enabled based on new configuration
    is_currently_enabled = is_ai_currently_enabled(config)

    return {
        "success": True,
        "message": "AI schedule updated successfully",
        "enabled": config.enabled,
        "schedule": config.schedule,
        "start_time": config.start_time,
        "end_time": config.end_time,
        "channels_config": config.channels_config,
        "is_currently_enabled": is_currently_enabled
    }


@router.get("/channels")
async def get_channels(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get the list of available channels and their current AI status.

    Returns:
        List of channels and their AI status
    """
    # Get AI status configuration from database
    config = await get_ai_status_config(current_user.async_db)

    # Ensure all channels are present with default values
    config.ensure_all_channels()

    # Get all available channels
    channels = [channel.value for channel in ChannelType]

    # Check status for each channel
    channel_status = {
        "whatsapp": {
            "enabled": config.channels_config.get(ChannelType.WHATSAPP.value, True),
            "is_currently_enabled": is_ai_currently_enabled(config, ChannelType.WHATSAPP.value),
            "schedule": config.channel_schedules.get(ChannelType.WHATSAPP.value, {}).get("schedule"),
            "start_time": config.channel_schedules.get(ChannelType.WHATSAPP.value, {}).get("start_time"),
            "end_time": config.channel_schedules.get(ChannelType.WHATSAPP.value, {}).get("end_time")
        },
        "website": {
            "enabled": config.channels_config.get(ChannelType.WEBSITE.value, True),
            "is_currently_enabled": is_ai_currently_enabled(config, ChannelType.WEBSITE.value),
            "schedule": config.channel_schedules.get(ChannelType.WEBSITE.value, {}).get("schedule"),
            "start_time": config.channel_schedules.get(ChannelType.WEBSITE.value, {}).get("start_time"),
            "end_time": config.channel_schedules.get(ChannelType.WEBSITE.value, {}).get("end_time")
        }
    }
    return {
        "success": True,
        "channels": channel_status
    }


@router.put("/channel/{channel}/toggle")
async def toggle_channel_status(
    channel: str,
    enabled: bool = Body(..., embed=True),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Toggle AI enabled/disabled status for a specific channel.

    Args:
        channel: The channel to update
        enabled: Whether to enable (True) or disable (False) AI for this channel

    Returns:
        Success message and updated status
    """
    # Validate channel
    valid_channels = [c.value for c in ChannelType]
    if channel not in valid_channels:
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"Invalid channel: {channel}. Valid channels are: {', '.join(valid_channels)}"
            }
        )

    # Get current configuration
    config = await get_ai_status_config(current_user.async_db)

    # Ensure all channels are present with default values
    config.ensure_all_channels()

    # Update channel configuration
    config.channels_config[channel] = enabled

    # Save to database
    success = await update_ai_status_config(
        current_user.async_db,
        config,
        current_user.user.id
    )

    if not success:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"Failed to update channel status"}
        )

    # Check if AI is currently enabled for this channel
    is_currently_enabled = is_ai_currently_enabled(config, channel)

    return {
        "success": True,
        "message": f"AI for channel {channel} {'enabled' if enabled else 'disabled'} successfully",
        "channel": channel,
        "enabled": enabled,
        "is_currently_enabled": is_currently_enabled
    }


@router.put("/channel/{channel}/schedule")
async def update_channel_schedule(
    channel: str,
    schedule: Optional[str] = Body(None),
    start_time: Optional[str] = Body(None),
    end_time: Optional[str] = Body(None),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update the schedule configuration for a specific channel.

    Args:
        channel: The channel to update (Whatsapp or Website)
        schedule: Schedule type ("enable", "disable", or "delete" to remove schedule)
        start_time: Start time in HH:MM format (required for "enable" and "disable")
        end_time: End time in HH:MM format (required for "enable" and "disable")

    Returns:
        Success message and updated configuration
    """
    # Validate channel
    if channel not in [ChannelType.WHATSAPP.value, ChannelType.WEBSITE.value]:
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "message": f"Invalid channel: {channel}. Only Whatsapp and Website channels support scheduling."
            }
        )

    # Get current configuration
    config = await get_ai_status_config(current_user.async_db)

    # Check if AI is enabled
    if not config.enabled:
        return JSONResponse(
            status_code=400,
            content={"success": False, "message": "Enable AI before setting a schedule"}
        )

    # Handle schedule deletion
    if schedule == "delete":
        if channel in config.channel_schedules:
            del config.channel_schedules[channel]

        # Save to database and return response
        success = await update_ai_status_config(
            current_user.async_db,
            config,
            current_user.user.id
        )

        if not success:
            return JSONResponse(
                status_code=500,
                content={"success": False, "message": f"Failed to delete schedule for {channel}"}
            )

        return {
            "success": True,
            "message": f"Schedule for {channel} deleted successfully",
            "channel": channel,
            "schedule": None,
            "start_time": None,
            "end_time": None,
            "is_currently_enabled": is_ai_currently_enabled(config, channel)
        }

    # Handle schedule update
    if schedule in ["enable", "disable"]:
        # Validate times
        if not start_time or not end_time:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "Start time and end time are required when setting a schedule"}
            )

        # Update config
        try:
            # Create a temporary schedule to validate the values
            temp_schedule = ChannelSchedule(
                schedule=schedule,
                start_time=start_time,
                end_time=end_time
            )

            # If validation passes, update the channel schedule
            if channel not in config.channel_schedules:
                config.channel_schedules[channel] = temp_schedule
            else:
                config.channel_schedules[channel].schedule = temp_schedule.schedule
                config.channel_schedules[channel].start_time = temp_schedule.start_time
                config.channel_schedules[channel].end_time = temp_schedule.end_time

        except ValueError as e:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": str(e)}
            )
    else:
        return JSONResponse(
            status_code=400,
            content={"success": False, "message": "Schedule must be 'enable', 'disable', or 'delete'"}
        )

    # Save to database
    success = await update_ai_status_config(
        current_user.async_db,
        config,
        current_user.user.id
    )

    if not success:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"Failed to update schedule for {channel}"}
        )

    # Check if AI is currently enabled for this channel
    is_currently_enabled = is_ai_currently_enabled(config, channel)

    return {
        "success": True,
        "message": f"Schedule for {channel} updated successfully",
        "channel": channel,
        "schedule": schedule,
        "start_time": start_time,
        "end_time": end_time,
        "is_currently_enabled": is_currently_enabled
    }
