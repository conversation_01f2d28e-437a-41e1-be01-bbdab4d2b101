"""
AI Status Configuration Models.
"""

from pydantic import BaseModel, Field, model_validator
from typing import Optional, Literal, Any, Dict
from datetime import datetime, time

from enum import Enum, auto


class ChannelType(str, Enum):
    """Enum for different communication channels."""
    WEBSITE = "Website"
    WHATSAPP = "Whatsapp"
    PLAYGROUND = "Playground"


class ChannelSchedule(BaseModel):
    """Model for channel-specific schedule configuration."""
    schedule: Optional[Literal["enable", "disable"]] = Field(None, description="Whether to enable or disable AI during scheduled hours")
    start_time: Optional[str] = Field(None, description="Time when scheduled action begins (24-hour format HH:MM)")
    end_time: Optional[str] = Field(None, description="Time when scheduled action ends (24-hour format HH:MM)")
    _start_time_obj: Optional[time] = None
    _end_time_obj: Optional[time] = None

    def get_start_time_obj(self) -> Optional[time]:
        """Get the start time as a datetime.time object for comparison."""
        if not self._start_time_obj and self.start_time:
            self._start_time_obj = datetime.strptime(self.start_time, "%H:%M").time()
        return self._start_time_obj

    def get_end_time_obj(self) -> Optional[time]:
        """Get the end time as a datetime.time object for comparison."""
        if not self._end_time_obj and self.end_time:
            self._end_time_obj = datetime.strptime(self.end_time, "%H:%M").time()
        return self._end_time_obj


class AIStatusConfig(BaseModel):
    enabled: bool = Field(..., description="Main toggle for AI functionality (True=enabled, False=disabled)")
    schedule: Optional[Literal["enable", "disable"]] = Field(None, description="Whether to enable or disable AI during scheduled hours")
    start_time: Optional[str] = Field(None, description="Time when scheduled action begins (24-hour format HH:MM)")
    end_time: Optional[str] = Field(None, description="Time when scheduled action ends (24-hour format HH:MM)")
    channels_config: dict[str, bool] = Field(
        default_factory=lambda: {channel.value: True for channel in ChannelType},
        description="Configuration for AI enablement per channel (True=enabled, False=disabled)"
    )
    channel_schedules: Dict[str, ChannelSchedule] = Field(
        default_factory=dict,
        description="Channel-specific schedule configurations"
    )

    # Internal properties for time comparison
    _start_time_obj: Optional[time] = None
    _end_time_obj: Optional[time] = None

    @model_validator(mode='before')
    @classmethod
    def validate_schedule_logic(cls, data: Any) -> Any:
        if isinstance(data, dict):
            schedule = data.get('schedule')
            start_time = data.get('start_time')
            end_time = data.get('end_time')

            # Parse times if they are strings to validate format
            for key in ['start_time', 'end_time']:
                time_str = data.get(key)
                if isinstance(time_str, str) and time_str:
                    try:
                        # Just validate the format, but keep as string
                        datetime.strptime(time_str, "%H:%M")
                    except ValueError:
                        raise ValueError(f"{key} must be in 24-hour format (HH:MM)")

            if schedule is not None:
                if not start_time or not end_time:
                    raise ValueError("start_time and end_time must be provided when schedule is set")

            if (start_time or end_time) and not schedule:
                raise ValueError("schedule must be provided when times are set")

            # Ensure all channels have default values
            channels_config = data.get('channels_config', {})
            if channels_config is None:
                channels_config = {}
                data['channels_config'] = channels_config

            # Set default value (True) for any missing channels
            for channel in ChannelType:
                if channel.value not in channels_config:
                    channels_config[channel.value] = True

            # Validate channel schedules
            channel_schedules = data.get('channel_schedules', {})
            if channel_schedules:
                for channel, schedule_data in channel_schedules.items():
                    if isinstance(schedule_data, dict):
                        schedule = schedule_data.get('schedule')
                        start_time = schedule_data.get('start_time')
                        end_time = schedule_data.get('end_time')

                        if schedule is not None:
                            if not start_time or not end_time:
                                raise ValueError(f"start_time and end_time must be provided for {channel} schedule")
                            try:
                                datetime.strptime(start_time, "%H:%M")
                                datetime.strptime(end_time, "%H:%M")
                            except ValueError:
                                raise ValueError(f"Invalid time format for {channel} schedule")

        return data

    def get_start_time_obj(self) -> Optional[time]:
        """Get the start time as a datetime.time object for comparison."""
        if not self._start_time_obj and self.start_time:
            self._start_time_obj = datetime.strptime(self.start_time, "%H:%M").time()
        return self._start_time_obj

    def get_end_time_obj(self) -> Optional[time]:
        """Get the end time as a datetime.time object for comparison."""
        if not self._end_time_obj and self.end_time:
            self._end_time_obj = datetime.strptime(self.end_time, "%H:%M").time()
        return self._end_time_obj

    def ensure_all_channels(self) -> None:
        """
        Ensure all channels are present in the channels_config with default values.
        This method can be called after initialization to make sure no channels are missing.
        """
        # Initialize channels_config if it's None
        if self.channels_config is None:
            self.channels_config = {}

        # Set default value (True) for any missing channels and always in order Whatsapp, Website only
        self.channels_config = {
            ChannelType.WHATSAPP.value: self.channels_config.get(ChannelType.WHATSAPP.value, True),
            ChannelType.WEBSITE.value: self.channels_config.get(ChannelType.WEBSITE.value, True),
        }

        # Initialize channel_schedules if it's None
        if self.channel_schedules is None:
            self.channel_schedules = {}

