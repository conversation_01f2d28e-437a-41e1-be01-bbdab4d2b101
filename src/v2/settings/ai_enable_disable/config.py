"""
AI Status Configuration Utilities.
"""

from datetime import datetime
from src.v2.settings.ai_enable_disable.model import AIStatusConfig, ChannelType

async def get_ai_status_config(db):
    """
    Get the current AI status configuration from the database.

    Args:
        db: MongoDB database connection

    Returns:
        AIStatusConfig: The current AI status configuration
    """
    # Get AI status configuration from settings collection
    config_doc = await db.settings.find_one({"name": "ai_status"})

    if not config_doc:
        # Create default configuration if not found
        default_config = AIStatusConfig(enabled=True)

        # Ensure all channels are enabled by default
        default_config.ensure_all_channels()

        # Save the default configuration to the database
        await db.settings.insert_one({
            "name": "ai_status",
            "enabled": True,
            "schedule": None,
            "start_time": None,
            "end_time": None,
            "channels_config": default_config.channels_config,
            "channel_schedules": {},
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        })

        return default_config

    # Get channels_config from document or create empty dict
    channels_config = config_doc.get("channels_config", {})
    channel_schedules = config_doc.get("channel_schedules", {})

    # Create AIStatusConfig from document
    config = AIStatusConfig(
        enabled=config_doc.get("enabled", True),
        schedule=config_doc.get("schedule"),
        start_time=config_doc.get("start_time"),
        end_time=config_doc.get("end_time"),
        channels_config=channels_config,
        channel_schedules=channel_schedules
    )

    # Ensure all channel options are present with default values
    config.ensure_all_channels()

    return config


async def update_ai_status_config(db, config: AIStatusConfig, user_id: str):
    """
    Update the AI status configuration in the database.

    Args:
        db: MongoDB database connection
        config: The new AI status configuration
        user_id: ID of the user making the update

    Returns:
        bool: True if update was successful, False otherwise
    """
    # Ensure all channels are present with default values
    config.ensure_all_channels()

    # Convert config to dictionary
    config_dict = config.model_dump()

    # Check if the document exists
    existing_doc = await db.settings.find_one({"name": "ai_status"})

    if existing_doc:
        # Update the existing document
        result = await db.settings.update_one(
            {"name": "ai_status"},
            {
                "$set": {
                    **config_dict,
                    "updated_at": datetime.now(),
                    "updated_by": user_id,
                }
            }
        )
    else:
        # Insert a new document with all required fields
        result = await db.settings.insert_one({
            "name": "ai_status",
            **config_dict,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "updated_by": user_id
        })

    # Check if operation was successful
    if existing_doc:
        return result.acknowledged
    else:
        return result.acknowledged if hasattr(result, 'acknowledged') else bool(result.inserted_id)


def is_ai_currently_enabled(config: AIStatusConfig, channel: str = None) -> bool:
    """
    Check if AI is currently enabled based on configuration, current time, and channel.
    Handles all scenarios including no schedules, global settings, and exceptions.

    Args:
        config: The AI status configuration
        channel: Optional channel to check (if None, only checks global status)

    Returns:
        bool: True if AI is currently enabled, False otherwise
    """
    try:
        # If config is None, return False
        if not config:
            return False

        # Special case for Playground - always enabled
        if channel == "Playground":
            return True

        # If main toggle is off, AI is disabled for all channels
        if not config.enabled:
            return False

        # Get current time
        current_time = datetime.now().time()

        # Check channel-specific schedule if channel is provided
        if channel and channel in config.channel_schedules:
            try:
                channel_schedule = config.channel_schedules[channel]
                if channel_schedule and channel_schedule.schedule:
                    start_time_obj = channel_schedule.get_start_time_obj()
                    end_time_obj = channel_schedule.get_end_time_obj()

                    if start_time_obj and end_time_obj:
                        # Check if current time is within scheduled hours
                        is_within_schedule = (
                            start_time_obj <= current_time <= end_time_obj
                            if start_time_obj <= end_time_obj
                            else (current_time >= start_time_obj or current_time <= end_time_obj)
                        )

                        # If schedule is "enable", AI is enabled during scheduled hours
                        # If schedule is "disable", AI is disabled during scheduled hours
                        channel_schedule_enabled = (
                            (channel_schedule.schedule == "enable" and is_within_schedule) or
                            (channel_schedule.schedule == "disable" and not is_within_schedule)
                        )

                        # If channel has its own schedule, use that instead of global schedule
                        return channel_schedule_enabled and config.channels_config.get(channel, True)
            except Exception:
                # If any error occurs in channel schedule check, fall back to global schedule
                pass

        # Check global schedule-based enablement
        schedule_enabled = True
        if config.schedule:
            try:
                # Get time objects for comparison
                start_time_obj = config.get_start_time_obj()
                end_time_obj = config.get_end_time_obj()

                if start_time_obj and end_time_obj:
                    # Check if current time is within scheduled hours
                    is_within_schedule = (
                        start_time_obj <= current_time <= end_time_obj
                        if start_time_obj <= end_time_obj
                        else (current_time >= start_time_obj or current_time <= end_time_obj)
                    )

                    # If schedule is "enable", AI is enabled during scheduled hours
                    # If schedule is "disable", AI is disabled during scheduled hours
                    schedule_enabled = (
                        (config.schedule == "enable" and is_within_schedule) or
                        (config.schedule == "disable" and not is_within_schedule)
                    )
            except Exception:
                # If any error occurs in global schedule check, assume schedule is enabled
                schedule_enabled = True

        # If no channel specified, return the global status
        if not channel:
            return schedule_enabled

        # Check if the channel is enabled in the channels configuration
        # Default to True if channel is not found (all channels enabled by default)
        try:
            channel_enabled = config.channels_config.get(channel, True)
        except Exception:
            # If any error occurs in channel config check, assume channel is enabled
            channel_enabled = True

        # AI is enabled only if both the global schedule allows it AND the specific channel is enabled
        return schedule_enabled and channel_enabled

    except Exception:
        # If any unexpected error occurs, return False for safety
        return False
