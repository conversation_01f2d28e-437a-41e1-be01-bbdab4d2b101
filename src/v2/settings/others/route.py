from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB

router = APIRouter(
    tags=["API Keys"],
)

@router.get("/api_keys")
async def get_api_keys(current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        result = await current_user.async_db.settings.find_one({"name": "env"})
        
        if not result or "config" not in result:
            raise HTTPException(status_code=404, detail="Config not found")

        config = result["config"]
        google_api_key = config.get("GOOGLE_API_KEY")
        openai_api_key = config.get("OPENAI_API_KEY")

        return JSONResponse(content={
            "GOOGLE_API_KEY": google_api_key,
            "OPENAI_API_KEY": openai_api_key
        })

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
