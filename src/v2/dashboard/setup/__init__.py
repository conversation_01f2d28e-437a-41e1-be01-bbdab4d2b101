from fastapi import APIRouter

from src.v2.dashboard.setup.get_doc import router as get_doc
setup_routers = APIRouter()

setup_routers.include_router(get_doc)  
# from src.routes.setup.routes import router as setup_router
# from src.routes.setup.category import router as category_router
# from src.routes.setup.general_info import router as business_router
# from src.routes.setup.product import router as product_router

# setup_routers.include_router(category_router)
# setup_routers.include_router(business_router)
# setup_routers.include_router(product_router)
# setup_routers.include_router(setup_router)
