# from fastapi import APIRouter, UploadFile, File, HTTPException, Form,WebSocket,WebSocketDisconnect,WebSocketException,Depends
# from src.core.security import get_tenant_info
# from src.models.user import UserTenantDB
# from typing import List, Optional
# import os
# from src.helper.file_processing import (
#     process_files,
#     process_urls,
#     extract_text_and_images,
#     format_extracted_data,
#     create_nodes,
#     MinIOClient
# )
# from src.reply.minio_client import MinIO<PERSON>lient, MinIOConfig
# from src.models.websocketmanager import ConnectionManager
# from dotenv import load_dotenv

# load_dotenv()

# router = APIRouter(tags=["Document Handler"])
# # Global status dictionary
# status = {
#     "Uploading Files": "Pending",
#     "Extracting Text": "Pending",
#     "Setting up Knowledge Base": "Pending",
#     "Setting up Database": "Pending",
#     "Finishing Up": "Pending"
# }


# manager = ConnectionManager()


# # @router.websocket("/setup_files/{user_id}")
# # async def websocket_endpoint(websocket: WebSocket, user_id: str):
# #     """WebSocket connection for file upload progress tracking."""
    
# #     # If the user is already connected, disconnect the previous session
# #     if manager.is_connected(user_id):
# #         manager.disconnect(user_id)
    
# #     await manager.connect(websocket, user_id)

# #     try:
# #         while True:
# #             await websocket.receive_text()  # Keep connection alive
# #     except WebSocketDisconnect:
# #         manager.disconnect(user_id)



# @router.post("/process-documents")
# async def process_documents(
#     files: Optional[List[UploadFile]] = File(None),
#     urls: Optional[str] = Form(None),
#     current_user: UserTenantDB = Depends(get_tenant_info),
# ):
#     """Processes documents and sends progress updates via WebSocket."""
#     try:

#         async def update_status(step: str, state: str):
#             """Update status and notify the user via WebSocket."""
#             status[step] = state
#             if manager.is_connected(current_user.user.id):
#                 await manager.send_message(current_user.user.id, f"{step}: {state}")

#         await update_status("Uploading Files", "In Progress")
        
#         #conerting all files types or urls to a list of UploadFile
#         file_pdfs = await process_files(files)
#         url_pdfs = await process_urls(urls)
#         all_pdfs = file_pdfs + url_pdfs
#         await update_status("Uploading Files", "Completed")

#         if not all_pdfs:
#             return {"message": "No valid documents processed"}

#         config = MinIOConfig(
#             minio_url=os.getenv("minio_url"),
#             access_key=os.getenv("access_key"),
#             secret_key=os.getenv("secret_key"),
#             bucket_name="eko.dramit",
#         )
#         minio_client = MinIOClient(config)

#         await update_status("Extracting Text", "In Progress")
        
#         #extracting text and images from all pdfs
#         raw_data = await extract_text_and_images(all_pdfs, minio_client)
#         await update_status("Extracting Text", "Completed")

#         #formatting extracted data
#         await update_status("Setting up Knowledge Base", "In Progress")
#         formatted_data = format_extracted_data(raw_data)
#         await update_status("Setting up Knowledge Base", "Completed")

#         #creating nodes
#         await update_status("Setting up Database", "In Progress")
#         nodes = create_nodes(formatted_data)
#         await update_status("Setting up Database", "Completed")

#         await update_status("Finishing Up", "Completed")

#         processed_files = [file.filename for file in all_pdfs]

#         return {
#             "processed_files": processed_files,
#             "formatted_data": formatted_data,
#             "nodes": nodes
        
#         }
#     except Exception as e:
#         raise HTTPException(status_code=500, detail=str(e))