from src.v2.dashboard.overview.total_messages import total_messages
from datetime import datetime
import math
def ai_horizon(db, start_date, end_date, category_id,product_id) -> dict:
    """
    Fetch the total number of messages for a given period.

    Args:
        db (Database): The database object.
        start_date (datetime): The start date of the period.
        end_date (datetime): The end date of the period.
        category_id (str): The ID of the category.
        page (int): The page number for pagination.

    # ai horizon

    no_of_msgs_reply_from_a_agent = 400 # (take avg reply from agent)
    ai_factor = no_of_msgs_reply_from_ai / no_of_msgs_reply_from_a_agent

    Returns:
        dict: A dictionary containing the name value and help
    """

    no_of_days = (end_date - start_date).days

    no_of_msgs_reply_from_ai = total_messages(db, start_date, end_date, product_id, category_id).get("value") / no_of_days
    no_of_msgs_sent_from_a_agent = db.settings.find_one({"name":"overview"}).get("no_of_msgs_sent_from_a_agent", 500)

    ai_factor = no_of_msgs_reply_from_ai / no_of_msgs_sent_from_a_agent
    
    # Prepare the response
    response = {
        "name": "AI Horizon",
        "value": math.ceil(ai_factor),
        "help": "Number of Human Agents AI can Replace",
    }

    return response
