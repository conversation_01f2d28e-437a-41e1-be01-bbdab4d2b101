def trending_category(db, start_date, end_date, product_id,total_documents, page):
    """
    Fetch the trending categories for a given period.

    Args:
        db (Database): The database object.
        start_date (datetime): The start date of the period.
        end_date (datetime): The end date of the period.
        product_id (str): The ID of the product (optional, if needed).
        page (int): The page number for pagination.

    Returns:
        dict: A dictionary containing the trending categories, total count, and total pages.
    """

    # Aggregation pipeline to find trending categories
    pipeline = [
    # Match documents within the date range
    {
        "$match": {
            "created_at": {
                "$gte": start_date,
                "$lt": end_date
            }
        }
    },
    # Unwind the categories array
    {
        "$unwind": "$response.categories"
    },
    # Group by each category and count occurrences
    {
        "$group": {
            "_id": "$response.categories",  # Group by each category
            "category_count": {"$sum": 1}   # Count occurrences of each category
        }
    },
    # Sort by category count in descending order
    {
        "$sort": {
            "category_count": -1
        }
    },
    # limt to 5
    {
        "$limit": total_documents
    },

    # Add a facet stage
    {
        "$facet": {
            "categories": [  # Apply pagination here
                {"$skip": 5 * (page - 1)},
                {"$limit": 5},
            ],
            "total_count": [  # Calculate total count separately
                {"$count": "total"}
            ]
        }
    }
]


    # Execute the aggregation pipeline
    result = list(db.ai_response.aggregate(pipeline))

    # Extract the total count and calculate total pages
    total_count = result[0]["total_count"][0]["total"] if result[0]["total_count"] else 0
    total_pages = (total_count // 5) + (1 if total_count % 5 > 0 else 0)
    
    # Prepare the response
    response = {
        "name": "Trending Categories",
        "value": result[0]["categories"],
        "total_count": total_count,
        "total_pages": total_pages,
        "current_page": page
    }

    return response
