from functools import singledispatch
from fastapi import APIRouter, HTTPException, Depends, Query
from openai import BaseModel
from pydantic import model_validator, root_validator
from src.helper.chat_topic_identifier import generate_topics_for_chat_messages
from src.helper import logger, convert_objectid_to_str
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.customers import CustomerModel, AllCustomersRequest
from typing import Any, List, Dict
from bson import ObjectId
import asyncio
from datetime import date, datetime, time, timedelta
from src.v2.dashboard.overview.usage_metrics import usage_router
from src.v2.dashboard.overview import (
    total_messages,
    trending_product,
    trending_category,
    total_customers,
    ai_response_count,
    distinct_categories,
    trending_sentiment,
    ai_horizon,
    neuro_capacity,
    beauty_AI_metrics
)

loggers = logger.setup_new_logging(__name__)

router = APIRouter(prefix="/reporting", tags=["Reporting"])


router.include_router(usage_router)

# ---- Single Dispatch Date Parser ----
@singledispatch
def parse_to_date(value: Any) -> date:
    raise ValueError(f"Unsupported date format: {value}")

@parse_to_date.register
def _(value: str) -> date:
    formats = [
        "%Y-%m-%d",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%dT%H:%M:%S.%fZ",
        "%Y-%m-%dT%H:%M:%SZ",
        "%Y-%m-%dT%H:%M:%S",
    ]
    for fmt in formats:
        try:
            return datetime.strptime(value, fmt).date()
        except ValueError:
            continue
    raise ValueError(f"Unsupported date format: {value}")

@parse_to_date.register
def _(value: datetime) -> date:
    return value.date()

@parse_to_date.register
def _(value: date) -> date:
    return value

# ---- Pydantic Model ----
class metricDate(BaseModel):
    start_date: datetime
    end_date: datetime

    @root_validator(pre=True)
    def adjust_date_times(cls, values):
        try:
            start_date = parse_to_date(values.get('start_date'))
            end_date = parse_to_date(values.get('end_date'))

            values['start_date'] = datetime.combine(start_date, time.min)  # 00:00:00
            values['end_date'] = datetime.combine(end_date, time.max)      # 23:59:59.999999
        except Exception as e:
            raise ValueError(f"Date parsing failed: {e}")

        return values    

@router.get("/metrics")
async def fetch_ai_metrics(
    start_date: datetime = Query(
        default=datetime.strptime("2024-12-01", "%Y-%m-%d")
    ),
    end_date: datetime = Query(
        default=datetime.strptime("2025-01-31", "%Y-%m-%d")
    ),
    product_id: str | None = None, 
    category_id: str | None = None,
    current_user: UserTenantDB = Depends(get_tenant_info),
    ):

    return [
        total_messages.total_messages(current_user.db, start_date, end_date, product_id, category_id),
        total_customers.total_customers(current_user.db, start_date, end_date, category_id),
        ai_response_count.ai_response_count(current_user.db, start_date, end_date, product_id, category_id),
        trending_sentiment.trending_sentiment(current_user.db, start_date, end_date, product_id),
        distinct_categories.distinct_categories(current_user.db, start_date, end_date, category_id),
        ai_horizon.ai_horizon(current_user.db, start_date, end_date, category_id, product_id),
        {
            "name":"Flawless Factor",
            "value": "99%",
            "help":"Overall System Accuracy"

        },
        neuro_capacity.neuro_capacity(current_user.db, start_date, end_date, category_id, product_id)
    ]


@router.get("/trending_categories")
async def fetch_trending_categories(
    start_date: datetime = Query(
        default=datetime.strptime("2024-12-01", "%Y-%m-%d")
    ),
    end_date: datetime = Query(
        default=datetime.strptime("2025-01-31", "%Y-%m-%d")
    ),
    category_id: str | None = None,
    total_documents: int | None = 10,
    page: int | None = 1,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    return trending_category.trending_category(current_user.db, start_date, end_date, category_id, total_documents, page)

@router.get("/trending_products")
async def fetch_trending_products(
    start_date: datetime = Query(
        default=datetime.strptime("2024-12-01", "%Y-%m-%d")
    ),
    end_date: datetime = Query(
        default=datetime.strptime("2025-01-31", "%Y-%m-%d")
    ),
    product_id: str | None = None, 
    total_documents : int | None = 10,
    page: int | None = 1,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    return trending_product.trending_product(current_user.db, start_date, end_date, product_id, total_documents, page)




@router.post("/average_processing_time")
async def fetch_average_processing_time(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):

    return beauty_AI_metrics.avg_processing_time(current_user.db, dates.start_date, dates.end_date)


@router.post("/channel_msg_count")
async def fetch_channel_msg_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    return beauty_AI_metrics.channel_message_count(current_user.db, dates.start_date, dates.end_date)


@router.post("/cta_count")
async def fetch_CTA_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
   
    return beauty_AI_metrics.CTA_count(current_user.db,  dates.start_date, dates.end_date)


@router.post("/cta_type_count")
async def fetch_CTA_type_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return beauty_AI_metrics.CTA_type_count(current_user.db, dates.start_date, dates.end_date)

@router.post("/evaluation_count")
async def fetch_evaluation_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return beauty_AI_metrics.evaluation_count(current_user.db, dates.start_date, dates.end_date)


@router.post("/message_count")
async def fetch_message_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return beauty_AI_metrics.message_and_cta_count(current_user.db, dates.start_date, dates.end_date)

@router.post("/unique_users_per_day")
async def fetch_unique_users_per_day(
     dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return beauty_AI_metrics.unique_users_per_day(current_user.db, dates.start_date, dates.end_date)    


@router.post("/language_count")
async def fetch_language_count(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return beauty_AI_metrics.language_count(current_user.db, dates.start_date, dates.end_date)

@router.post("/human-intervention")
async def fetch_human_intervention(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    
    return await beauty_AI_metrics.human_reply(current_user.db, dates.start_date, dates.end_date)

# @router.get("/message_topic")
# async def fetch_message_topic(
#     dates: metricDate,
#     current_user: UserTenantDB = Depends(get_tenant_info),
    
# ):
#     result=await beauty_AI_metrics.message_topic(current_user.db, dates.start_date, dates.end_date)

    
#     return result


@router.post("/topic_count")
async def generate_topics(
    dates: metricDate,
    current_user: UserTenantDB = Depends(get_tenant_info),
    

):
    data= await  beauty_AI_metrics.message_topic_count(current_user.async_db, dates.start_date, dates.end_date)

    return data


