def trending_sentiment(db, start_date, end_date, product_id) -> dict:
    """
    Find the most popular primary product within a given time frame.

    Args:
        db (Database): The MongoDB database object.
        start_date (datetime): The start date for filtering.
        end_date (datetime): The end date for filtering.

    Returns:
        dict: A dictionary containing the most popular primary product and its count.
    """
    # MongoDB aggregation pipeline
    pipeline = [
        # Match documents within the date range
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                }
            }
        },
        # Group by primary_product and count occurrences
        {
            "$group": {
                "_id": "$response.sentiment", 
                "count": {"$sum": 1}               
            }
        },
        # Sort by count in descending order
        {
            "$sort": {
                "count": -1
            }
        },
        # Limit to the top result
        {
            "$limit": 1
        }
    ]

    # Execute the aggregation pipeline
    result = list(db.ai_response.aggregate(pipeline))

    return {"name":"Trending Sentiment","value":result}