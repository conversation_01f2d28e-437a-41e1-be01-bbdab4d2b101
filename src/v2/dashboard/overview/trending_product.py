def trending_product(db, start_date, end_date, category_id,total_documents, page) -> dict:
    """
    Fetch the total number of messages for a given period.

    Args:
        db (Database): The database object.
        start_date (datetime): The start date of the period.
        end_date (datetime): The end date of the period.
        category_id (str): The ID of the category.
        page (int): The page number for pagination.

    Returns:
        dict: A dictionary containing the trending products, total count, and total pages.
    """

    # Query MongoDB for the total number of messages
    pipeline = [
        # Match documents within the date range
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                },
                "request.primary_product": {
                    "$nin": [None, "string", ""]  # Exclude None and "string"
                },
                "request.category_id": category_id  # Filter by category_id
            }
        },
        # Group by primary_product and count occurrences
        {
            "$group": {
                "_id": "$request.primary_product",  # Group by primary_product
                "message_count": {"$sum": 1}       # Count messages per product
            }
        },
        # Sort by message count in descending order
        {
            "$sort": {
                "message_count": -1
            }
        },
        {
            "$limit": total_documents
        },
        # Add a facet stage
        {
            "$facet": {
                "products": [  # Apply pagination here
                    {"$skip": 5 * (page - 1)},
                    {"$limit": 5},
                ],
                "total_count": [  # Calculate total count separately
                    {"$count": "total"}
                ]
            }
        }
    ]

    # Execute the aggregation pipeline
    result = list(db.ai_response.aggregate(pipeline))
    
    # Extract the total count and calculate total pages
    total_count = result[0]["total_count"][0]["total"] if result[0]["total_count"] else 0
    total_pages = (total_count // 5) + (1 if total_count % 5 > 0 else 0)
    
    # Prepare the response
    response = {
        "name": "Trending Product",
        "value": result[0]["products"],
        "total_count": total_count,
        "total_pages": total_pages,
        "current_page": page
    }

    return response
