

def total_messages(db, start_date, end_date, product_id, category_id) -> dict:
    """
    Fetch the total number of messages for a given period.

    Args:
        db (Database): The database object.
        start_date (datetime): The start date of the period.
        end_date (datetime): The end date of the period.
        product_id (str): The ID of the product.
        category_id (str): The
    Returns:
        int: The total number of messages.
    """

    # Query MongoDB for the total number of messages

    total_messages = db.ai_response.count_documents({
        "created_at": {
            "$gte": start_date,
            "$lt": end_date
        }
    })

    return {"name":"Total Messages","value":total_messages}