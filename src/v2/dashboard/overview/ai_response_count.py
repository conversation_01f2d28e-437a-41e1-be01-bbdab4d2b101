
def ai_response_count(db, start_date, end_date, product_id, category_id) -> dict:
    """
    Count the total number of AI responses within a given time frame.

    Args:
        db (Database): The MongoDB database object.
        start_date (datetime): The start date for filtering.
        end_date (datetime): The end date for filtering.

    Returns:
        int: The total count of AI responses within the given time frame.
    """
    # Query to count documents in the ai_response collection
    count = db.ai_response.count_documents({
        "created_at": {
            "$gte": start_date,
            "$lt": end_date
        }
    })
    return {"name":"AI Response count","value":count}