from datetime import datetime, timedelta

def total_customers(db, start_date, end_date, category_id) -> dict:
    try:
        # Ensure start_date is before end_date
        if start_date >= end_date:
            return {"name": "Total Customers", "value": 0, "error": "Start date must be before end date."}
        
        # Ensure start_date is before today's last hour (23:59:59)
        today_end = datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)
        if start_date > today_end:
            return {"name": "Total Customers", "value": 0, "error": "Start date must be before today's last hour."}
        
        # Ensure end_date is not after tomorrow
        # tomorrow = datetime.now() + timedelta(days=1)
        # if end_date > tomorrow:
        #     return {"name": "Total Customers", "value": 0, "error": "End date cannot be after tomorrow."}

        # Aggregation pipeline to calculate total users
        pipeline = [
            {
                "$match": {
                    "created_at": {
                        "$gte": start_date,
                        "$lt": end_date
                    },
                    "request.user_id": {"$exists": True}
                }
            },
            {
                "$group": {
                    "_id": "$request.user_id"
                }
            },
            {
                "$count": "total_users"
            }
        ]

        # Execute the aggregation pipeline
        result = list(db.ai_response.aggregate(pipeline))
        
        if not result:
            return {"name": "Total Customers", "value": 0}

        return {"name": "Total Customers", "value": result[0]["total_users"]}

    except Exception as e:
        # Log the error (optional) and return a default response
        print(f"Error occurred: {e}")
        return {"name": "Total Customers", "value": 0, "error": str(e)}
