from fastapi import APIRouter, Depends, HTTPException
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
import asyncio
from bson import ObjectId
from src.helper.logger import setup_new_logging
loggers = setup_new_logging(__name__)
usage_router = APIRouter(tags=["Usage Metrics"])

def calculate_section_cost(section_data: Dict[str, Any], TOKEN_PRICES: dict) -> Dict[str, Any]:
    """
    Calculate cost for a single section of usage data.
    Returns a dictionary with prompt_tokens, completion_tokens, and cost details.
    """
    if not isinstance(section_data, dict) or not section_data:
        return {"prompt_tokens": 0, "completion_tokens": 0, "cost": 0.0}

    model = section_data.get("model", "").lower()
    pricing = TOKEN_PRICES.get(model)

    if not pricing:
        loggers.error(f"Cost calculation for model '{model}' is not supported")
        return {"prompt_tokens": 0, "completion_tokens": 0, "cost": 0.0}  # Skip unknown models

    prompt_tokens = section_data.get("prompt_tokens", 0)
    completion_tokens = section_data.get("completion_tokens", 0)

    prompt_cost = (prompt_tokens / 1000000) * pricing["prompt_tokens"]
    completion_cost = (completion_tokens / 1000000) * pricing["completion_tokens"]

    return {
        "prompt_tokens": prompt_tokens,
        "completion_tokens": completion_tokens,
        "cost": prompt_cost + completion_cost
    }

def calculate_message_cost(usage: Dict[str, Any], TOKEN_PRICES: dict, TWILIO_PRICES: dict) -> Dict[str, Any]:
    """
    Calculate total cost for a single message's usage data, breaking out AI and Twilio costs.
    Returns prompt_tokens, completion_tokens, and cost breakdown.
    """
    ai_cost = 0.0
    twilio_cost = 0.0
    image_process_cost = 0.0
    total_prompt_tokens = 0
    total_completion_tokens = 0

    # Get the prompt_tokens section which contains all subsections
    prompt_tokens_section = usage.get("prompt_tokens", {})
    if isinstance(prompt_tokens_section, dict) and prompt_tokens_section:
        # Process each subsection (input_tokens, lang_usage, etc.)
        for section_name, section_data in prompt_tokens_section.items():
            section_result = calculate_section_cost(section_data, TOKEN_PRICES)
            ai_cost += section_result["cost"]
            total_prompt_tokens += section_result["prompt_tokens"]
            total_completion_tokens += section_result["completion_tokens"]

    # Calculate Twilio costs
    twilio_section = usage.get("twilio_cost", {})
    if isinstance(twilio_section, dict) and twilio_section:
        image_count = twilio_section.get("images", 0)
        text_count = twilio_section.get("text", 0)

        twilio_cost = (image_count * TWILIO_PRICES["image"]) + (text_count * TWILIO_PRICES["text"])

    # Calculate image processing costs
    image_process_cost_section = usage.get("image_process_cost", {})
    if isinstance(image_process_cost_section, dict) and image_process_cost_section:
        # Uses gemini or other model for image processing
        image_cost = calculate_section_cost(image_process_cost_section, TOKEN_PRICES)
        image_process_cost = image_cost["cost"]
        total_prompt_tokens += image_cost["prompt_tokens"]
        total_completion_tokens += image_cost["completion_tokens"]

    # Add image processing cost to AI cost
    ai_cost += image_process_cost

    # Calculate total cost including all components
    total_cost = ai_cost + twilio_cost

    return {
        "prompt_tokens": total_prompt_tokens,
        "completion_tokens": total_completion_tokens,
        "ai_cost": ai_cost,
        "twilio_cost": twilio_cost,
        "image_process_cost": image_process_cost,  # Include this separately for tracking
        "total_cost": total_cost
    }

async def process_batch(batch: List[Dict[str, Any]], TOKEN_PRICES: dict, TWILIO_PRICES: dict) -> Dict[str, Any]:
    """Process a batch of messages in parallel."""
    batch_ai_cost = 0.0
    batch_twilio_cost = 0.0
    batch_total_cost = 0.0
    batch_image_process_cost = 0.0
    batch_count = 0
    total_prompt_tokens = 0
    total_completion_tokens = 0

    for doc in batch:
        usage = doc.get("usage", {})
        cost_breakdown = calculate_message_cost(usage, TOKEN_PRICES, TWILIO_PRICES)

        total_prompt_tokens += cost_breakdown["prompt_tokens"]
        total_completion_tokens += cost_breakdown["completion_tokens"]

        if cost_breakdown["total_cost"] > 0:
            batch_ai_cost += cost_breakdown["ai_cost"]
            batch_twilio_cost += cost_breakdown["twilio_cost"]
            batch_image_process_cost += cost_breakdown.get("image_process_cost", 0.0)
            batch_total_cost += cost_breakdown["total_cost"]
            batch_count += 1

    return {
        "ai_cost": batch_ai_cost,
        "twilio_cost": batch_twilio_cost,
        "image_process_cost": batch_image_process_cost,
        "total_cost": batch_total_cost,
        "count": batch_count,
        "prompt_tokens": total_prompt_tokens,
        "completion_tokens": total_completion_tokens
    }

@usage_router.get("/usage-metrics")
async def get_usage_metrics(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    response_id: Optional[str] = None,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Retrieve usage metrics with detailed cost breakdown in both USD and NPR.
    Includes:
    - Token counts (prompt and completion)
    - Token costs per million
    - Separate metrics for AI costs and Twilio costs
    """
    try:
        # Set default date range to today if not provided
        if not start_date:
            start_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        else:
            start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

        if not end_date:
            end_date = start_date + timedelta(days=1) - timedelta(microseconds=1)
        else:
            end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

        match_filter = {
            "created_at": {"$gte": start_date, "$lte": end_date},
            "response.usage": {"$exists": True}
        }

        if response_id:
            try:
                match_filter["_id"] = ObjectId(response_id)
            except Exception:
                raise HTTPException(status_code=400, detail="Invalid response_id format.")

        # Fetch cost map and NPR_TO_USD_RATE from the database
        price_data = await current_user.async_db.settings.find_one({"name": "cost_map"})
        TOKEN_PRICES = price_data["TOKEN_PRICES"]
        TWILIO_PRICES = price_data["TWILIO_PRICES"]
        NPR_TO_USD_RATE = price_data["NPR_TO_USD_RATE"]

        collection = current_user.async_db.ai_response
        pipeline = [
            {"$match": match_filter},
            {"$project": {"usage": "$response.usage"}}
        ]

        results = await (await collection.aggregate(pipeline)).to_list(length=None)

        # Split results into batches for parallel processing
        batch_size = 100  # Adjust based on your typical data volume
        batches = [results[i:i + batch_size] for i in range(0, len(results), batch_size)]

        # Process batches in parallel
        tasks = [process_batch(batch, TOKEN_PRICES, TWILIO_PRICES) for batch in batches]
        batch_results = await asyncio.gather(*tasks)

        # Combine results from all batches
        total_ai_cost_usd = sum(result["ai_cost"] for result in batch_results)
        total_twilio_cost_usd = sum(result["twilio_cost"] for result in batch_results)
        total_image_process_cost_usd = sum(result.get("image_process_cost", 0.0) for result in batch_results)
        total_cost_usd = sum(result["total_cost"] for result in batch_results)
        total_messages = sum(result["count"] for result in batch_results)
        total_prompt_tokens = sum(result["prompt_tokens"] for result in batch_results)
        total_completion_tokens = sum(result["completion_tokens"] for result in batch_results)
        total_tokens = total_prompt_tokens + total_completion_tokens

        # Calculate average costs per message
        avg_ai_cost_usd = total_ai_cost_usd / total_messages if total_messages > 0 else 0
        avg_twilio_cost_usd = total_twilio_cost_usd / total_messages if total_messages > 0 else 0
        avg_image_process_cost_usd = total_image_process_cost_usd / total_messages if total_messages > 0 else 0
        avg_total_cost_usd = total_cost_usd / total_messages if total_messages > 0 else 0

        # Convert to NPR
        total_ai_cost_npr = total_ai_cost_usd * NPR_TO_USD_RATE
        total_twilio_cost_npr = total_twilio_cost_usd * NPR_TO_USD_RATE
        total_image_process_cost_npr = total_image_process_cost_usd * NPR_TO_USD_RATE
        total_cost_npr = total_cost_usd * NPR_TO_USD_RATE

        avg_ai_cost_npr = avg_ai_cost_usd * NPR_TO_USD_RATE
        avg_twilio_cost_npr = avg_twilio_cost_usd * NPR_TO_USD_RATE
        avg_image_process_cost_npr = avg_image_process_cost_usd * NPR_TO_USD_RATE
        avg_total_cost_npr = avg_total_cost_usd * NPR_TO_USD_RATE

        # Calculate cost per million tokens for each model
        cost_per_million = {}
        for model, pricing in TOKEN_PRICES.items():
            cost_per_million[model] = {
                "prompt_tokens_usd": pricing["prompt_tokens"],
                "prompt_tokens_npr": pricing["prompt_tokens"] * NPR_TO_USD_RATE,
                "completion_tokens_usd": pricing["completion_tokens"],
                "completion_tokens_npr": pricing["completion_tokens"] * NPR_TO_USD_RATE
            }

        return {
            "token_usage": {
                "prompt_tokens": total_prompt_tokens,
                "completion_tokens": total_completion_tokens,
                "total_tokens": total_tokens
            },
            "cost_per_million": cost_per_million,
            "ai_costs": {
                "total_usd": round(total_ai_cost_usd, 6),
                "total_npr": round(total_ai_cost_npr, 2),
                "avg_per_message_usd": round(avg_ai_cost_usd, 6),
                "avg_per_message_npr": round(avg_ai_cost_npr, 2)
            },
            "image_process_costs": {
                "total_usd": round(total_image_process_cost_usd, 6),
                "total_npr": round(total_image_process_cost_npr, 2),
                "avg_per_message_usd": round(avg_image_process_cost_usd, 6),
                "avg_per_message_npr": round(avg_image_process_cost_npr, 2)
            },
            "twilio_costs": {
                "total_usd": round(total_twilio_cost_usd, 6),
                "total_npr": round(total_twilio_cost_npr, 2),
                "avg_per_message_usd": round(avg_twilio_cost_usd, 6),
                "avg_per_message_npr": round(avg_twilio_cost_npr, 2)
            },
            "total_costs": {
                "total_usd": round(total_cost_usd, 6),
                "total_npr": round(total_cost_npr, 2),
                "avg_per_message_usd": round(avg_total_cost_usd, 6),
                "avg_per_message_npr": round(avg_total_cost_npr, 2)
            },
            "total_messages": total_messages,
            "date_range": {
                "start": start_date.isoformat(),
                "end": end_date.isoformat()
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error calculating usage metrics: {str(e)}")