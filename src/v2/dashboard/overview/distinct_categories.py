

def distinct_categories(db, start_date, end_date, category_id) -> dict:
    """
    Fetch all distinct categories within a given time frame.

    Args:
        db (Database): The MongoDB database object.
        start_date (datetime): The start date for filtering.
        end_date (datetime): The end date for filtering.

    Returns:
        dict: A dictionary containing the distinct categories.
    """
    pipeline = [
        # Match documents within the date range
        {
            "$match": {
                "created_at": {
                    "$gte": start_date,
                    "$lt": end_date
                }
            }
        },
        # Unwind the categories array
        {
            "$unwind": "$response.categories"
        },
        # Get distinct categories
        {
            "$group": {
                "_id": None,  # Group all together
                "distinct_categories": {"$addToSet": "$response.categories"}  # Collect unique categories
            }
        }
    ]

    # Execute the aggregation pipeline
    result = list(db.ai_response.aggregate(pipeline))

    
    category=list(db.categories.find())



    # Return the list of distinct categories or an empty list if none exist
    # if result:
    return {
        "name": "Distinct Categories",
        "value": [i.get("name") for i in category]
    }
