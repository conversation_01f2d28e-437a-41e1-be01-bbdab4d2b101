from src.v2.dashboard.overview.total_messages import total_messages

def total_verified_messages(db, start_date, end_date, product_id, category_id) -> int:
    total_messages = db.ai_response.count_documents({
        "created_at": {
            "$gte": start_date,
            "$lt": end_date
        },
        "response.verified":True
        })
    return total_messages

def neuro_capacity(db, start_date, end_date, category_id,product_id) -> dict:
    """
    Calculates the neuro_capacity for a given period.



    Args:
        db (Database): The database object.
        start_date (datetime): The start date of the period.
        end_date (datetime): The end date of the period.
        category_id (str): The ID of the category.
        page (int): The page number for pagination.

    neuro capacity (coverage) = (total_msgs_reply_from_bot(verified)) / (total_msgs_received)
        
    Returns:
        dict: A dictionary containing the name value and help
    """

    # no_of_days = (end_date - start_date).days

    try:
        neuro_capacity = total_verified_messages(
            db, start_date, end_date, product_id, category_id
        ) / total_messages(db, start_date, end_date, product_id, category_id).get("value")
    except:
        neuro_capacity = 0
    # Prepare the response
    response = {
        "name": "Neuro Capacity",
        "value": f"{round(neuro_capacity, 2)*100}%",
        "help": "AI Coverage",
    }

    return response
