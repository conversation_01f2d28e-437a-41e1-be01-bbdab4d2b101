from typing import Optional
from fastapi import APIRouter, Depends, HTTPException
from openai import BaseModel
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from src.helper import logger,convert_objectid_to_str
from bson import ObjectId
from passlib.context import CryptContext
import secrets


loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["User"])

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class ResetPasswordRequest(BaseModel):
    subordinate_id: Optional[str] = None

@router.post("/users/reset_password")
async def reset_password(req: ResetPasswordRequest, user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    users_collection = user_tenant_info.db.users
    
    user = users_collection.find_one({"username": user_tenant_info.user.username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    user_role = user_tenant_info.user.role       
    if user_role != "admin":
        raise HTTPException(status_code=403, 
                            detail="You are not authorized to reset password."
        )
    
    new_random_password = ''.join(secrets.choice('0123456789abcdef') for _ in range(10))
    new_hashed_password = pwd_context.hash(new_random_password)
    
    if req.subordinate_id:
        subordinate = users_collection.find_one({"_id": ObjectId(req.subordinate_id)})
        if not subordinate:
            raise HTTPException(status_code=404, 
                                detail="Subordinate user doesn't exist.")
        
        result = users_collection.update_one(
            {"_id": ObjectId(req.subordinate_id)},
            {"$set": {"hashed_password": new_hashed_password}}
        )
        
        loggers.info(f"Subordinate Password reset successfully by user: {user['username']}")
        return {"message": f"Subordinate Password reset to {new_random_password} successfully."}
    
    result = users_collection.update_one(
        {"_id": ObjectId(user["_id"])},
        {"$set": {"hashed_password": new_hashed_password}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to reset password."
        )
    
    loggers.debug(f"Password reset successfully by user: {user['username']}")
    return {"message": f"Password reset to {new_random_password} successfully."}



class ChangePasswordRequest(BaseModel):
   
    old_password: str
    new_password: str
    confirm_password: str


@router.post("/users/change_password")
async def change_password(req: ChangePasswordRequest, user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    try:
       
        
        users_collection = user_tenant_info.async_db.users
        user =await  users_collection.find_one({"_id": ObjectId(user_tenant_info.user.id)})
        if not user:
           return {"status": 404, "message": "User not found."}
        
        if not pwd_context.verify(req.old_password, user["hashed_password"]):
            return {"status": 403, "message": "Old password is incorrect."}
        

        if req.new_password != req.confirm_password:
            return {"status": 400, "message": "New password and confirm password do not match."}
        
        new_hashed_password = pwd_context.hash(req.new_password)

        result = await users_collection.update_one(
            {"_id": ObjectId(user_tenant_info.user.id)},
            {"$set": {"hashed_password": new_hashed_password}}
        )
        
        if result.matched_count == 0:
           return {"status": 500, "message": "Failed to update password."}
        
        return {"status": 200, "message": "Password changed successfully."}
    
    except Exception as e:
        loggers.error(f"Error changing password: {str(e)}")
        return {"status": 500, "message": "Failed to change password."}
    
    

@router.delete("/user/delete")
async def delete_user(
    user_id: str,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
    ):
    users_collection = user_tenant_info.db.users
    
    try:
        user = users_collection.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")    
                
        del_user = users_collection.delete_one({"_id":ObjectId(user_id)})
        if del_user.deleted_count == 1:
            return{"message":f"User with id {user_id} deleted"}
    except Exception as e:
        loggers.error(e)

@router.get("/users/subordinates")
async def get_agents(page_number: int = 1, 
                     documents_per_page: int = 10, 
                     agent_search_query: Optional[str] = None,
                     role_filter: Optional[str] = None,
                     user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    # print(type(user_tenant_info))
    user_role = user_tenant_info.user.role
    users_collection = user_tenant_info.db.users
    total_agents = []
    find_query = {}
    
    try:            
        if user_role in ["admin", "supervisor"]:
            if agent_search_query:
                find_query = {
                    "username": {"$regex": agent_search_query.lower(), "$options": "i"}
                }
                
            if user_role.lower() == "admin":
                find_query["role"] = {"$in": ["agent", "admin"]}
                
            if role_filter:
                find_query["role"] = role_filter.lower()


            find_query["type"]={"$ne":"bot"}
                            
            skip = (page_number - 1) * documents_per_page
            limit = documents_per_page
            
            count_total = users_collection.count_documents(find_query)
            num_pages = (count_total + documents_per_page - 1) // documents_per_page

            total_agents = list(users_collection.find(find_query, {"hashed_password": 0}).skip(skip).limit(limit))
            if not total_agents:
                raise HTTPException(status_code=403, detail="No matching agents found.")

            total_agents = [convert_objectid_to_str(agent) for agent in total_agents]
                        
        else:
            raise HTTPException(status_code=403, detail="You are not authorized to access this information")
    except Exception as e:
        loggers.error(e)
    
    return {
        "current_page": page_number,
        "total_pages": num_pages,
        "total_documents": count_total, 
        "documents": total_agents
        }

@router.put("/user/update")
async def update_role(
    user_id: str, 
    role_update_request: str,
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
):
    users_collection = user_tenant_info.db.users
    
    try:
        # Validate user_id
        if not ObjectId.is_valid(user_id):
            raise HTTPException(status_code=400, detail="Invalid user ID format")
        
        # Find user by ID
        user = users_collection.find_one({"_id": ObjectId(user_id)})
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Update user role
        update_result = users_collection.update_one(
            {"_id": ObjectId(user_id)}, 
            {"$set": {"role": role_update_request}}
        )
        
        if update_result.matched_count == 0:
            raise HTTPException(status_code=404, detail="User not found or update failed")
        
        return {"message": f"Role for user {user.get('username')} updated to {role_update_request}"}
    
    except Exception as e:
        loggers.error(f"Error updating user role: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
