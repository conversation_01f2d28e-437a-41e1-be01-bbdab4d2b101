from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional
from pydantic import BaseModel
from bson import ObjectId
from datetime import datetime

from src.core.security import get_tenant_info, require_user
from src.models.user import UserTenantDB

# Define Pydantic models for request and response
class PromptBase(BaseModel):
    name: str
    text: str
    model: str

class PromptCreate(PromptBase):
    pass

class PromptUpdate(BaseModel):
    name: Optional[str] = None
    text: Optional[str] = None
    model: Optional[str] = None

class PromptResponse(PromptBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# Initialize router
prompts_router = APIRouter(prefix="/prompts", tags=["Prompts"])


@prompts_router.get("/all")
async def get_all_prompts(
    current_user: UserTenantDB = Depends(require_user(["superadmin"]))
):
    prompts = []
    for prompt in current_user.db.prompt.find():
        prompt["id"] = str(prompt.pop("_id"))
        prompts.append(prompt)

    return prompts  

# Create a new prompt
@prompts_router.post("/", response_model=PromptResponse)
async def create_prompt(
    prompt: PromptCreate,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    # Check if prompt with the same name already exists
    if current_user.db.prompt.find_one({"name": prompt.name}):
        raise HTTPException(status_code=400, detail="Prompt with this name already exists")
    
    # Prepare prompt data
    prompt_data = prompt.dict()
    prompt_data["created_at"] = datetime.utcnow()
    
    # Insert into database
    result = current_user.db.prompt.insert_one(prompt_data)
    
    # Get the created prompt
    created_prompt = current_user.db.prompt.find_one({"_id": result.inserted_id})
    created_prompt["id"] = str(created_prompt.pop("_id"))
    
    return created_prompt


# Get a specific prompt by name
@prompts_router.get("/{prompt_name}", response_model=PromptResponse)
async def get_prompt(
    prompt_name: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    prompt = current_user.db.prompt.find_one({"name": prompt_name})
    
    if not prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    prompt["id"] = str(prompt.pop("_id"))
    return prompt

# Update a prompt
@prompts_router.put("/{prompt_name}")
async def update_prompt(
    prompt_name: str,
    prompt_update: PromptUpdate,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    # Check if prompt exists
    existing_prompt = current_user.db.prompt.find_one({"name": prompt_name})
    if not existing_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Prepare update data
    update_data = {k: v for k, v in prompt_update.dict().items() if v is not None}
    
    # if update_data:
    #     update_data["updated_at"] = datetime.utcnow()
        
        # Update in database
    current_user.db.prompt.update_one(
            {"name": prompt_name},
            {"$set": update_data}
        )
    
    # Get the updated prompt
    updated_prompt = current_user.db.prompt.find_one({"name": prompt_name})
    updated_prompt["id"] = str(updated_prompt.pop("_id"))
    
    return updated_prompt

# Delete a prompt
@prompts_router.delete("/{prompt_name}", response_model=dict)
async def delete_prompt(
    prompt_name: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    # Check if prompt exists
    existing_prompt = current_user.db.prompt.find_one({"name": prompt_name})
    if not existing_prompt:
        raise HTTPException(status_code=404, detail="Prompt not found")
    
    # Delete from database
    current_user.db.prompt.delete_one({"name": prompt_name})
    
    return {"message": f"Prompt '{prompt_name}' deleted successfully"}