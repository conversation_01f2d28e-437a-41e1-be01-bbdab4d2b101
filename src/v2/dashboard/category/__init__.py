from fastapi import APIRouter, Depends, HTTPException
from src.helper import logger
from src.models.user import UserTenantDB
from src.models.category import Category
from src.core.security import get_tenant_info
from typing import Dict, List

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Category"])

# @router.post("/category/all", response_model=List[Category])
# async def get_all_categories(search_filters: Dict | None =None,  current_user: UserTenantDB = Depends(get_tenant_info)):
#     """ Get all categories

#     Args:
#         search_filters (Dict, optional): Search filters for the mongodb find. Defaults to {}.
#     """
#     if search_filters is None:
#         search_filters = {}

#     try:
#         categories_mongo = list(current_user.db.categories.find(search_filters))
#         loggers.info(f"Categories fetched:")
#     except Exception as e:
#         loggers.error(f"Error while fetching all categories: {e}")
#         raise HTTPException(status_code=500, detail="Internal Server Error")

#     if not categories_mongo:
#         return []
    
#     try:
#         categories = [Category(**i) for i in  categories_mongo]
#         return categories
    
#     except Exception as e:
#         import traceback
#         traceback.print_exc()
#         loggers.error(f"Error while creating categories: {e}")
#         raise HTTPException(status_code=500, detail="Internal Server Error")


