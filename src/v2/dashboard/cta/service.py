from typing import List, Dict, Any, Optional
from datetime import datetime, time
from bson import ObjectId
from pymongo import AsyncMongoClient

from src.v2.dashboard.cta.models import CT<PERSON>, CTAFilter, CTAType, resolve_cta
from src.background_tasks.assign_agent import assign_to_cta
from src.models.user import UserTenantDB

from bson import ObjectId
from datetime import datetime, time
from typing import Dict
from src.helper.logger import setup_new_logging
from src.helper.filter_access import get_filter_access

loggers = setup_new_logging(__name__)

async def get_cta_items(filters: CTAFilter, current_user) -> Dict:
    db = current_user.async_db.cta
    match_conditions = {
    }
    blocked_filters = get_filter_access(current_user, "CTA",False)
    print(f"{blocked_filters=}")

    if filters.channels:
        if isinstance(filters.channels, str):
            match_conditions["channel"] = filters.channels
        if  isinstance(filters.channels, list):
            match_conditions["channel"] = {"$in": filters.channels}
    else:
        # match_conditions["channel"] = {"$ne": "Playground"}
        match_conditions["channel"] = {"$nin": blocked_filters.get("channel", [])}
    if filters.assigned_to:
        match_conditions["assigned_to"] = filters.assigned_to
    else:
        match_conditions["assigned_to"] = {"$nin": blocked_filters.get("assigned_to", [])}
    
        
        
    # Build date range

    if filters.start_date or filters.end_date:
        start_dt = datetime.combine(filters.start_date.date(), time.min)
        end_dt = datetime.combine(filters.end_date.date(), time.max)
        # Validate required date filters
        match_conditions["created_at"] = {"$gte": start_dt, "$lte": end_dt}

    # Pagination values
    page = filters.page or 1
    per_page = filters.per_page or 10

    # Base match

    # Optional filters
    optional_fields = ["status", "priority", "created_by", "type"]
    for field in optional_fields:
        value = getattr(filters, field, None)
        if value:
            match_conditions[field] = value
        else:
            match_conditions[field] = {"$nin": blocked_filters.get(field, [])}

    # Handle assigned_to separately to ensure proper handling of null values
    # if filters.assigned_to:
    #     match_conditions["assigned_to"] = filters.assigned_to
    
    # elif filters.assigned_to == "":
    #     # If explicitly looking for unassigned items
    #     unassigned_condition = [
    #         {"assigned_to": None},
    #         {"assigned_to": ""},
    #         {"assigned_to": {"$exists": False}}
    #     ]

    #     # Check if there's already an $or condition (from search)
    #     if "$or" in match_conditions:
    #         # Add $and condition to combine existing $or with unassigned condition
    #         match_conditions["$and"] = [
    #             {"$or": match_conditions.pop("$or")},  # Remove and use existing $or
    #             {"$or": unassigned_condition}
    #         ]
    #     else:
    #         # No existing $or, just add the unassigned condition
    #         match_conditions["$or"] = unassigned_condition



    if filters.search:
        regex = {"$regex": filters.search, "$options": "i"}
        match_conditions["$or"] = [
            {"name": regex},
            {"description": regex},
            {"customer_id": regex},
            {"extra_info.customer_name": regex},
            {"extra_info.whatsapp_number": regex},
            {"extra_info.whatsapp_id": regex},
            {"extra_info.email": regex},
        ]

    pipeline = [
        {"$match": match_conditions},
        {
            "$addFields": {
                "sortDate": {
                    "$cond": [
                        {"$eq": ["$status", "resolved"]},
                        "$resolved_at",
                        "$created_at",
                    ]
                }
            }
        },
        {"$sort": {"sortDate": -1}},
        {
            "$addFields": {
                "validUserId": {
                    "$cond": [
                        {
                            "$and": [
                                {"$ne": ["$customer_id", None]},
                                {"$ne": ["$customer_id", ""]},
                                {"$eq": [{"$type": "$customer_id"}, "string"]},
                                {"$eq": [{"$strLenCP": "$customer_id"}, 24]},
                                {
                                    "$regexMatch": {
                                        "input": "$customer_id",
                                        "regex": "^[0-9a-fA-F]{24}$",
                                    }
                                },
                            ]
                        },
                        {"$toObjectId": "$customer_id"},
                        None,
                    ]
                }
            }
        },
        {
            "$lookup": {
                "from": "customers",
                "let": {"cid": "$customer_id"},
                "pipeline": [
                    {"$match": {"$expr": {"$eq": ["$customer_id", "$$cid"]}}},
                    {"$project": {"customer_name": 1, "whatsapp_id": 1,"country_code": 1, "phone_number": 1, "email":1,"_id": 0}},
                ],
                "as": "customer_info",
            }
        },
        {
            "$lookup": {
                "from": "users",
                "let": {"uid": "$validUserId"},
                "pipeline": [
                    {"$match": {"$expr": {"$eq": ["$_id", "$$uid"]}}},
                    {"$project": {"username": 1, "_id": 0}},
                ],
                "as": "user_info",
            }
        },
        {
            "$addFields": {
                "validAssignedToId": {
                    "$cond": [
                        {
                            "$and": [
                                {"$ne": ["$assigned_to", None]},
                                {"$ne": ["$assigned_to", ""]},
                                {"$eq": [{"$type": "$assigned_to"}, "string"]},
                                {"$eq": [{"$strLenCP": "$assigned_to"}, 24]},
                                {
                                    "$regexMatch": {
                                        "input": "$assigned_to",
                                        "regex": "^[0-9a-fA-F]{24}$",
                                    }
                                },
                            ]
                        },
                        {"$toObjectId": "$assigned_to"},
                        None,
                    ]
                }
            }
        },
        {
            "$lookup": {
                "from": "users",
                "let": {"assigned_uid": "$validAssignedToId"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {
                                "$and": [
                                    {"$ne": ["$$assigned_uid", None]},
                                    {"$eq": ["$_id", "$$assigned_uid"]}
                                ]
                            }
                        }
                    },
                    {"$project": {"username": 1, "_id": 0}},
                ],
                "as": "assigned_user_info",
            }
        },
        {
            "$addFields": {
                "customer_name": {
                    "$cond": [
                        {"$gt": [{"$size": "$customer_info"}, 0]},
                        {"$arrayElemAt": ["$customer_info.customer_name", 0]},
                        {
                            "$cond": [
                                {"$gt": [{"$size": "$user_info"}, 0]},
                                {"$arrayElemAt": ["$user_info.username", 0]},
                                "Unknown",
                            ]
                        },
                    ]
                },
        "customer_phone_number": {
            "$cond": [
                {"$gt": [{"$size": "$customer_info"}, 0]},
                {
                    "$cond": [
                        {
                            "$and": [
                                {"$ne": [{"$arrayElemAt": ["$customer_info.country_code", 0]}, None]},
                                {"$ne": [{"$arrayElemAt": ["$customer_info.phone_number", 0]}, None]}
                            ]
                        },
                        {
                            "$concat": [
                                {"$arrayElemAt": ["$customer_info.country_code", 0]},
                                {"$arrayElemAt": ["$customer_info.phone_number", 0]}
                            ]
                        },
                        {
                            "$cond": [
                                {"$ne": [{"$arrayElemAt": ["$customer_info.whatsapp_id", 0]}, None]},
                                {"$concat": ["+", {"$arrayElemAt": ["$customer_info.whatsapp_id", 0]}]},
                                ""
                            ]
                        }
                    ]
                },
                ""
            ]
        },
                "customer_email": {
                    "$cond": [
                        {"$gt": [{"$size": "$customer_info"}, 0]},
                        {"$arrayElemAt": ["$customer_info.email", 0]},
                        "",
                    ]
                },
                "assigned_to_name": {
                    "$cond": [
                        {
                            "$and": [
                                {"$ne": ["$assigned_to", None]},
                                {"$ne": ["$assigned_to", ""]},
                                {"$gt": [{"$size": "$assigned_user_info"}, 0]}
                            ]
                        },
                        {"$arrayElemAt": ["$assigned_user_info.username", 0]},
                        "Unassigned",
                    ]
                },
                "id": {"$toString": "$_id"},
            }
        },
        {
            "$facet": {
                "data": [{"$skip": (page - 1) * per_page}, {"$limit": per_page}],
                "totalCount": [{"$count": "count"}],
            }
        },
    ]
    # print(f"{pipeline=}")

    try:
        result = await (await db.aggregate(pipeline)).to_list(length=1)

        if not result or not result[0]["data"]:
            return {
                "data": [],
                "meta": {"total": 0, "page": page, "per_page": per_page},
            }

        data = result[0]["data"]
        total = result[0]["totalCount"][0]["count"] if result[0]["totalCount"] else 0

        cta_items = []
        for item in data:
            try:
                # Skip items with invalid assigned_to values
                if "assigned_to" in item and item["assigned_to"] is None:
                    # Set to "Unassigned" explicitly
                    item["assigned_to_name"] = "Unassigned"

                cta_items.append(CTA(**item))
            except Exception as e:
                print(f"[CTA Conversion Error] {e}")
        # "resolved_at": "2025-04-24T15:00:35.353326", sort by resolved at created at
        if filters.status == "resolved":
            cta_items.sort(key=lambda x: x.resolved_at, reverse=True)
        else:
            cta_items.sort(key=lambda x: x.created_at, reverse=True)
        return {
            "data": [item.model_dump() for item in cta_items],
            "meta": {"total": total, "page": page, "per_page": per_page},
        }

    except Exception as e:
        print(f"[Aggregation Error] {e}")
        return {
            "data": [],
            "meta": {"total": 0, "page": page, "per_page": per_page},
            "message": f"Error: {str(e)}",
        }


async def create_cta_item(data: Dict[str, Any], current_user: UserTenantDB) -> CTA:
    """
    Create a new CTA item with intelligent agent assignment

    Args:
        data (Dict[str, Any]): Data for creating CTA
        current_user (UserTenantDB): Current user with database access

    Returns:
        CTA: Created CTA item
    """
    try:
        # Get agent assignment with both ID and name
        agent_data = await assign_to_cta(current_user)
        data["assigned_to"] = agent_data["id"]
        data["assigned_to_name"] = agent_data["name"]

        # Get customer information
        if data.get("customer_id"):
            try:
                # Get customer details from database
                customer = await current_user.async_db.customers.find_one(
                    {"customer_id": data["customer_id"]}
                )

                if customer:
                    # Store customer details in extra_info field
                    data["extra_info"] = data.get("extra_info", {})
                    customer_details = {
                        "customer_name": customer.get("customer_name"),
                        "whatsapp_number": customer.get("whatsapp_number"),
                        "whatsapp_id": customer.get("whatsapp_id"),
                        "email": customer.get("email"),
                        "country_code": customer.get("country_code"),
                        "phone_number": customer.get("phone_number")
                    }
                    data["extra_info"].update(customer_details)

                    # Set top-level fields for direct access
                    data["customer_name"] = customer.get("customer_name")
                    data["customer_phone_number"] = customer.get("whatsapp_number")
                    data["customer_email"] = customer.get("email")
            except Exception as e:
                loggers.error(f"Error fetching customer details: {str(e)}")

        # Remove empty dictionaries to keep data clean
        if data.get("extra_info") and not data["extra_info"]:
            data.pop("extra_info")

        loggers.info(f"Creating CTA item: {data}")
        result = await current_user.async_db.cta.insert_one(data)
        data["id"] = str(result.inserted_id)

        return CTA(**data)
    except Exception as e:
        loggers.error(f"Error creating CTA: {str(e)}")
        raise e


async def update_cta_item(
    cta_id: str, update_data: Dict[str, Any], tenant_id: str, async_db: AsyncMongoClient
) -> CTA:
    """
    Update an existing CTA item

    Args:
        cta_id (str): ID of CTA to update
        update_data (Dict[str, Any]): Update parameters
        tenant_id (str): Tenant ID for authorization
        async_db (AsyncMongoClient): Async database client

    Returns:
        CTA: Updated CTA item
    """
    # if not async_db:
    #     raise ValueError("Async database client is required")

    result = await async_db.cta.find_one_and_update(
        {"_id": ObjectId(cta_id)}, {"$set": update_data}, return_document=True
    )
    result2 = await async_db.ai_response.find_one_and_update(
        {"response.call_to_action.id": cta_id},
        {
            "$set": {
                "response.call_to_action.$.status": update_data.get("status", "opened"),
            }
        },
        return_document=True,
    )

    if not result:
        raise ValueError(f"CTA {cta_id} not found or unauthorized access")
    if not result2:
        raise ValueError(f"CTA {cta_id} not found or unauthorized access")

    result["id"] = str(result["_id"])
    return CTA(**result)


async def delete_cta_item(
    cta_id: str, tenant_id: str, async_db: AsyncMongoClient
) -> None:
    """
    Delete a CTA item

    Args:
        cta_id (str): ID of CTA to delete
        tenant_id (str): Tenant ID for authorization
        async_db (AsyncMongoClient): Async database client
    """

    result = await async_db.cta.delete_one({"_id": ObjectId(cta_id)})

    return result.deleted_count


async def get_cta_item(cta_id: str, tenant_id: str, async_db: AsyncMongoClient) -> CTA:
    """
    Get a specific CTA item by ID

    Args:
        cta_id (str): ID of CTA to retrieve
        tenant_id (str): Tenant ID for authorization
            async_db (AsyncMongoClient): Async database client

    Returns:
        CTA: Retrieved CTA item
    """
    try:
        # if not async_db:
        #     raise ValueError("Async database client is required")

        result = await async_db.cta.find_one({"_id": ObjectId(cta_id)})

        if not result:
            return {"status": "404", "detail": "no detail found"}

        result["id"] = str(result["_id"])

        # Get customer details from extra_info
        extra_info = result.get("extra_info", {}) or {}

        # Set customer name from extra_info if not already set
        if not result.get("customer_name"):
            result["customer_name"] = extra_info.get("customer_name")

        # Set customer phone number from extra_info if not already set
        if not result.get("customer_phone_number"):
            result["customer_phone_number"] = extra_info.get("whatsapp_number")

        # Set customer email from extra_info if not already set
        if not result.get("customer_email"):
            result["customer_email"] = extra_info.get("email")

        # Add assigned_to_name by looking up the username from users collection if not already present
        if not result.get("assigned_to_name") and result.get("assigned_to"):
            try:
                assigned_user = await async_db.users.find_one(
                    {"_id": ObjectId(result["assigned_to"])}
                )
                if assigned_user:
                    result["assigned_to_name"] = assigned_user.get("username", "Unknown")
                else:
                    result["assigned_to_name"] = "Unassigned"
            except Exception as e:
                loggers.error(f"Error fetching assigned user: {str(e)}")
                result["assigned_to_name"] = "Unassigned"
        elif not result.get("assigned_to_name"):
            result["assigned_to_name"] = "Unassigned"

        return CTA(**result)
    except Exception as e:
        import traceback
        print(traceback.format_exc())
        print(f"Error fetching CTA: {str(e)}")
        raise e

from bson import ObjectId


async def get_filters(
    cta_type: Optional[CTAType],
    async_db: AsyncMongoClient,
) -> dict:
    """
    Get filters for CTA
    """
    req = ["status", "priority", "created_by", "assigned_to", "resource"]

    # Build match condition

    # Add date filtering if provided
    if not cta_type:
        match_condition = {}
    else:
        match_condition = {"type": cta_type}

    # Use MongoDB aggregation pipeline to get all distinct values in a single query
    pipeline = [
        {"$match": match_condition},
        {
            "$group": {
                "_id": None,
                **{field: {"$addToSet": f"${field}"} for field in req},
            }
        },
        {"$project": {"_id": 0}},
    ]

    # Execute the aggregation pipeline
    result = await (await async_db.cta.aggregate(pipeline)).to_list(length=1)

    if not result:
        return {}

    result = result[0]

    # Convert string ObjectIds to ObjectId type for created_by
    created_by_ids = [ObjectId(cid) for cid in result.get("created_by", [])]
    # Fetch user names in one query using $in
    users = await async_db.users.find(
        {"_id": {"$in": created_by_ids}}, {"_id": 1, "username": 1}
    ).to_list(None)
    result["created_by"] = [
        {"id": str(user["_id"]), "name": user["username"]} for user in users
    ]

    # Convert string ObjectIds to ObjectId type for assigned_to
    assigned_to_ids = [ObjectId(aid) for aid in result.get("assigned_to", [])]
    # Fetch assigned user names in one query using $in
    assigned_users = await async_db.users.find(
        {"_id": {"$in": assigned_to_ids}}, {"_id": 1, "username": 1}
    ).to_list(None)
    result["assigned_to"] = [
        {"id": str(user["_id"]), "name": user["username"]} for user in assigned_users
    ]

    return result


# async def get_filters():
#     filters = [{
#         "status": ["open", "resolved"],
#         "CTAType": ["ticket", "booking"]
#     }]
#     return filters


async def resolve_cta_type(
    cta_type: resolve_cta, async_db: AsyncMongoClient
) -> Optional[CTAType]:
    """
    Resolve CTA type from string to enum

    Args:
        cta_type (str): CTA type as string
        async_db (AsyncMongoClient): Async database client

    Returns:
        Optional[CTAType]: Resolved CTA type enum
    """
    try:

        update_data = {
            "resolved_by": cta_type.resolved_by,
            "resolved_at": datetime.now(),
            "status": "resolved",
        }
        if cta_type.remarks:
            update_data["remarks"] = cta_type.remarks

        # Update the CTA document
        await async_db.cta.update_one(
            {"_id": ObjectId(cta_type.id)}, {"$set": update_data}
        )

        # Update the corresponding entry in ai_response collection
        await async_db.ai_response.find_one_and_update(
            {"response.call_to_action.id": cta_type.id},
            {
                "$set": {
                    "response.call_to_action.$.status": "resolved",
                }
            },
            return_document=True,
        )
        return True

    except ValueError:
        return None
    except Exception as e:
        print(e)
        return None


async def get_topics(async_db: AsyncMongoClient) -> List[str]:
    """
    Get all topics from the database

    Args:
        async_db (AsyncMongoClient): Async database client

    Returns:
        List[str]: List of topics
    """
    topics = await async_db.cta.distinct("topic")
    if not topics:
        return [
            "Treatment procedure",
            "cost",
            " recovery info",
            "booking inq",
            "doctor/clinic info",
            "accomodation",
        ]
    return topics
