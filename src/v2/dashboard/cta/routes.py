from bson import ObjectId
from fastapi import APIRouter, Depends, HTTPException,Query
from fastapi.responses import JSONResponse
from typing import List, Literal,Optional
from datetime import datetime
from pydantic import BaseModel

from src.v2.dashboard.cta.models import (
    CTA,
    CT<PERSON>reate,
    CT<PERSON>Update,
    CTAFilter,
    CTAResponse,
    CTAType,
    CTAPriority,
    CTAStatus,
    resolve_cta
)
from src.v2.dashboard.cta.service import (
    get_cta_items,
    create_cta_item,
    update_cta_item,
    delete_cta_item,
    get_cta_item,
    get_filters,
    resolve_cta_type,
    get_topics
)
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from src.helper.logger import setup_new_logging
from src.background_tasks.assign_agent import assign_to_cta
from src.v2.external_hooks.whatsapp_webhook.email_sender.send_ import send_cta_email_notification
from src.helper.filter_access import get_filter_access

loggers = setup_new_logging(__name__)

router = APIRouter(prefix="/cta", tags=["cta"])

@router.post("/fetch")
async def list_cta_items(
    request: CTAFilter=Depends() ,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get CTA items with filtering and pagination
    """
    return await get_cta_items(request, current_user)

@router.post("/create", response_model=CTAResponse)
async def create_cta(
    cta: CTACreate,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create a new CTA item
    """
    try:
        cta_dict = cta.model_dump()
        cta_dict["created_by"] = current_user.user.id
        cta_dict["created_at"] = datetime.now()
        cta_dict["updated_at"] = datetime.now()

        # Pass the current_user to create_cta_item for agent assignment
        new_cta: CTA = await create_cta_item(cta_dict, current_user)

        # Return a properly serialized response
        response = CTAResponse(success=True, data=new_cta)
        loggers.info(f"Created new CTA: {new_cta}")
        await send_cta_email_notification(new_cta, current_user)
        return response
    except Exception as e:
        loggers.error(f"Failed to create CTA: {str(e)}")
        return CTAResponse(success=False, error=str(e))

@router.put("/update/{cta_id}", response_model=CTAResponse)
async def update_cta(
    cta_id: str,
    cta_update: CTAUpdate,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Update an existing CTA item
    """
    try:
        update_data = cta_update.model_dump(exclude_unset=True)
        update_data["updated_at"] = datetime.now()

        updated_cta = await update_cta_item(
            cta_id,
            update_data,
            current_user.tenant_id,
            current_user.async_db
        )
        return CTAResponse(success=True, data=updated_cta)
    except Exception as e:
        return CTAResponse(success=False, error=str(e))

@router.delete("/delete/{cta_id}", response_model=CTAResponse)
async def delete_cta(
    cta_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Delete a CTA item
    """
    try:
        deleted_count = await delete_cta_item(
            cta_id,
            current_user.tenant_id,
            current_user.async_db
        )
        if deleted_count == 0:
            return CTAResponse(
                success=False,
                error=f"CTA {cta_id} not found"
            )
        return CTAResponse(
            success=True,
            message=f"CTA {cta_id} deleted successfully"
        )
    except Exception as e:
        return CTAResponse(success=False, error=str(e))

@router.get("/get/{cta_id}", response_model=CTAResponse)
async def get_cta(
    cta_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get a specific CTA item by ID
    """
    try:
        cta = await get_cta_item(
            cta_id,
            current_user.tenant_id,
            current_user.async_db
        )
        return CTAResponse(success=True, data=cta)
    except Exception as e:
        return CTAResponse(success=False, error=str(e))
    
@router.get("/types")
async def get_cta_types(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get CTA types
    """
    # return [{"name": cta_type.value} for cta_type in CTAType]
    return {
        "types": [{"name": cta_type.value} for cta_type in CTAType],
        "status": [{"name": s.value} for s in CTAStatus],
    }


@router.get("/status")
async def get_cta_status(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get CTA status
    """
    return [{"name": cta_status.value} for cta_status in CTAStatus]

@router.get("/priorities")
async def get_cta_priorities(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get CTA priorities
    """
    return [{"name": cta_priority.value} for cta_priority in CTAPriority]




@router.post("/resolve_cta_type")
async def resolve_cta(
    request: resolve_cta,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Resolve CTA type
    """
    return await resolve_cta_type(request, current_user.async_db)




@router.get("/get_topics")
async def get_cta(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Get Topics
    """
    return await get_topics(current_user.async_db)

@router.post("/get_agents")
async def get_agents(current_user: UserTenantDB = Depends(get_tenant_info)):
    cursor = current_user.async_db.users.find(
        {"role": "agent"},
        {"_id": 1, "username": 1}
    )

    return [
        {
            "name": user["username"],
            "key": str(user["_id"]),
        }
        async for user in cursor
    ]



@router.get("/assign_agent")
async def assign_agent(
    cta_id: str,
    assign_to: Optional[str] = Query(None, description="Agent ID to assign the CTA to"),
    mode: Literal["assign", "unassign", "auto-assign"] = Query(..., description="Action mode"),
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Endpoint to assign, unassign, or auto-assign an agent to a CTA.

    Args:
        cta_id (str): The ID of the CTA.
        assign_to (Optional[str]): The ID of the agent to assign (required for "assign" mode).
        mode (Literal["assign", "unassign", "auto-assign"]): The action mode.
        current_user (UserTenantDB): The current user with database access.

    Returns:
        dict: A response indicating the result of the operation.
    """
    try:
        # Validate CTA ID
        if not cta_id :
            raise HTTPException(status_code=400, detail="CTA ID is required")

        # Handle each mode
        if mode == "assign":
            if not assign_to:
                raise HTTPException(status_code=400, detail="assign_to is required for 'assign' mode")

            # Get agent name from database
            try:
                agent = await current_user.async_db.users.find_one({"_id": ObjectId(assign_to)})
                agent_name = agent.get("username", "Unknown") if agent else "Unknown"
            except Exception as e:
                loggers.error(f"Error fetching agent details: {str(e)}")
                agent_name = "Unknown"

            update_query = {"$set": {"assigned_to": assign_to, "assigned_to_name": agent_name}}
            log_message = f"CTA {cta_id} manually assigned to agent {agent_name} (ID: {assign_to})"

        elif mode == "unassign":
            update_query = {"$set": {"assigned_to": None, "assigned_to_name": "Unassigned"}}
            log_message = f"CTA {cta_id} unassigned"

        elif mode == "auto-assign":
            agent_data = await assign_to_cta(current_user)  # Auto-assign logic
            update_query = {"$set": {"assigned_to": agent_data["id"], "assigned_to_name": agent_data["name"]}}
            log_message = f"CTA {cta_id} auto-assigned to agent {agent_data['name']} (ID: {agent_data['id']})"

        # Perform the database update
        result = await current_user.async_db.cta.update_one(
            {"_id": ObjectId(cta_id)},
            update_query
        )

        # Check if the update was successful
        if result.modified_count == 0:
            loggers.warning(f"No changes made for CTA {cta_id}")
            raise HTTPException(status_code=404, detail="CTA not found or no changes applied")

        # Log the operation
        loggers.info(log_message)

        # Return a consistent response
        if mode == "auto-assign":
            return {
                "status": "success",
                "message": log_message,
                "cta_id": cta_id,
                "assigned_to": agent_data["id"],
                "assigned_to_name": agent_data["name"]
            }
        elif mode == "assign":
            return {
                "status": "success",
                "message": log_message,
                "cta_id": cta_id,
                "assigned_to": assign_to,
                "assigned_to_name": agent_name
            }
        else:  # unassign
            return {
                "status": "success",
                "message": log_message,
                "cta_id": cta_id,
                "assigned_to": None,
                "assigned_to_name": "Unassigned"
            }

    except Exception as e:
        loggers.error(f"Error in /assign_agent endpoint: {str(e)}")
        # raise HTTPException(status_code=500, detail="An unexpected error occurred")
        return {
                "status": "fail to assign",
                "message": "no agent found",
                "cta_id": cta_id,
                "assigned_to": None,
                "assigned_to_name": "Unassigned"
        }