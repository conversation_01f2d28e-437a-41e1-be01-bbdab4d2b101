from enum import Enum
from typing import Optional, Literal, List
from pydantic import BaseModel, Field
from datetime import datetime
from bson import ObjectId

class CTAType(str, Enum):
    TICKET = "ticket"
    BOOKING = "booking"
#     AI_IS_STUCK = "follow_up_required"

# class CTATypes(str, Enum):
#     ticket = "ticket"
#     booking = "booking"

# class CTAStatus(str, Enum):
#     open = "open"
#     resolved = "resolved"

# class CTAType(BaseModel):
#     type: CTATypes
#     status: CTAStatus

class CTAStatus(str, Enum):
    OPEN = "open"
    # IN_PROGRESS = "in_progress"
    # CLOSED = "closed"
    # PENDING = "pending"
    # CONFIRMED = "confirmed"
    # CANCELLED = "cancelled"
    RESOLVED = "resolved"

class CTAPriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class CTA(BaseModel):
    id: Optional[str] = None
    name: str
    description: str
    type: CTAType
    status: CTAStatus = CTAStatus.OPEN
    priority: CTAPriority = CTAPriority.MEDIUM
    created_by: Optional[str] = None
    assigned_to: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    customer_id: Optional[str] = None
    channel: Optional[str] = None
    customer_name: Optional[str] = None  # 👈 new field here
    customer_email: Optional[str] = None
    customer_phone_number: Optional[str] = None
    assigned_to_name: Optional[str] = None  # 👈 new field for assigned agent's username
    remarks: Optional[str] = None
    resolved_at:Optional[datetime] = Field(default_factory=datetime.now)
    resolved_by:Optional[str]=None
    extra_info:Optional[dict]=None

    class Config:
        from_attributes = True
        json_encoders = {
            ObjectId: str
        }

    def model_dump(self):
        """Custom model_dump to handle serialization"""
        data = super().model_dump()
        # Convert datetime objects to strings
        for key, value in data.items():
            if isinstance(value, datetime):
                data[key] = value.isoformat()
        return data


    @property
    def formatted_email_context(self) -> dict:
        # Get customer details from extra_info
        extra_info = self.extra_info or {}

        # Get customer name from the most specific source available
        customer_name = self.customer_name or extra_info.get("customer_name", self.name)

        # Get email from the most specific source available
        email = self.customer_email or extra_info.get("email", "Not Available")

        # Get phone number from the most specific source available
        phone_number = self.customer_phone_number or extra_info.get("whatsapp_number", "Not Available")

        return {
            "Patient Name": customer_name,
            "description": self.description,
            "type": self.type,
            "email": email,
            "phone_number": phone_number,
            "channel": self.channel,
            "assigned_to": self.assigned_to_name,
        }

class CTACreate(BaseModel):
    name: str
    description: str
    type: CTAType
    resource: Optional[str] = None
    response_id: Optional[str] = None
    extra_info: Optional[dict] = {}
    status: CTAStatus = CTAStatus.OPEN
    priority: CTAPriority = CTAPriority.MEDIUM
    channel: str
    assigned_to: Optional[str] = None
    customer_id: Optional[str] = None


class CTAUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[CTAType] = None
    status: Optional[CTAStatus] = None
    priority: Optional[CTAPriority] = None
    assigned_to: Optional[str] = None
    remarks: Optional[str] = None

class CTAFilter(BaseModel):
    channels:Optional[str]=None
    search: Optional[str] = None
    type: Optional[Literal["ticket", "booking"]] = None
    status: Optional[CTAStatus] = None
    priority: Optional[CTAPriority] = None
    created_by: Optional[str] = None
    assigned_to:Optional[str]=None
    start_date: Optional[datetime] =None
    end_date: Optional[datetime] = None
    per_page: int = Field(default=10, ge=1, le=100)
    page: int = Field(default=1, ge=1)

class CTAResponse(BaseModel):
    success: bool
    data: Optional[CTA] = None
    message: Optional[str] = None
    error: Optional[str] = None

    class Config:
        json_encoders = {
            ObjectId: str
        }

    def model_dump(self):
        """Custom model_dump to handle serialization"""
        result = {
            "success": self.success,
            "message": self.message,
            "error": self.error
        }

        # Handle CTA data serialization
        if self.data:
            if hasattr(self.data, 'model_dump'):
                result["data"] = self.data.model_dump()
            else:
                # Fallback if data doesn't have model_dump
                result["data"] = str(self.data)

        return result


class resolve_cta(BaseModel):
    cta_type: CTAType
    id: str
    resolved_by: Optional[str]
    resolved_at: Optional[datetime] = None
    remarks: Optional[str] = None