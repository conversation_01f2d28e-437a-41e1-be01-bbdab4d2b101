from pydantic import BaseModel, field_validator, model_validator, Field
from typing import List, Optional, Any
from datetime import datetime


class Message(BaseModel):
    _id: str
    role: str
    content: str
    sender: str
    created_at: Optional[datetime] = None
    user_id: Optional[Any] = None
    media_ids: Optional[List[str]] = Field(default_factory=list)
    reply_urls: Optional[List[str]] = Field(default_factory=list)
    media_values: Optional[str] = None
    chat_ids: Optional[List[str]] = Field(default_factory=list)
    verified_by: Optional[str] = None
    verified_at: Optional[datetime] = None
    verified: Optional[bool] = None
    message_id: Optional[str] = None
    information_gathering: Optional[Any] = None
    processing_time: Optional[float] = None
    products: Optional[str] = None
    sentiment: Optional[str] = None
    language: Optional[str] = None
    evaluation_id: Optional[str] = None
    evaluation_status: Optional[str] = None
    metadata: Optional[List] = Field(default_factory=list)
    cta: Optional[List] = Field(default_factory=list)
    ai_enabled: Optional[bool] = True
    has_credit: Optional[bool] = True

    @field_validator('reply_urls', mode='before')
    @classmethod
    def validate_reply_urls(cls, v):
        """Ensure reply_urls contains only valid strings, filtering out None values."""
        if v is None:
            return []
        if isinstance(v, list):
            return [url for url in v if url is not None and isinstance(url, str)]
        return []

    @model_validator(mode='before')
    @classmethod
    def validate_fields(cls, values):
        if isinstance(values, dict) and values.get("role") == "user":
            values["metadata"] = None
            values.pop("evaluation_id", None)
            values.pop("evaluation_status", None)
        return values

    def dict(self, *args, **kwargs):
        exclude_none = kwargs.pop('exclude_none', True)

        #if content = "" return none
        data= super().model_dump(*args, exclude_none=exclude_none, **kwargs)
        if data.get("content") == "":
            data["content"] = None
        return data

    class Config:
        orm_mode = True

