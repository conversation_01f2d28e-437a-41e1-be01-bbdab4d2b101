from fastapi import APIRouter

dashboard_router = APIRouter(
    # prefix="/dashboard",
    # tags=["Dashboard"]
)

from src.v2.dashboard.users import router as user_router
from src.v2.dashboard.customers import router as customer_router
from src.v2.dashboard.overview import router as overview_router
from src.v2.dashboard.profile import router as profile_router
from src.v2.dashboard.change_password import router as router_user
from src.v2.dashboard.channels.fetch_convo import router as fetch_convo_router
from src.v2.dashboard.customers.new_customer import cust_router
from src.v2.dashboard.prompts import prompts_router
from src.v2.dashboard.setup import setup_routers

dashboard_router.include_router(user_router)
dashboard_router.include_router(customer_router)
dashboard_router.include_router(overview_router)
dashboard_router.include_router(profile_router)
dashboard_router.include_router(router_user)
dashboard_router.include_router(fetch_convo_router)
dashboard_router.include_router(cust_router)
dashboard_router.include_router(prompts_router)
dashboard_router.include_router(setup_routers)
