"""
Models for handling topic generation responses.
"""

import json
import re
from datetime import datetime
from src.helper.logger import setup_new_logging
from src.v2.dashboard.topic.utils.cost import calculate_cost

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

loggers = setup_new_logging(__name__)

class TopicGeneratorResponse:
    def __init__(self, raw_data):
        # Parse the raw data which contains multiple JSON objects (one per line)
        self.responses = []
        self.detailed_info = []

        for line in raw_data.decode('utf-8').strip().split('\n'):
            try:
                response_obj = json.loads(line)
                self.responses.append(response_obj)

                # Extract detailed information during initialization
                try:
                    # Extract all relevant information
                    batch_info = {
                        "custom_id": response_obj.get('custom_id'),  # User ID
                        "id": response_obj.get('id'),  # Batch request ID
                        "response_status": response_obj.get('response', {}).get('status_code'),
                        "request_id": response_obj.get('response', {}).get('request_id'),
                    }

                    # Extract body information
                    body = response_obj.get('response', {}).get('body', {})
                    if body:
                        batch_info.update({
                            "completion_id": body.get('id'),
                            "model": body.get('model'),
                            "created": body.get('created'),
                            "system_fingerprint": body.get('system_fingerprint'),
                            "service_tier": body.get('service_tier'),
                        })

                        # Extract usage information
                        usage = body.get('usage', {})
                        if usage:
                            batch_info.update({
                                "prompt_tokens": usage.get('prompt_tokens'),
                                "completion_tokens": usage.get('completion_tokens'),
                                "total_tokens": usage.get('total_tokens'),
                                "prompt_tokens_details": usage.get('prompt_tokens_details'),
                                "completion_tokens_details": usage.get('completion_tokens_details'),
                            })

                        # Extract content
                        choices = body.get('choices', [])
                        if choices:
                            message = choices[0].get('message', {})
                            batch_info.update({
                                "content": message.get('content'),
                                "role": message.get('role'),
                                "finish_reason": choices[0].get('finish_reason'),
                            })

                    # Add error information if present
                    if response_obj.get('error'):
                        batch_info["error"] = response_obj.get('error')

                    self.detailed_info.append(batch_info)
                except Exception as e:
                    loggers.error(f"Error extracting detailed batch info: {e}")

            except json.JSONDecodeError as e:
                loggers.error(f"Error parsing JSON line: {e}")

    def get_topics_and_summaries(self, custom_id_map=None):
        """
        Process each response and format them as user_id -> topics and summaries.

        Args:
            custom_id_map: Optional dictionary mapping custom_ids to original user_ids
                           for multi-batch processing

        Returns:
            List of dictionaries with user_id, topics, and summary
        """
        result = []
        for response_obj in self.responses:
            try:
                # Get the custom_id from the response
                custom_id = response_obj.get('custom_id')

                # Map the custom_id to the original user_id if a mapping exists
                if custom_id_map and custom_id in custom_id_map:
                    user_id = custom_id_map.get(custom_id)
                    # Log that we're using a mapped ID
                    loggers.info(f"Mapping custom_id {custom_id} to original user_id {user_id}")
                else:
                    user_id = custom_id

                body = response_obj.get('response', {}).get('body', {})
                choices = body.get('choices', [])

                if choices and user_id:
                    content = choices[0].get('message', {}).get('content', '')
                    loggers.debug(f"Processing content for user {user_id}: {content[:200]}..." if len(content) > 200 else f"Processing content for user {user_id}: {content}")

                    # Parse content to extract topics and summary
                    parsed_data = self._parse_content(content)

                    # Log the parsed data for debugging
                    loggers.debug(f"Parsed topics for user {user_id}: {parsed_data.get('topics')}")
                    loggers.debug(f"Parsed summary for user {user_id}: {parsed_data.get('summary')[:100]}..." if len(parsed_data.get('summary', '')) > 100 else f"Parsed summary for user {user_id}: {parsed_data.get('summary')}")

                    # Ensure topics are properly structured
                    topics = parsed_data.get("topics", [])

                    # Post-process topics to handle specific formats
                    processed_topics = []
                    subtopics_by_topic = {}

                    # First pass: identify main topics and collect subtopics
                    for topic in topics:
                        topic_name = topic.get("topic", "")

                        # Check if this is a "Sub-topics: [...]" entry
                        if topic_name.startswith("Sub-topics:") and "[" in topic_name and "]" in topic_name:
                            # This is a subtopics entry - find the previous main topic
                            if processed_topics:
                                main_topic = processed_topics[-1]["topic"]

                                # Extract the subtopics
                                subtopics_text = topic_name[topic_name.find('[')+1:topic_name.find(']')]
                                subtopics = [s.strip() for s in subtopics_text.split(',')]

                                # Store these subtopics for the main topic
                                if main_topic not in subtopics_by_topic:
                                    subtopics_by_topic[main_topic] = []
                                subtopics_by_topic[main_topic].extend(subtopics)
                        else:
                            # This is a regular topic
                            processed_topics.append(topic)

                    # Second pass: add collected subtopics to their main topics
                    for topic in processed_topics:
                        topic_name = topic.get("topic", "")
                        if topic_name in subtopics_by_topic:
                            # Add the collected subtopics to this topic
                            for subtopic_name in subtopics_by_topic[topic_name]:
                                if subtopic_name:  # Skip empty names
                                    topic["sub_topics"].append({"name": subtopic_name})
                                    loggers.debug(f"Added collected subtopic to {topic_name}: {subtopic_name}")

                    # Add the result with processed topics
                    result.append({
                        "user_id": user_id,
                        "topics": processed_topics,
                        "summary": parsed_data.get("summary", "")
                    })
            except Exception as e:
                loggers.error(f"Error processing response for user {user_id}: {e}")
                import traceback
                loggers.error(traceback.format_exc())

        return result

    def get_topics(self):
        # For backward compatibility
        results = self.get_topics_and_summaries()
        # Filter out the summary field
        return [{
            "user_id": item.get("user_id"),
            "topics": item.get("topics", [])
        } for item in results]

    def get_detailed_info(self, include_batch_cost=True):
        """
        Get the detailed batch information that was extracted during initialization.

        Args:
            include_batch_cost: Whether to include batch cost information in the result

        Returns:
            A dictionary containing:
            - detailed_info: List of dictionaries with detailed information for each response
            - batch_cost: Batch cost information (if include_batch_cost is True)
        """
        if include_batch_cost:
            # Calculate batch cost
            batch_cost = self.calculate_batch_cost(model="gpt-4.1-mini")

            return {
                "detailed_info": self.detailed_info,
                "batch_cost": batch_cost
            }
        else:
            return self.detailed_info

    def add_batch_metadata(self, batch_id=None, input_file_id=None, output_file_id=None,
                          batch_created_at=None, batch_status=None):
        """
        Add batch metadata to all detailed information entries.

        Args:
            batch_id: Batch ID
            input_file_id: Input file ID
            output_file_id: Output file ID
            batch_created_at: Batch creation timestamp
            batch_status: Batch status
        """
        for info in self.detailed_info:
            if batch_id:
                info["batch_id"] = batch_id
            if input_file_id:
                info["input_file_id"] = input_file_id
            if output_file_id:
                info["output_file_id"] = output_file_id
            if batch_created_at:
                # Handle different types of batch_created_at
                if isinstance(batch_created_at, datetime):
                    # If it's a datetime object, convert to ISO format
                    info["batch_created_at"] = batch_created_at.isoformat()
                elif isinstance(batch_created_at, (int, float)):
                    # If it's a timestamp (int or float), convert to datetime first
                    try:
                        created_at_dt = datetime.fromtimestamp(batch_created_at)
                        info["batch_created_at"] = created_at_dt.isoformat()
                    except (ValueError, OverflowError, OSError) as e:
                        # If conversion fails, use the original value as a string
                        loggers.error(f"Error converting timestamp to datetime: {e}")
                        info["batch_created_at"] = str(batch_created_at)
                else:
                    # For any other type, convert to string
                    info["batch_created_at"] = str(batch_created_at)
            if batch_status:
                info["batch_status"] = batch_status

    def calculate_batch_cost(self, model="gpt-4.1-mini"):
        """
        Calculate the cost for all responses in the batch.

        Args:
            model: Model name (currently only supports 'gpt-4.1-mini')

        Returns:
            Dictionary containing cost breakdown and total cost for the entire batch
        """
        total_prompt_tokens = 0
        total_completion_tokens = 0
        user_costs = {}

        for info in self.detailed_info:
            user_id = info.get("custom_id")
            prompt_tokens = info.get("prompt_tokens", 0)
            completion_tokens = info.get("completion_tokens", 0)

            # Calculate cost for this response
            cost = calculate_cost(prompt_tokens, completion_tokens, model)

            # Add cost information to the detailed info
            if cost:
                info["cost"] = cost

            # Update totals
            total_prompt_tokens += prompt_tokens
            total_completion_tokens += completion_tokens

            # Update user costs
            if user_id and cost:
                if user_id not in user_costs:
                    user_costs[user_id] = {
                        "prompt_tokens": 0,
                        "completion_tokens": 0,
                        "total_tokens": 0,
                        "prompt_cost": 0,
                        "completion_cost": 0,
                        "total_cost": 0
                    }
                user_costs[user_id]["prompt_tokens"] += prompt_tokens
                user_costs[user_id]["completion_tokens"] += completion_tokens
                user_costs[user_id]["total_tokens"] += prompt_tokens + completion_tokens
                user_costs[user_id]["prompt_cost"] += cost["prompt_cost"]
                user_costs[user_id]["completion_cost"] += cost["completion_cost"]
                user_costs[user_id]["total_cost"] += cost["total_cost"]

        # Calculate total batch cost
        total_cost = calculate_cost(total_prompt_tokens, total_completion_tokens, model)

        # Return comprehensive cost information
        return {
            "batch_summary": total_cost,
            "user_costs": user_costs,
            "total_responses": len(self.detailed_info)
        }

    def _parse_content(self, content):
        # Parse content to extract both topics and summary
        result = {
            "topics": [],
            "summary": ""
        }

        # First try to extract summary using regex
        summary_pattern = re.compile(r'(?:Summary:|####\s*Summary:(?:---|\s*)|\*\*Summary:\*\*)([\s\S]+?)(?:$|\n\n|\n---)')
        summary_match = summary_pattern.search(content)

        if summary_match:
            summary = summary_match.group(1).strip()
            loggers.debug(f"Extracted summary using initial regex: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary using initial regex: {summary}")
            result["summary"] = summary

        # First try to parse as JSON
        try:
            # Try to load it as a JSON to properly format it
            parsed_data = json.loads(content)
            if isinstance(parsed_data, dict):
                # If it's a dictionary, check for topics and summary keys
                if "topics" in parsed_data:
                    result["topics"] = parsed_data["topics"]
                if "summary" in parsed_data:
                    result["summary"] = parsed_data["summary"]
                # Return the parsed data
                return result
            else:
                # If it's not a dictionary, assume it's just topics
                result["topics"] = parsed_data
                return result
        except json.JSONDecodeError as e:
            # If JSON parsing fails, try to parse as a structured text
            try:
                loggers.info(f"Content is not JSON, trying to parse as structured text")

                # Log the raw content for debugging
                loggers.debug(f"Raw content to parse: {content[:200]}..." if len(content) > 200 else f"Raw content to parse: {content}")

                # Check if the content follows the specific format we're seeing
                if content.startswith("Relevant Topics:") and "\n\nSummary:" in content:
                    # Split into topics and summary sections
                    topics_part, summary_part = content.split("\n\nSummary:", 1)
                    topics_section = topics_part
                    summary_section = "Summary: " + summary_part

                    loggers.debug(f"Directly extracted topics section: {topics_section[:100]}..." if len(topics_section) > 100 else f"Directly extracted topics section: {topics_section}")
                    loggers.debug(f"Directly extracted summary section: {summary_section[:100]}..." if len(summary_section) > 100 else f"Directly extracted summary section: {summary_section}")
                else:
                    # Fall back to the original section splitting logic
                    sections = content.split("---")
                    loggers.debug(f"Found {len(sections)} sections after splitting by '---'")

                    # Look for Relevant Topics section
                    topics_section = None
                    summary_section = None

                    for section in sections:
                        # Check for various formats of topic headers
                        if any(header in section for header in [
                            "Relevant Topics:",
                            "**Relevant Topics:**",
                            "#### Relevant Topics:",
                            "####  Relevant Topics:"
                        ]):
                            topics_section = section
                        # Check for various formats of summary headers
                        elif any(header in section for header in [
                            "Summary:",
                            "**Summary:**",
                            "#### Summary:",
                            "####  Summary:",
                            "####  Summary:---"
                        ]):
                            summary_section = section

                    loggers.debug(f"Found topics section: {bool(topics_section)}")
                    loggers.debug(f"Found summary section: {bool(summary_section)}")

                # Parse topics section if found
                if topics_section:
                    # Parse the topics section to extract topics and subtopics
                    topics = self._parse_topics_section(topics_section)

                    # Process the topics to ensure proper hierarchy
                    # Look for subtopics that were incorrectly parsed as main topics
                    result["topics"] = self._reorganize_topics(topics)

                    # Log the final topics structure
                    loggers.debug(f"Final topics structure: {result['topics']}")

                # Parse summary section if found and we don't already have a summary from regex
                if summary_section and not result["summary"]:
                    summary = self._parse_summary_section(summary_section)
                    result["summary"] = summary
                    loggers.debug(f"Extracted summary from section: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary from section: {summary}")

                # If we still don't have a summary, try a more aggressive approach
                if not result["summary"]:
                    # Look for any line containing 'Summary:' and take everything after it
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'Summary:' in line:
                            # Take all remaining lines as the summary
                            remaining_text = '\n'.join(lines[i+1:])
                            if remaining_text.strip():
                                result["summary"] = remaining_text.strip()
                                loggers.debug(f"Found summary using line-by-line search")
                            break

                return result
            except Exception as parsing_error:
                loggers.error(f"Error parsing content as structured text: {parsing_error}")
                return result

    def _parse_topics_section(self, topics_section):
        # Parse the topics section to extract topics and subtopics using regex
        # Log the topics section for debugging
        loggers.debug(f"Parsing topics section: {topics_section[:200]}..." if len(topics_section) > 200 else f"Parsing topics section: {topics_section}")

        # Initialize the topics list
        topics = []

        # First, try to parse using regex for the specific format we're seeing
        # Pattern to match: "- Topic: X\n  - Sub-topics: [Y, Z]" or similar formats
        topic_pattern = re.compile(r'- (?:Topic: )?(.*?)(?:\n\s+- Sub-topics: \[(.*?)\]|$)', re.MULTILINE)
        matches = topic_pattern.findall(topics_section)

        if matches:
            loggers.debug(f"Found {len(matches)} topic matches using regex")

            # Process each match (topic and its subtopics)
            for topic_name, subtopics_str in matches:
                # Clean up the topic name
                topic_name = topic_name.strip()
                if not topic_name:
                    continue

                # Create a new topic
                current_topic = {"topic": topic_name, "sub_topics": []}
                topics.append(current_topic)
                loggers.debug(f"Added main topic: {topic_name}")

                # Process subtopics if any
                if subtopics_str:
                    # Split by comma to get individual subtopics
                    subtopics = [s.strip() for s in subtopics_str.split(',')]

                    # Add each subtopic
                    for subtopic_name in subtopics:
                        if subtopic_name:  # Skip empty names
                            current_topic["sub_topics"].append({"name": subtopic_name})
                            loggers.debug(f"Added subtopic from list: {subtopic_name}")

        # If regex didn't find any topics, fall back to line-by-line parsing
        if not topics:
            loggers.debug("Falling back to line-by-line parsing")
            lines = topics_section.strip().split('\n')
            current_topic = None

            for line in lines:
                line = line.strip()
                if not line or any(line.startswith(header) for header in [
                    "Relevant Topics:",
                    "**Relevant Topics:**",
                    "#### Relevant Topics:",
                    "####  Relevant Topics:"
                ]):
                    continue

                # Check if it's a main topic (starts with '- ')
                if line.startswith('- '):
                    # Extract the topic name
                    topic_text = line[2:].strip()

                    # Check if it contains 'Sub-topics:' - this is a special case
                    if 'Sub-topics:' in topic_text and current_topic is not None:
                        # This is actually a subtopic line for the current topic
                        subtopic_text = topic_text[topic_text.find('Sub-topics:')+len('Sub-topics:'):].strip()

                        # Check if the subtopic text contains a list in square brackets
                        if '[' in subtopic_text and ']' in subtopic_text:
                            # Extract the list part
                            list_part = subtopic_text[subtopic_text.find('[')+1:subtopic_text.find(']')]
                            # Split by comma to get individual subtopics
                            subtopics = [s.strip() for s in list_part.split(',')]

                            # Add each subtopic
                            for subtopic_name in subtopics:
                                if subtopic_name:  # Skip empty names
                                    current_topic["sub_topics"].append({"name": subtopic_name})
                                    loggers.debug(f"Added subtopic from list: {subtopic_name}")
                    else:
                        # This is a main topic
                        # Check if it starts with 'Topic:'
                        if topic_text.startswith('Topic:'):
                            topic_name = topic_text[len('Topic:'):].strip()
                        else:
                            topic_name = topic_text

                        # Create a new topic
                        current_topic = {"topic": topic_name, "sub_topics": []}
                        topics.append(current_topic)
                        loggers.debug(f"Added main topic: {topic_name}")

                # Check if it's a subtopic line (starts with '  - ' or more spaces)
                elif (line.startswith('  - ') or line.startswith('   - ')) and current_topic is not None:
                    subtopic_text = line.lstrip(' -').strip()

                    # Check if it contains 'Sub-topics:'
                    if 'Sub-topics:' in subtopic_text:
                        subtopic_text = subtopic_text[subtopic_text.find('Sub-topics:')+len('Sub-topics:'):].strip()

                    # Check if the subtopic text contains a list in square brackets
                    if '[' in subtopic_text and ']' in subtopic_text:
                        # Extract the list part
                        list_part = subtopic_text[subtopic_text.find('[')+1:subtopic_text.find(']')]
                        # Split by comma to get individual subtopics
                        subtopics = [s.strip() for s in list_part.split(',')]

                        # Add each subtopic
                        for subtopic_name in subtopics:
                            if subtopic_name:  # Skip empty names
                                current_topic["sub_topics"].append({"name": subtopic_name})
                                loggers.debug(f"Added subtopic from list: {subtopic_name}")
                    else:
                        # Regular subtopic format
                        subtopic_name = subtopic_text
                        # Add as a subtopic to the current main topic
                        current_topic["sub_topics"].append({"name": subtopic_name})
                        loggers.debug(f"Added regular subtopic: {subtopic_name}")

        # Post-process to handle any remaining "Sub-topics: [...]" entries
        processed_topics = []

        for topic in topics:
            topic_name = topic["topic"]

            # Check if this is a "Sub-topics: [...]" entry
            if topic_name.startswith("Sub-topics:") and "[" in topic_name and "]" in topic_name:
                # This should be subtopics for the previous topic
                if processed_topics:  # Make sure we have a previous topic
                    previous_topic = processed_topics[-1]

                    # Extract the subtopics
                    subtopics_text = topic_name[topic_name.find('[')+1:topic_name.find(']')]
                    subtopics = [s.strip() for s in subtopics_text.split(',')]

                    # Add each subtopic to the previous topic
                    for subtopic_name in subtopics:
                        if subtopic_name:  # Skip empty names
                            previous_topic["sub_topics"].append({"name": subtopic_name})
                            loggers.debug(f"Added subtopic to previous topic: {subtopic_name}")
            else:
                # This is a regular topic
                processed_topics.append(topic)

        # Log the parsed topics for debugging
        loggers.debug(f"Parsed topics after post-processing: {processed_topics}")

        return processed_topics

    def _parse_summary_section(self, summary_section):
        # Parse the summary section to extract the summary text
        lines = summary_section.strip().split('\n')
        summary_lines = []

        # Log the summary section for debugging
        loggers.debug(f"Parsing summary section: {summary_section}")

        for line in lines:
            line = line.strip()
            # Skip header lines with various formats
            if any(line.startswith(header) for header in [
                "Summary:",
                "**Summary:**",
                "#### Summary:",
                "####  Summary:",
                "####  Summary:---"
            ]):
                continue
            if line:
                summary_lines.append(line)

        summary = "\n".join(summary_lines).strip()
        loggers.debug(f"Extracted summary: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary: {summary}")
        return summary

    def _reorganize_topics(self, topics):
        """
        Reorganize topics to ensure proper hierarchy.
        This method identifies subtopics that were incorrectly parsed as main topics
        and moves them under their parent topics.
        """
        # First, get the predefined topic hierarchy from the prompt
        # This is a simplified approach - in a real implementation, you might want to
        # pass the predefined hierarchy as a parameter
        predefined_hierarchy = {}

        # Create a mapping of topic names to their proper structure
        for topic in topics:
            topic_name = topic["topic"]
            predefined_hierarchy[topic_name] = {
                "is_main": True,  # Assume it's a main topic by default
                "parent": None,   # No parent by default
                "data": topic     # The original topic data
            }

            # Check if this topic appears as a subtopic in any other topic
            for other_topic in topics:
                if other_topic["topic"] != topic_name:  # Don't compare with itself
                    for subtopic in other_topic["sub_topics"]:
                        if subtopic["name"] == topic_name:
                            # This topic is actually a subtopic of another topic
                            predefined_hierarchy[topic_name]["is_main"] = False
                            predefined_hierarchy[topic_name]["parent"] = other_topic["topic"]
                            break

        # Now reorganize the topics based on the hierarchy
        reorganized_topics = []
        processed_topics = set()  # Track topics that have been processed

        # First, add all main topics
        for topic_name, info in predefined_hierarchy.items():
            if info["is_main"] and topic_name not in processed_topics:
                reorganized_topics.append(info["data"])
                processed_topics.add(topic_name)

        # Now, for each main topic, find its subtopics that were incorrectly parsed as main topics
        for topic in reorganized_topics:
            topic_name = topic["topic"]

            # Look for topics that should be subtopics of this topic
            for other_name, info in predefined_hierarchy.items():
                if not info["is_main"] and info["parent"] == topic_name and other_name not in processed_topics:
                    # This is a subtopic that was incorrectly parsed as a main topic
                    # Add its data as a subtopic to the current topic
                    subtopic_data = {"name": other_name}

                    # If the subtopic has its own subtopics, preserve them
                    if info["data"]["sub_topics"]:
                        subtopic_data["sub_topics"] = info["data"]["sub_topics"]

                    topic["sub_topics"].append(subtopic_data)
                    processed_topics.add(other_name)

        # Log the reorganized topics for debugging
        loggers.debug(f"Reorganized topics: {reorganized_topics}")

        return reorganized_topics

    def _clean_content(self, content):
        # For backward compatibility
        parsed_data = self._parse_content(content)
        return parsed_data.get("topics", [])
