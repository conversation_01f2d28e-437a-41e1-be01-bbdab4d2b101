from datetime import datetime
from typing import Dict, List, Optional, TypeVar, Generic
from pydantic import BaseModel

# Generic type for classification
T = TypeVar("T")

class ConversationWrapper(BaseModel, Generic[T]):
    """
    Represents a single conversation entry.
    """
    role: str
    content: str
    created_at: datetime  # Store datetime as an ISO-formatted string
    metadata: Optional[T] = None  # Optional metadata for classification

    class Config:
        extra = "allow"  # Allow extra fields

    def model_dump(self):
        """
        Custom serialization method for ConversationWrapper.
        Converts the created_at datetime to an ISO-formatted string.
        """
        data = super().model_dump()
        data["created_at"] = self.created_at.isoformat()
        return {
            "role": self.role,
            "content": self.content,
            "created_at": data["created_at"],
        }


class SubtopicWrapper(BaseModel, Generic[T]):
    """
    Represents a subtopic with associated conversations.
    """
    name: str


class TopicWrapper(BaseModel, Generic[T]):
    """
    Represents a topic containing multiple subtopics.
    """
    topic: str
    sub_topics: Optional[List[SubtopicWrapper[T]]] = []


class TopicData(BaseModel):
    """
    Represents a collection of topics.
    """
    name: str
    data: List[TopicWrapper]

    class Config:
        extra = "allow"


class PrevInfo(BaseModel, Generic[T]):
    """
    Stores previous summary and topic information.
    """
    prev_summary: str
    prev_topic: Dict[str, TopicWrapper[T]]
