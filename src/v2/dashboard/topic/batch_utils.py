"""
Utility functions for working with OpenAI batch responses.
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional


def extract_batch_info_from_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Extract batch information from a file containing OpenAI batch responses.

    Args:
        file_path: Path to the file containing batch responses

    Returns:
        List of dictionaries with detailed batch information
    """
    try:
        with open(file_path, "r") as f:
            batch_info = json.load(f)
        return batch_info
    except Exception as e:
        print(f"Error extracting batch information from file: {e}")
        return []


def filter_batch_info_by_user_id(
    batch_info: List[Dict[str, Any]], user_id: str
) -> List[Dict[str, Any]]:
    """
    Filter batch information by user ID.

    Args:
        batch_info: List of dictionaries with batch information
        user_id: User ID to filter by

    Returns:
        List of dictionaries with batch information for the specified user ID
    """
    return [info for info in batch_info if info.get("custom_id") == user_id]


def get_batch_usage_stats(batch_info: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Get usage statistics for a batch.

    Args:
        batch_info: List of dictionaries with batch information

    Returns:
        Dictionary with usage statistics
    """
    total_prompt_tokens = 0
    total_completion_tokens = 0
    total_tokens = 0
    user_stats = {}

    for info in batch_info:
        user_id = info.get("custom_id")
        prompt_tokens = info.get("prompt_tokens", 0)
        completion_tokens = info.get("completion_tokens", 0)
        tokens = info.get("total_tokens", 0)

        total_prompt_tokens += prompt_tokens
        total_completion_tokens += completion_tokens
        total_tokens += tokens

        if user_id:
            if user_id not in user_stats:
                user_stats[user_id] = {
                    "prompt_tokens": 0,
                    "completion_tokens": 0,
                    "total_tokens": 0,
                }
            user_stats[user_id]["prompt_tokens"] += prompt_tokens
            user_stats[user_id]["completion_tokens"] += completion_tokens
            user_stats[user_id]["total_tokens"] += tokens

    return {
        "total_prompt_tokens": total_prompt_tokens,
        "total_completion_tokens": total_completion_tokens,
        "total_tokens": total_tokens,
        "user_stats": user_stats,
    }


def example_usage():
    """
    Example usage of batch utility functions.
    """
    # Example batch information file path
    batch_info_file = "batch_info_20240101_120000.json"

    # Extract batch information from file
    batch_info = extract_batch_info_from_file(batch_info_file)

    # Get usage statistics
    usage_stats = get_batch_usage_stats(batch_info)
    print(f"Total tokens used: {usage_stats['total_tokens']}")

    # Filter batch information by user ID
    user_id = "9779816043599"
    user_batch_info = filter_batch_info_by_user_id(batch_info, user_id)
    print(f"Found {len(user_batch_info)} batch responses for user {user_id}")

    # Get user usage statistics
    user_stats = usage_stats["user_stats"].get(user_id, {})
    print(f"User {user_id} used {user_stats.get('total_tokens', 0)} tokens")


if __name__ == "__main__":
    example_usage()
