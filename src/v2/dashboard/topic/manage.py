"""
Topic management service with Pydantic models for type safety.
"""
from typing import Optional, List, Union, TypeVar, Generic, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field
from bson import ObjectId
from pymongo.collection import Collection

# Generic type for topic metadata
T = TypeVar("T")

class PyObjectId(ObjectId):
    """Custom type for handling MongoDB ObjectId fields in Pydantic models."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_core_schema__(cls, _source_type, _handler):
        """
        Generate Pydantic core schema for ObjectId validation.
        Parameters are required by Pydantic but not used in this implementation.
        """
        from pydantic_core import core_schema
        return core_schema.union_schema([
            core_schema.is_instance_schema(ObjectId),
            core_schema.chain_schema([
                core_schema.str_schema(),
                core_schema.no_info_plain_validator_function(cls.validate),
            ])
        ])


class SubtopicModel(BaseModel, Generic[T]):
    """Model representing a subtopic."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    type: str = "subtopic"
    parent_id: Optional[PyObjectId] = None
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    metadata: Optional[T] = None

    model_config = {
        "populate_by_name": True,
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str,
            datetime: lambda dt: dt.isoformat()
        },
        "arbitrary_types_allowed": True
    }

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document format."""
        data = self.model_dump(by_alias=True)
        return data


class TopicModel(BaseModel, Generic[T]):
    """Model representing a main topic with subtopics."""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    name: str
    type: str = "topic"
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    sub_topics: List[SubtopicModel[T]] = Field(default_factory=list)
    metadata: Optional[T] = None

    model_config = {
        "populate_by_name": True,
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str,
            datetime: lambda dt: dt.isoformat()
        },
        "arbitrary_types_allowed": True
    }

    def to_mongo(self) -> Dict[str, Any]:
        """Convert to MongoDB document format without subtopics."""
        data = self.model_dump(by_alias=True)
        data.pop("sub_topics", None)
        return data


class TopicResponse(BaseModel, Generic[T]):
    """Response model for topic operations."""
    id: str
    name: str
    type: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    sub_topics: List[Dict[str, Any]] = Field(default_factory=list)
    metadata: Optional[T] = None

    model_config = {
        "json_encoders": {
            datetime: lambda dt: dt.isoformat()
        }
    }


    def model_dump_filter(self, exclude_fields: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Custom model_dump to format the topic data in the required format:
        {
            "topic": "Topic Name",
            "sub_topics": [
                {"name": "Subtopic 1"},
                {"name": "Subtopic 2"}
            ]
        }
        """
        data = super().model_dump()

        # Rename 'name' to 'topic'
        result = {
            "topic": data.pop("name")
        }

        # Format subtopics to only include the name field
        if data.get("sub_topics"):
            simplified_subtopics = [{"name": sub.get("name")} for sub in data["sub_topics"]]
            if simplified_subtopics:
                result["sub_topics"] = simplified_subtopics

        return result


class TopicService(Generic[T]):
    """Service for managing topics and subtopics with generic metadata support."""

    def __init__(self, db):
        self.collection: Collection = db["topics"]

    async def initialize(self):
        """Initialize the service by creating necessary indexes."""
        # Create indexes if they don't exist
        await self.collection.create_index([("type", 1)])
        await self.collection.create_index([("parent_id", 1)])

    async def create(self, name: str, type: str, parent_id: Optional[Union[str, ObjectId]] = None,
               metadata: Optional[T] = None) -> str:
        """Create a new topic or subtopic."""
        now = datetime.now()

        if type == "topic":
            topic = TopicModel(
                name=name,
                type=type,
                created_at=now,
                metadata=metadata
            )
            doc = topic.to_mongo()
        else:  # subtopic
            subtopic = SubtopicModel(
                name=name,
                type=type,
                created_at=now,
                metadata=metadata
            )
            if parent_id:
                subtopic.parent_id = ObjectId(parent_id)
            doc = subtopic.to_mongo()

        result = await self.collection.insert_one(doc)
        return str(doc["_id"])

    async def get_all(self) -> List[TopicResponse[T]]:
        """Get all topics with their subtopics."""
        docs = await self.collection.find({}).to_list(length=None)
        topics = [d for d in docs if d.get("type") == "topic"]
        subtopics = [d for d in docs if d.get("type") == "subtopic"]

        # Group subtopics by parent
        sub_by_parent = {}
        for sub in subtopics:
            pid = sub.get("parent_id")
            if pid:
                sub_by_parent.setdefault(pid, []).append({
                    "id": str(sub["_id"]),
                    "name": sub["name"],
                    "type": sub["type"],
                    "created_at": sub.get("created_at", datetime.now()),
                    "updated_at": sub.get("updated_at"),
                    "metadata": sub.get("metadata")
                })

        result = []
        for topic in topics:
            topic_response = TopicResponse(
                id=str(topic["_id"]),
                name=topic["name"],
                type=topic["type"],
                created_at=topic.get("created_at", datetime.now()),
                updated_at=topic.get("updated_at"),
                sub_topics=sub_by_parent.get(topic["_id"], []),
                metadata=topic.get("metadata")
            )
            result.append(topic_response)

        return result

    async def get_by_id(self, doc_id: Union[str, ObjectId]) -> Optional[Dict[str, Any]]:
        """Get a topic or subtopic by ID."""
        return await self.collection.find_one({"_id": ObjectId(doc_id)})

    async def get_topic_with_subtopics(self, topic_id: Union[str, ObjectId]) -> Optional[TopicResponse[T]]:
        """Get a topic with all its subtopics."""
        topic = await self.get_by_id(topic_id)
        if not topic or topic.get("type") != "topic":
            return None

        subtopics = await self.collection.find({"parent_id": ObjectId(topic_id)}).to_list(length=None)
        sub_topics_list = []

        for sub in subtopics:
            sub_topics_list.append({
                "id": str(sub["_id"]),
                "name": sub["name"],
                "type": sub["type"],
                "created_at": sub.get("created_at", datetime.now()),
                "updated_at": sub.get("updated_at"),
                "metadata": sub.get("metadata")
            })

        return TopicResponse(
            id=str(topic["_id"]),
            name=topic["name"],
            type=topic["type"],
            created_at=topic.get("created_at", datetime.now()),
            updated_at=topic.get("updated_at"),
            sub_topics=sub_topics_list,
            metadata=topic.get("metadata")
        )

    async def update(self, doc_id: Union[str, ObjectId], new_name: str,
                    metadata: Optional[T] = None, updated_by: Optional[str] = None) -> bool:
        """Update a topic or subtopic name and metadata."""
        update_data = {
            "name": new_name,
            "updated_at": datetime.now(),
        }

        if updated_by is not None:
            update_data["updated_by"] = updated_by

        if metadata is not None:
            update_data["metadata"] = metadata

        result = await self.collection.update_one(
            {"_id": ObjectId(doc_id)},
            {"$set": update_data}
        )
        return result.modified_count > 0

    async def delete(self, doc_id: Union[str, ObjectId]) -> int:
        """Delete a topic or subtopic. If topic, also deletes all subtopics."""
        doc = await self.get_by_id(doc_id)
        if not doc:
            return 0

        if doc.get("type") == "topic":
            # Delete topic and its subtopics
            result = await self.collection.delete_many({
                "$or": [
                    {"_id": ObjectId(doc_id)},
                    {"parent_id": ObjectId(doc_id)}
                ]
            })
            return result.deleted_count
        else:
            # Delete only subtopic
            result = await self.collection.delete_one({"_id": ObjectId(doc_id)})
            return result.deleted_count

    async def reassign_subtopic(self, sub_id: Union[str, ObjectId],
                          new_parent_id: Union[str, ObjectId]) -> bool:
        """Move a subtopic to a different parent topic."""
        result = await self.collection.update_one(
            {"_id": ObjectId(sub_id), "type": "subtopic"},
            {
                "$set": {
                    "parent_id": ObjectId(new_parent_id),
                    "updated_at": datetime.now()
                }
            }
        )
        return result.modified_count > 0

    async def batch_create_topics(self, topics_data: List[Dict[str, Any]]) -> List[str]:
        """Create multiple topics in a single batch operation."""
        now = datetime.now()
        docs_to_insert = []

        for data in topics_data:
            if data.get("type") == "topic":
                topic = TopicModel(
                    name=data["name"],
                    type="topic",
                    created_at=now,
                    metadata=data.get("metadata")
                )
                docs_to_insert.append(topic.to_mongo())
            elif data.get("type") == "subtopic" and data.get("parent_id"):
                subtopic = SubtopicModel(
                    name=data["name"],
                    type="subtopic",
                    parent_id=ObjectId(data["parent_id"]),
                    created_at=now,
                    metadata=data.get("metadata")
                )
                docs_to_insert.append(subtopic.to_mongo())

        if docs_to_insert:
            result = await self.collection.insert_many(docs_to_insert)
            return [str(id) for id in result.inserted_ids]
        return []


    async def delete_many(self, query: Dict[str, Any]) -> int:
        """Delete multiple topics or subtopics based on a query."""
        result = await self.collection.delete_many(query)
        return result.deleted_count
