# Topic Management System

This module provides functionality for managing, generating, and analyzing topics from user conversations.

## Overview

The topic management system allows you to:

1. Generate topics and subtopics from user conversations
2. Store and retrieve topics in a structured format
3. Aggregate topic data across multiple chats for users
4. Analyze topic trends over time

## Structure

The topic management system is organized into the following components:

- **Models**: Define the data structures for topics, subtopics, and responses
- **Services**: Provide functionality for topic classification, batch processing, and aggregation
- **Routes**: Expose API endpoints for topic management and analysis

## Topic Storage

Topics are stored in the `ai_response` collection with the following structure:

```json
{
  "response": {
    "topic": {
      "topic_id": "<hash_id>",
      "topics": [
        {
          "topic": "<topic_name>",
          "sub_topics": [
            {"name": "<subtopic_name>"}
          ]
        }
      ]
    }
  }
}
```

The `topic_id` is a hash of the topics data, which helps identify unique topic structures across conversations.

## Aggregation Pipeline

The topic aggregation pipeline provides a way to analyze topics across multiple chats for users. It:

1. Groups by user_id to identify unique users
2. Processes topics and subtopics across all user conversations
3. Maintains proper topic hierarchy and relationships
4. Provides counts by date for time-based analysis

## API Endpoints

### Topic Management

- `GET /topic/generate_topics`: Generate topics for user conversations
  - Supports two modes:
    - `mode=batch` (default): Process all users' messages in batches
    - `mode=realtime`: Process a single user's messages in real-time
- `GET /topic/get_topic`: Get topic data
- `GET /topic/batch/{batch_id}/status`: Check the status of a batch processing job
- `GET /topic/batch/{batch_id}/results`: Get the results of a completed batch processing job

### Topic Analytics

- `GET /topic-analytics/by-date`: Get topic aggregation data grouped by date
- `GET /topic-analytics/user/{user_id}`: Get topic history for a specific user
- `GET /topic-analytics/similar-topics/{topic_id}`: Find users with similar topics

## Usage Examples

### Generate Topics

#### Batch Processing (Default)

```python
import requests

# Generate topics for all users in batch mode
response = requests.get(
    "http://localhost:8000/topic/generate_topics",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

batch_id = response.json()["batch_id"]

# Check batch status
status_response = requests.get(
    f"http://localhost:8000/topic/batch/{batch_id}/status",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Get batch results when completed
if status_response.json()["status"] == "completed":
    results = requests.get(
        f"http://localhost:8000/topic/batch/{batch_id}/results",
        headers={"Authorization": "Bearer YOUR_TOKEN"}
    )
```

#### Real-time Processing

```python
import requests

# Generate topics for a specific user in real-time
response = requests.get(
    "http://localhost:8000/topic/generate_topics",
    params={"mode": "realtime", "user_id": "user123"},
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

# Results are returned immediately
topics = response.json()["topics"]
summary = response.json()["summary"]
```

### Get Topic Analytics by Date

```python
import requests
from datetime import datetime, timedelta

# Get topics for the last 7 days
end_date = datetime.now().isoformat()
start_date = (datetime.now() - timedelta(days=7)).isoformat()

response = requests.get(
    "http://localhost:8000/topic-analytics/by-date",
    params={"start_date": start_date, "end_date": end_date},
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

topics_by_date = response.json()
```

### Get User Topic History

```python
import requests

response = requests.get(
    "http://localhost:8000/topic-analytics/user/user123",
    headers={"Authorization": "Bearer YOUR_TOKEN"}
)

user_topics = response.json()
```

## Implementation Details

The topic aggregation pipeline is implemented in `src/topic/services/aggregation.py` and provides reusable functions for topic analysis. The main functions are:

- `get_topic_aggregation_pipeline`: Creates a MongoDB aggregation pipeline for analyzing topics across user conversations
- `get_user_topic_history`: Creates a MongoDB aggregation pipeline for retrieving a user's topic history
- `get_topic_similarity_pipeline`: Creates a MongoDB aggregation pipeline for finding similar topics
