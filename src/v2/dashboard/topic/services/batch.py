"""
Services for batch processing of topic generation.
"""

import json
import os
import tempfile
from datetime import datetime
import pymongo
from bson import ObjectId
from openai import OpenAI
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.v2.dashboard.topic.models.response import TopicGeneratorResponse
from src.v2.dashboard.topic.utils.cost import calculate_cost
import hashlib

# Custom JSON encoder to handle datetime objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

loggers = setup_new_logging(__name__)

async def process_openai_batch(
    user_ids, doc_ids_map, current_user: UserTenantDB,
    prompt, topic_hierarchy, save_method=None, save_path=None,
    min_messages=1, messages_per_batch=5, save_results=True
):
    """
    Process a batch of user IDs in parallel to generate topic information.

    Args:
        user_ids: List of user IDs to process
        doc_ids_map: Dictionary mapping user IDs to document IDs
        current_user: Current user tenant database
        prompt: The prompt to use for topic generation
        topic_hierarchy: The topic hierarchy to use for topic generation
        save_method: Method to save detailed batch information ('file', 'db', or None)
        save_path: Path to save detailed batch information if save_method is 'file'
        min_messages: Minimum number of messages to include per user (default: 1)
        messages_per_batch: Number of messages per batch (default: 5)
        save_results: Whether to save results to database when batch is completed (default: True)

    Returns:
        Dictionary with batch information including batch_id
    """
    env_var = await current_user.async_db.settings.find_one({"name": "env"})
    api_config = env_var.get("config")
    api_key = api_config.get("OPENAI_API_KEY")
    client = OpenAI(api_key=api_key)

    tasks = []
    # Store mapping between custom_id and specific message IDs for each batch
    batch_message_map = {}

    # Process each user
    loggers.info(f"Processing {len(user_ids)} users with min_messages={min_messages}, messages_per_batch={messages_per_batch}")

    for user_id in user_ids:
        # Log the number of document IDs for this user
        user_doc_ids = doc_ids_map.get(user_id, [])
        loggers.info(f"User {user_id} has {len(user_doc_ids)} document IDs in doc_ids_map")

        # First, get the total number of batches for this user
        initial_data = await get_prompt_for_user(
            user_id,
            doc_ids_map,  # Pass the entire doc_ids_map
            current_user,
            prompt,
            topic_hierarchy,
            min_messages,
            messages_per_batch,
            0  # Get first batch to determine total batches
        )

        # Get batch information
        batch_info = initial_data.get("batch_info", {})
        total_batches = batch_info.get("total_batches", 0)
        total_conversations = batch_info.get("total_conversations", 0)

        # Process all users regardless of message count
        # Even users with just 1 message should be processed
        if total_conversations == 0:
            loggers.warning(f"Skipping user {user_id} with 0 messages")
            continue

        loggers.info(f"User {user_id} has {total_conversations} conversations, creating {total_batches} batches")

        # Create base messages array with system message and context (common for all batches)
        base_messages = [
            # System message with the prompt and topic hierarchy
            {"role": "system", "content": prompt + "\n\nPredefined Hierarchical Topic Structure:\n" + initial_data["topic_hierarchy"]}
        ]

        # Add previous summary if available
        if initial_data["summary"]:
            # Handle summary content - use custom encoder if it's not a string
            if isinstance(initial_data['summary'], str):
                summary_content = initial_data['summary']
            else:
                # Use the custom DateTimeEncoder to handle datetime objects
                summary_content = json.dumps(initial_data['summary'], indent=2, cls=DateTimeEncoder)
            base_messages.append({"role": "assistant", "content": f"Previous Summary: {summary_content}"})

        # Add previous topics if available
        if initial_data["topic"]:
            # Use the custom DateTimeEncoder to handle datetime objects
            topics_content = json.dumps(initial_data['topic'], indent=2, cls=DateTimeEncoder)
            base_messages.append({"role": "assistant", "content": f"Previously Identified Topics: {topics_content}"})

        # Process each batch for this user
        for batch_idx in range(total_batches):
            # Get data for this specific batch
            if batch_idx > 0:  # We already have batch 0
                data = await get_prompt_for_user(
                    user_id,
                    doc_ids_map,  # Pass the entire doc_ids_map
                    current_user,
                    prompt,
                    topic_hierarchy,
                    min_messages,
                    messages_per_batch,
                    batch_idx
                )
            else:
                data = initial_data

            # Get the specific message IDs for this batch and log them
            batch_message_ids = data.get("batch_message_ids", [])
            loggers.info(f"Batch {batch_idx+1} for user {user_id} has {len(batch_message_ids)} message IDs")

            # Parse the conversations JSON string back to a list
            try:
                conversations_list = json.loads(data["conversations"])
                loggers.debug(f"Successfully parsed conversations for user {data['user_id']} batch {batch_idx+1}: {len(conversations_list)} conversations")
            except json.JSONDecodeError:
                loggers.error(f"Error parsing conversations JSON for user {data['user_id']} batch {batch_idx+1}")
                continue

            # Start with the base messages
            messages = base_messages.copy()

            # Add a batch identifier if there are multiple batches
            if total_batches > 1:
                batch_identifier = f"Batch {batch_idx + 1} of {total_batches} for user {user_id}"
                messages.append({"role": "system", "content": batch_identifier})

            # Process conversation messages to ensure proper formatting
            for conversation in conversations_list:
                for msg in conversation:
                    if isinstance(msg, dict):
                        # Ensure role comes before content in the message
                        role = msg.get("role", "user")
                        content = msg.get("content", "")

                        # Skip empty content
                        if not content:
                            continue

                        # Normalize role names
                        if role not in ["user", "assistant", "system"]:
                            role = "assistant" if role in ["agent", "bot"] else "user"

                        # Create properly formatted message - explicitly create a new dict with role first
                        # This ensures the keys are in the correct order for the OpenAI API
                        formatted_message = {
                            "role": role,
                            "content": content
                        }
                        messages.append(formatted_message)

            # Log message structure for debugging
            loggers.debug(f"Message count for batch {batch_idx+1} of user {data['user_id']}: {len(messages)}")
            if len(messages) > 0:
                # Log the first message to verify structure
                first_msg = messages[0]
                loggers.debug(f"First message structure: role={first_msg.get('role')}, content preview={first_msg.get('content')[:50]}...")

                # Verify all messages have role before content in their structure
                for i, msg in enumerate(messages):
                    if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                        loggers.warning(f"Message {i} has invalid structure: {msg}")

            # Create a unique custom_id for each batch
            # For users with multiple batches, append the batch number to ensure uniqueness
            # Format: user_id_batchnum (e.g., "user123_1", "user123_2", etc.)
            user_id = data["user_id"]

            # Always include batch number in custom_id for consistency, even for single batches
            # This ensures each batch has a unique identifier
            custom_id = f"{user_id}_{batch_idx+1}"

            loggers.info(f"Created unique custom_id for batch: {custom_id} (user: {user_id}, batch: {batch_idx+1}/{total_batches})")

            # Store the mapping between custom_id and message IDs for this batch
            batch_message_map[custom_id] = data.get("batch_message_ids", [])

            # Skip if no messages were created
            if not messages:
                loggers.warning(f"No messages were created for user {data['user_id']} batch {batch_idx+1}")
                continue

            # Validate all messages have the correct structure
            valid_messages = []
            for msg in messages:
                if isinstance(msg, dict) and 'role' in msg and 'content' in msg:
                    # Create a new dict to ensure proper order of keys (role first, then content)
                    valid_messages.append({
                        "role": msg['role'],
                        "content": msg['content']
                    })
                else:
                    loggers.warning(f"Skipping message with invalid structure: {msg}")

            # Skip if no valid messages
            if not valid_messages:
                loggers.warning(f"No valid messages for user {data['user_id']} batch {batch_idx+1}")
                continue

            # Create the task with properly formatted messages
            task = {
                "custom_id": custom_id,
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": {
                    "model": "gpt-4.1-mini",
                    "messages": valid_messages,
                },
            }
            tasks.append(task)

    # Skip if no tasks were created
    if not tasks:
        loggers.warning("No tasks were created for batch processing")
        return {
            "batch_id": None,
            "status": "error",
            "error": "No tasks were created for batch processing",
            "user_count": len(user_ids),
            "task_count": 0
        }

    loggers.info(f"Creating batch with {len(tasks)} tasks")

    # Create a temporary file for the tasks with explicit .jsonl extension
    temp_file_path = None
    try:
        # Create a temporary directory that will be automatically cleaned up
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a file with explicit .jsonl extension in the temporary directory
            # Ensure the file has the correct extension for the OpenAI Batch API
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            temp_file_path = os.path.join(temp_dir, f"batch_tasks_{timestamp}.jsonl")

            # Write tasks to the temporary file
            with open(temp_file_path, 'w', encoding='utf-8') as temp_file:
                for task in tasks:
                    # Use the custom DateTimeEncoder to handle datetime objects
                    temp_file.write(json.dumps(task, cls=DateTimeEncoder) + '\n')

            # Log file info for debugging
            file_size = os.path.getsize(temp_file_path)
            loggers.info(f"Created temporary JSONL file at {temp_file_path} with size {file_size} bytes")

            # Verify file content
            with open(temp_file_path, 'r', encoding='utf-8') as verify_file:
                first_line = verify_file.readline().strip()
                line_count = 1
                for _ in verify_file:
                    line_count += 1
            loggers.info(f"JSONL file contains {line_count} lines")
            loggers.debug(f"First line sample: {first_line[:100]}...")

            # Upload the temporary file and create batch
            try:
                with open(temp_file_path, 'rb') as file:
                    # Log file details before upload
                    file_size = os.path.getsize(temp_file_path)
                    loggers.info(f"Uploading file with size {file_size} bytes and extension .jsonl")

                    # Upload the file
                    batch_input_file = client.files.create(file=file, purpose='batch')
                    loggers.info(f"Successfully uploaded file with ID: {batch_input_file.id}")

                    # Create the batch
                    batch = client.batches.create(
                        input_file_id=batch_input_file.id,
                        endpoint="/v1/chat/completions",
                        completion_window="24h",
                    )
                    loggers.info(f"Successfully created batch with ID: {batch.id}")
            except Exception as e:
                # Log the specific error
                loggers.error(f"Error during file upload or batch creation: {str(e)}")

                # Try to get more details about the file
                if os.path.exists(temp_file_path):
                    file_info = f"File exists, size: {os.path.getsize(temp_file_path)}, extension: {os.path.splitext(temp_file_path)[1]}"
                    loggers.error(f"File details: {file_info}")

                    # Read the first few lines to check format
                    try:
                        with open(temp_file_path, 'r', encoding='utf-8') as debug_file:
                            first_lines = [debug_file.readline() for _ in range(3)]
                            loggers.error(f"First few lines: {first_lines}")
                    except Exception as read_error:
                        loggers.error(f"Error reading file for debug: {read_error}")
                else:
                    loggers.error("File does not exist at the time of error")

                # Re-raise the exception to be caught by the outer try-except
                raise
    except Exception as e:
        loggers.error(f"Error creating batch: {str(e)}")
        return {
            "batch_id": None,
            "status": "error",
            "error": str(e),
            "user_count": len(user_ids),
            "task_count": len(tasks)
        }

    # Save batch information to database if requested
    if save_method == 'db':
        try:
            # Create a collection for batch information if it doesn't exist
            collection_name = "batch_info"
            if collection_name not in await current_user.async_db.list_collection_names():
                await current_user.async_db.create_collection(collection_name)

            # Create a mapping of custom_ids to original user_ids
            # This mapping is used to track which custom_id belongs to which user
            # Format: {custom_id: user_id} where custom_id is "user_id_batchnum"
            custom_id_map = {}
            for task in tasks:
                custom_id = task.get("custom_id", "")
                if custom_id:
                    # Extract the original user_id from the custom_id
                    # All custom_ids now follow the format "user_id_batchnum"
                    original_user_id = custom_id.split("_")[0]
                    custom_id_map[custom_id] = original_user_id
                    loggers.debug(f"Mapped custom_id {custom_id} to user_id {original_user_id}")

            # Get all custom IDs used in this batch
            custom_ids = [task.get("custom_id") for task in tasks]

            # Create a document with batch information
            # Use current_time to ensure consistency
            current_time = datetime.now()
            batch_document = {
                "batch_id": batch.id,
                "input_file_id": batch_input_file.id,
                "created_at": current_time,  # MongoDB can handle datetime objects
                "model": "gpt-4.1-mini",
                "status": "pending",
                "user_ids": user_ids,
                "custom_ids": custom_ids,
                "custom_id_map": custom_id_map,
                "doc_ids_map": doc_ids_map,
                "batch_message_map": batch_message_map,
                "message_limits": {
                    "min_messages": min_messages,
                    "messages_per_batch": messages_per_batch
                }
            }

            # Insert batch document into the database
            await current_user.async_db[collection_name].insert_one(batch_document)
            loggers.debug(f"Batch information saved to database collection '{collection_name}'")
        except Exception as e:
            loggers.error(f"Error saving batch information to database: {e}")

    # Return batch information immediately
    # Handle created_at which could be a datetime, int timestamp, or missing
    created_at_iso = None
    if hasattr(batch, 'created_at') and batch.created_at is not None:
        if isinstance(batch.created_at, datetime):
            # If it's already a datetime object, convert to ISO format
            created_at_iso = batch.created_at.isoformat()
        elif isinstance(batch.created_at, (int, float)):
            # If it's a timestamp (int or float), convert to datetime first
            try:
                created_at_dt = datetime.fromtimestamp(batch.created_at)
                created_at_iso = created_at_dt.isoformat()
            except (ValueError, OverflowError, OSError) as e:
                # If conversion fails, log the error and use current time
                loggers.error(f"Error converting timestamp to datetime: {e}")
                created_at_iso = datetime.now().isoformat()
        else:
            # If it's some other type, convert to string
            created_at_iso = str(batch.created_at)

    # If we couldn't get a valid created_at, use current time
    if not created_at_iso:
        created_at_iso = datetime.now().isoformat()

    return {
        "batch_id": batch.id,
        "input_file_id": batch_input_file.id,
        "created_at": created_at_iso,  # Use ISO format string
        "status": "pending",
        "user_count": len(user_ids),
        "task_count": len(tasks),
        "message_limits": {
            "min_messages": min_messages,
            "messages_per_batch": messages_per_batch
        }
    }

async def check_batch_status(batch_id: str, current_user: UserTenantDB, save_results=True):
    """
    Check the status of a batch.

    Args:
        batch_id: The ID of the batch to check
        current_user: Current user tenant database
        save_results: Whether to save results to database when batch is completed (default: True)

    Returns:
        Dictionary with batch status information
    """
    # Get API key from settings
    env_var = await current_user.async_db.settings.find_one({"name": "env"})
    api_config = env_var.get("config")
    api_key = api_config.get("OPENAI_API_KEY")
    client = OpenAI(api_key=api_key)

    try:
        # Retrieve batch status from OpenAI
        status = client.batches.retrieve(batch_id)

        # Update status in database if it exists
        collection_name = "batch_info"
        if collection_name in await current_user.async_db.list_collection_names():
            await current_user.async_db[collection_name].update_one(
                {"batch_id": batch_id},
                {"$set": {"status": status.status}}
            )

        # Return status information
        # Handle datetime fields which could be datetime objects, int timestamps, or missing

        # Handle created_at
        created_at_iso = None
        if hasattr(status, 'created_at') and status.created_at is not None:
            if isinstance(status.created_at, datetime):
                created_at_iso = status.created_at.isoformat()
            elif isinstance(status.created_at, (int, float)):
                try:
                    created_at_dt = datetime.fromtimestamp(status.created_at)
                    created_at_iso = created_at_dt.isoformat()
                except (ValueError, OverflowError, OSError) as e:
                    loggers.error(f"Error converting created_at timestamp to datetime: {e}")
                    created_at_iso = str(status.created_at)
            else:
                created_at_iso = str(status.created_at)

        # Handle expires_at
        expires_at_iso = None
        if hasattr(status, 'expires_at') and status.expires_at is not None:
            if isinstance(status.expires_at, datetime):
                expires_at_iso = status.expires_at.isoformat()
            elif isinstance(status.expires_at, (int, float)):
                try:
                    expires_at_dt = datetime.fromtimestamp(status.expires_at)
                    expires_at_iso = expires_at_dt.isoformat()
                except (ValueError, OverflowError, OSError) as e:
                    loggers.error(f"Error converting expires_at timestamp to datetime: {e}")
                    expires_at_iso = str(status.expires_at)
            else:
                expires_at_iso = str(status.expires_at)

        # Handle completed_at
        completed_at_iso = getattr(status, 'completed_at', None)
        if completed_at_iso is not None:
            if isinstance(completed_at_iso, datetime):
                completed_at_iso = completed_at_iso.isoformat()
            elif isinstance(completed_at_iso, (int, float)):
                try:
                    completed_at_dt = datetime.fromtimestamp(completed_at_iso)
                    completed_at_iso = completed_at_dt.isoformat()
                except (ValueError, OverflowError, OSError) as e:
                    loggers.error(f"Error converting completed_at timestamp to datetime: {e}")
                    completed_at_iso = str(completed_at_iso)
            else:
                completed_at_iso = str(completed_at_iso)

        status_info = {
            "batch_id": batch_id,
            "status": status.status,
            "created_at": created_at_iso,
            "expires_at": expires_at_iso,
            "completed_at": completed_at_iso,
            "has_output": hasattr(status, 'output_file_id') and status.output_file_id is not None
        }

        # Add output file ID if available
        if hasattr(status, 'output_file_id') and status.output_file_id:
            status_info["output_file_id"] = status.output_file_id

        # If batch is completed and has output, save the results
        if save_results and status.status == "completed" and hasattr(status, 'output_file_id') and status.output_file_id:
            # Get batch info from database
            batch_info = await current_user.async_db[collection_name].find_one({"batch_id": batch_id})

            if batch_info and not batch_info.get("output_file_id"):
                # Process and save the results
                output = client.files.content(status.output_file_id)

                # Process the output content
                response = TopicGeneratorResponse(output.content)

                # Get the custom_id mapping if it exists
                custom_id_map = batch_info.get("custom_id_map", {})

                # Process the output with custom_id mapping
                topics_data = response.get_topics_and_summaries(custom_id_map)

                # Add batch metadata
                response.add_batch_metadata(
                    batch_id=batch_id,
                    input_file_id=batch_info.get("input_file_id"),
                    output_file_id=status.output_file_id,
                    batch_created_at=batch_info.get("created_at"),
                    batch_status=status.status
                )

                # Get detailed info with batch cost included
                detailed_info = response.get_detailed_info(include_batch_cost=True)
                detailed_batch_info = detailed_info["detailed_info"]
                batch_cost = detailed_info["batch_cost"]

                # Update batch info in database
                # Use current_time for consistency
                current_time = datetime.now()
                await current_user.async_db[collection_name].update_one(
                    {"batch_id": batch_id},
                    {"$set": {
                        "status": "completed",
                        "output_file_id": status.output_file_id,
                        "completed_at": current_time,  # MongoDB can handle datetime objects directly
                        "detailed_info": detailed_batch_info,
                        "batch_cost": batch_cost
                    }}
                )

                # Save the topics data to the database
                doc_ids_map = batch_info.get("doc_ids_map", {})
                # Pass the batch_id to save_batch_user_data
                await save_batch_user_data(topics_data, doc_ids_map, current_user, batch_id)

                # Add information about saved data to status_info
                status_info["data_saved"] = True

        return status_info
    except Exception as e:
        loggers.error(f"Error checking batch status: {e}")
        return {"batch_id": batch_id, "status": "error", "error": str(e)}

async def get_batch_results(batch_id: str, current_user: UserTenantDB):
    """
    Get the results of a completed batch.

    Args:
        batch_id: The ID of the batch to get results for
        current_user: Current user tenant database

    Returns:
        Dictionary with batch results or error information
    """
    # First check batch status (with save_results=False to avoid duplicate saving)
    status_info = await check_batch_status(batch_id, current_user, save_results=False)

    # If batch is not completed, return status
    if status_info.get("status") != "completed":
        return {"batch_id": batch_id, "status": status_info.get("status"), "message": "Batch not completed yet"}

    # Get API key from settings
    env_var = await current_user.async_db.settings.find_one({"name": "env"})
    api_config = env_var.get("config")
    api_key = api_config.get("OPENAI_API_KEY")
    client = OpenAI(api_key=api_key)

    try:
        # Get batch info from database
        collection_name = "batch_info"
        batch_info = await current_user.async_db[collection_name].find_one({"batch_id": batch_id})

        if not batch_info:
            return {"batch_id": batch_id, "status": "error", "error": "Batch information not found in database"}

        # Get output file content
        output_file_id = status_info.get("output_file_id")
        if not output_file_id:
            return {"batch_id": batch_id, "status": "error", "error": "No output file ID found"}

        output = client.files.content(output_file_id)

        # Process the output content
        response = TopicGeneratorResponse(output.content)

        # Get the custom_id mapping if it exists
        custom_id_map = batch_info.get("custom_id_map", {})

        # Process the output with custom_id mapping
        topics_data = response.get_topics_and_summaries(custom_id_map)

        # Get batch cost information if available
        batch_cost = batch_info.get("batch_cost", {})
        if not batch_cost and "detailed_info" in batch_info:
            # Calculate batch cost if not already available
            detailed_info = response.get_detailed_info(include_batch_cost=True)
            batch_cost = detailed_info.get("batch_cost", {})

        # Return the topics data and batch information
        return {
            "batch_id": batch_id,
            "status": "completed",
            "topics_data": topics_data,
            "batch_cost": batch_cost.get("batch_summary", {})
        }
    except Exception as e:
        loggers.error(f"Error getting batch results: {e}")
        return {"batch_id": batch_id, "status": "error", "error": str(e)}

async def save_batch_user_data(topics_data, doc_ids_map, current_user: UserTenantDB, batch_id=None):
    """
    Save the topic and summary data for each user to the database.

    Args:
        topics_data: List of dictionaries with user_id, topics, and summary
        doc_ids_map: Dictionary mapping user_id to doc_ids
        current_user: Current user tenant database
        batch_id: Optional batch ID to use for retrieving batch information
    """
    # Get the batch info to access the batch_message_map
    collection_name = "batch_info"
    batch_info = None
    batch_message_map = {}

    # Try to get the batch info
    if collection_name in await current_user.async_db.list_collection_names():
        if batch_id:
            # If batch_id is provided, get that specific batch
            batch_info = await current_user.async_db[collection_name].find_one(
                {"batch_id": batch_id}
            )
        else:
            # Otherwise get the most recent completed batch
            batch_info = await current_user.async_db[collection_name].find_one(
                {"status": "completed"},
                sort=[("created_at", -1)]
            )
        if batch_info:
            batch_message_map = batch_info.get("batch_message_map", {})

    # Convert the list of dictionaries to a dictionary keyed by custom_id (which may include batch info)
    # This ensures each batch result is applied only to its specific messages
    data_by_batch = {}
    for item in topics_data:
        custom_id = item.get('user_id')  # This is actually the custom_id which may be user_id_batchnum
        topics = item.get('topics', [])
        summary = item.get('summary', '')

        if not custom_id:
            continue

        # Store the data for this specific batch
        data_by_batch[custom_id] = {
            'topics': topics,
            'summary': summary
        }

    # Now process all updates in a batch operation
    bulk_operations = []

    for custom_id, batch_data in data_by_batch.items():
        # Get the specific message IDs for this batch
        batch_specific_doc_ids = batch_message_map.get(custom_id, [])

        # If we don't have specific message IDs for this batch, fall back to the user's doc_ids
        if not batch_specific_doc_ids:
            # Extract user_id from custom_id (all custom_ids now follow the format "user_id_batchnum")
            user_id = custom_id.split('_')[0]

            # Get all document IDs for this user
            all_user_doc_ids = doc_ids_map.get(user_id, [])

            # Extract batch number from custom_id
            batch_num = int(custom_id.split('_')[1]) if '_' in custom_id else 1

            # Calculate which documents belong to this batch based on the batch number
            # For example, if we have 20 messages and 4 batches of 5 messages each:
            # Batch 1 gets messages 0-4, Batch 2 gets messages 5-9, etc.

            # Get messages_per_batch from batch_info if available, otherwise use default value of 5
            messages_per_batch = 5  # Default value
            if batch_info and "message_limits" in batch_info:
                messages_per_batch = batch_info.get("message_limits", {}).get("messages_per_batch", 5)
            start_idx = (batch_num - 1) * messages_per_batch
            end_idx = min(start_idx + messages_per_batch, len(all_user_doc_ids))

            # Get the specific document IDs for this batch
            batch_specific_doc_ids = all_user_doc_ids[start_idx:end_idx]

            loggers.info(f"Calculated batch-specific doc_ids for {custom_id}: {len(batch_specific_doc_ids)} documents (batch {batch_num}, indices {start_idx}-{end_idx})")

        loggers.debug(f'    Batch {custom_id} doc_ids: {batch_specific_doc_ids}')
        if not batch_specific_doc_ids:
            continue

        # Create update operations for the specific message IDs for this batch
        for doc_id in batch_specific_doc_ids:
            # Convert string ID to ObjectId if needed
            if isinstance(doc_id, str):
                try:
                    doc_id = ObjectId(doc_id)
                except Exception as e:
                    loggers.error(f"Error converting doc_id to ObjectId: {e}")
                    continue

            # Create update operation with both topics and summary
            # Generate a unique hash_id for each batch by including the custom_id, batch_id, and topics in the hash
            # This ensures each batch gets a different topic_id even if the topics are the same
            # Extract the batch number from custom_id if it exists (format: user_id_batchnum)
            batch_num = custom_id.split('_')[1] if '_' in custom_id else "1"

            # Include batch_id in the hash if available
            batch_identifier = batch_id if batch_id else f"batch_{batch_num}"

            # Create a unique hash that includes user ID, batch identifier, and topic content
            hash_input = f"{custom_id}_{batch_identifier}_{str(batch_data['topics'])}"
            hash_id = hashlib.sha256(hash_input.encode('utf-8')).hexdigest()
            update_data = {}
            if batch_data.get('topics'):
                # Extract user_id from custom_id (remove batch number if present)
                user_id = custom_id.split('_')[0] if '_' in custom_id else custom_id

                # Extract batch number from custom_id if it exists
                batch_num = custom_id.split('_')[1] if '_' in custom_id else "1"

                # Create a more detailed topic structure with metadata
                # Convert datetime to ISO format string to avoid JSON serialization issues
                current_time = datetime.now()
                update_data["response.topic"] = {
                    "topic_id": hash_id,
                    "user_id": user_id,
                    "batch_num": batch_num,
                    "batch_id": batch_id if batch_id else None,
                    "created_at": current_time.isoformat(),  # Store as ISO format string
                    "topics": batch_data['topics']}
            if batch_data.get('summary'):
                update_data["response.summary"] = batch_data['summary']

            bulk_operations.append(
                pymongo.UpdateOne(
                    {"_id": doc_id},
                    {"$set": update_data}
                )
            )

    # Execute the bulk operation if there are any updates
    if bulk_operations:
        try:
            result = await current_user.async_db.ai_response.bulk_write(bulk_operations)
            loggers.info(f"Batch update completed: {result.modified_count} documents modified")
        except Exception as e:
            loggers.error(f"Error executing bulk write operation: {e}")

async def get_serialized_data(user_id, doc_ids_map, current_user: UserTenantDB, min_messages=1, messages_per_batch=5, batch_index=0):
    """
    Serialize all topics, subtopics, and conversations into a dictionary format.

    Args:
        user_id: User ID to get data for
        doc_ids_map: List of document IDs to include
        current_user: Current user tenant database
        min_messages: Minimum number of messages to include (default: 1)
        messages_per_batch: Number of messages per batch (default: 5)
        batch_index: Index of the batch to retrieve (default: 0, first batch)

    Returns:
        Tuple of (conversations, latest_topic, latest_summary)
    """
    async_db = current_user.async_db
    collection = async_db["ai_response"]

    # Step 1: Find the latest document with a topic
    latest_with_topic = await collection.find_one(
        {"request.user_id": user_id, "response.topic": {"$exists": True}},
        sort=[("created_at", -1)],
        limit=1,  # Sort by created_at descending (latest first)
    )

    # Extract the topic from the latest document (if it exists)
    latest_topic = (
        latest_with_topic.get("response", {}).get("topic")
        if latest_with_topic
        else None
    )
    latest_summary = (
        latest_with_topic.get("response", {}).get("summary")
        if latest_with_topic
        else None
    )

    # Step 2: Fetch all documents for the user
    # doc_ids_map is a dictionary mapping user_id to a list of document IDs
    # We need to extract the list of document IDs for this specific user
    user_doc_ids = doc_ids_map.get(user_id, [])

    loggers.info(f"User {user_id} has {len(user_doc_ids)} document IDs in doc_ids_map")

    all_docs = (
        await collection.find({"request.user_id": user_id, "_id": {"$in": user_doc_ids}})
        .sort("created_at", -1)
        .to_list(None)
    )

    # Log the number of documents found for debugging
    loggers.info(f"Found {len(all_docs)} documents for user {user_id}")

    # Step 3: Process all documents to create conversations
    conversations = []
    for doc in all_docs:
        chat_data = doc.get("response", {}).get("chat_data", [])
        parsed_chats = [
            {
                "role": chat.get("role"),
                "content": chat.get("content"),
            }
            for chat in chat_data
        ]
        conversations.append(parsed_chats)

    # Process all conversations regardless of count
    # We want to process even users with just 1 or 2 messages
    if batch_index == 0:
        loggers.info(f"User {user_id} has {len(conversations)} messages")
        # Don't return early, process all messages

    # Calculate total number of batches
    total_conversations = len(conversations)
    num_batches = (total_conversations + messages_per_batch - 1) // messages_per_batch

    # Ensure batch_index is valid
    if batch_index >= num_batches:
        loggers.warning(f"Batch index {batch_index} is out of range for user {user_id} with {num_batches} batches")
        batch_index = 0

    # Get the document IDs for this specific batch
    batch_doc_ids = []
    if all_docs:
        start_idx = batch_index * messages_per_batch
        end_idx = min(start_idx + messages_per_batch, len(all_docs))
        batch_docs = all_docs[start_idx:end_idx]
        batch_doc_ids = [str(doc.get('_id')) for doc in batch_docs]

    # Get the specific batch of conversations
    start_idx = batch_index * messages_per_batch
    end_idx = min((batch_index + 1) * messages_per_batch, total_conversations)
    batch_conversations = conversations[start_idx:end_idx]

    loggers.info(f"User {user_id}: Returning batch {batch_index+1}/{num_batches} with {len(batch_conversations)} conversations")

    return batch_conversations, latest_topic, latest_summary, batch_doc_ids

async def get_prompt_for_user(user_id, doc_ids_map, current_user, prompt, topic_hierarchy, min_messages=1, messages_per_batch=5, batch_index=0):
    """
    Get the prompt and related data for a user.

    Args:
        user_id: User ID to get data for
        doc_ids_map: List of document IDs to include
        current_user: Current user tenant database
        prompt: The prompt to use for topic generation
        topic_hierarchy: The topic hierarchy to use for topic generation
        min_messages: Minimum number of messages to include (default: 1)
        messages_per_batch: Number of messages per batch (default: 5)
        batch_index: Index of the batch to retrieve (default: 0, first batch)

    Returns:
        Dictionary with user data for topic generation and number of batches
    """
    conversations, latest_topic, latest_summary, batch_message_ids = await get_serialized_data(
        user_id, doc_ids_map, current_user, min_messages, messages_per_batch, batch_index
    )

    # Calculate total number of batches
    all_conversations, _, _, _ = await get_serialized_data(
        user_id, doc_ids_map, current_user, min_messages, messages_per_batch, 0
    )
    total_conversations = len(all_conversations)
    num_batches = (total_conversations + messages_per_batch - 1) // messages_per_batch

    conversations_json = json.dumps(conversations, indent=2)

    return {
        "user_id": user_id,
        "prompt": prompt,
        "summary": latest_summary,
        "topic": latest_topic,
        "conversations": conversations_json,
        "topic_hierarchy": topic_hierarchy,
        "batch_message_ids": batch_message_ids,
        "batch_info": {
            "batch_index": batch_index,
            "total_batches": num_batches,
            "total_conversations": total_conversations,
            "conversations_in_batch": len(conversations)
        }
    }
