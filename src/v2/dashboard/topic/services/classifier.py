"""
Topic classifier service for managing conversations, topics, and subtopics.
"""

import asyncio
import json
from typing import Dict, TypeVar, Generic, List, Optional
from openai import OpenAI
from pydantic import BaseModel
from bson import ObjectId

from src.models.user import UserTenantDB
from src.v2.dashboard.topic.model import TopicData, TopicWrapper
from src.v2.dashboard.topic.services.batch import process_openai_batch, save_batch_user_data
from src.helper.logger import setup_new_logging
from pymongo.collection import Collection
from llama_index.core.llms import ChatMessage
from llama_index.core import Response
from llama_index.llms.openai import OpenAI
from openai import AsyncOpenAI
import hashlib

loggers = setup_new_logging(__name__)

# Generic type for classification
T = TypeVar("T")

class TopicClassifierWrapper(BaseModel, Generic[T]):
    """
    Main class for managing conversations, topics, and subtopics.
    """

    current_user: UserTenantDB = None
    topics: Dict[str, TopicWrapper[T]] = {}
    prompt: str = None
    llm: OpenAI = None

    class Config:
        arbitrary_types_allowed = True

    async def initialize(self, current_user: UserTenantDB,llm:Optional[OpenAI]=None):
        """
        Asynchronous initialization logic.
        """
        self.current_user = current_user
        env_var = await self.current_user.async_db.settings.find_one({"name": "env"})
        api_config = env_var.get("config")
        if llm:
            self.llm = llm
        else:
            # self.llm = OpenAI(api_key=api_config.get("OPENAI_API_KEY"))   
            self.llm = AsyncOpenAI(api_key=api_config.get("OPENAI_API_KEY"))

        # Find topic and store at topics
        topic_data = self.get_topics_data()
        self.topics = TopicData(**topic_data)

        # Find prompt and store at prompt
        prompt_data = await self.current_user.async_db["prompt"].find_one(
            {"name": "topic_identify"}
        )
        self.prompt = prompt_data["text"]

    def get_topics_data(self) -> dict:
        """
        Fetches topic data from the database and returns it in a structured format.
        """
        topic_coll: Collection = self.current_user.db.topics

        # Fetch all documents
        all_docs = list(topic_coll.find({}))

        # Separate topics and subtopics
        topics = [doc for doc in all_docs if doc.get("type") == "topic"]
        subtopics = [doc for doc in all_docs if doc.get("type") == "subtopic"]

        # Group subtopics by parent_id
        subtopics_by_parent = {}
        for sub in subtopics:
            parent_id = sub.get("parent_id")
            if parent_id not in subtopics_by_parent:
                subtopics_by_parent[parent_id] = []
            subtopics_by_parent[parent_id].append({
                "name": sub["name"]
            })

        # Build the final structure
        data: List[dict] = []
        for topic in topics:
            topic_entry = {
                "topic": topic["name"],
                "sub_topics": subtopics_by_parent.get(topic["_id"], [])
            }
            data.append(topic_entry)

        return {
            "name": "Topics",
            "data": data
        }


    async def get_serialized_data(self, user_id, doc_ids_map: List[ObjectId]) -> Dict:
        """
        Serialize all topics, subtopics, and conversations into a dictionary format.
        """
        async_db = self.current_user.async_db
        collection = async_db["ai_response"]

        # Step 1: Find the latest document with a topic
        latest_with_topic = await collection.find_one(
            {"request.user_id": user_id, "response.topic": {"$exists": True}},
            sort=[("created_at", -1)],
            limit=1,  # Sort by created_at descending (latest first)
        )

        # Extract the topic from the latest document (if it exists)
        latest_topic = (
            latest_with_topic.get("response", {}).get("topic")
            if latest_with_topic
            else None
        )
        latest_summary = (
            latest_with_topic.get("response", {}).get("summary")
            if latest_with_topic
            else None
        )

        # Step 2: Fetch all documents for the user
        all_docs = (
            await collection.find({"request.user_id": user_id, "_id": {"$in": doc_ids_map}})
            .sort("created_at", -1)
            .to_list(None)
        )

        # Step 3: Process all documents to create conversations
        conversations = []
        for doc in all_docs:
            chat_data = doc.get("response", {}).get("chat_data", [])
            parsed_chats = [
                {
                    "role": chat.get("role"),
                    "content": chat.get("content"),
                }
                for chat in chat_data
            ]
            conversations.append(parsed_chats)

        return conversations, latest_topic, latest_summary

    async def get_prompt_for_user(self, user_id: str, doc_ids_map: List[ObjectId]) -> Dict[str, str]:
        conversations, latest_topic, latest_summary = await self.get_serialized_data(
            user_id, doc_ids_map
        )

        general_topic_json = json.dumps(
            [topic.model_dump() for topic in self.topics.data], indent=2
        )
        conversations_json = json.dumps(conversations, indent=2)

        return {
            "user_id": user_id,
            "prompt": self.prompt,
            "summary": latest_summary,
            "topic": latest_topic,
            "conversations": conversations_json,
            "topic_hierarchy": general_topic_json
        }

    async def process_openai_batch(
        self, user_ids: List[str], doc_ids_map, current_user: UserTenantDB, save_method: str = None, save_path: str = None,
        min_messages: int = 3, messages_per_batch: int = 5, save_results: bool = True
    ) -> Dict:
        """
        Process a batch of user IDs in parallel to generate topic information.

        Args:
            user_ids: List of user IDs to process
            doc_ids_map: Dictionary mapping user IDs to document IDs
            current_user: Current user tenant database
            save_method: Method to save detailed batch information ('file', 'db', or None)
            save_path: Path to save detailed batch information if save_method is 'file'
            min_messages: Minimum number of messages to include per user (default: 3)
            messages_per_batch: Number of messages per batch (default: 5)
            save_results: Whether to save results to database when batch is completed (default: True)

        Returns:
            Dictionary with batch information including batch_id
        """
        # Get the topic hierarchy
        general_topic_json = json.dumps(
            [topic.model_dump() for topic in self.topics.data], indent=2
        )

        # Process the batch with message limits
        return await process_openai_batch(
            user_ids=user_ids,
            doc_ids_map=doc_ids_map,
            current_user=current_user,
            prompt=self.prompt,
            topic_hierarchy=general_topic_json,
            save_method=save_method,
            save_path=save_path,
            min_messages=min_messages,
            messages_per_batch=messages_per_batch,
            save_results=save_results
        )

    async def save_batch_user_data(self, topics_data, doc_ids_map, current_user: UserTenantDB, batch_id=None):
        """
        Save the topic and summary data for each user to the database.

        Args:
            topics_data: List of dictionaries with user_id, topics, and summary
            doc_ids_map: Dictionary mapping user_id to doc_ids
            current_user: Current user tenant database
            batch_id: Optional batch ID to use for retrieving batch information
        """
        await save_batch_user_data(topics_data, doc_ids_map, current_user, batch_id)


            
    async def create_batch(self, data, min_message, max_message, current_user: UserTenantDB):
        result = []  # Store the final result with batches
        topic_data = self.get_topics_data()
        prompt = f"{self.prompt}\n\nPredefined Hierarchical Topic Structure:\n{topic_data}"

        class sub_topic(BaseModel):
            name: str
            
        class topic_cla(BaseModel):
            topic: str
            sub_topics: List[sub_topic]
            
        class response_cla(BaseModel):
            topic: List[topic_cla]
            summary: str

        async def process_batch(user_id: str, batch: List[dict], prompt_chat: List[dict]):
            try:
                # Convert any ObjectId to string in the batch
                batch_cleaned = []
                for msg in batch:
                    msg_copy = msg.copy()
                    if "doc_id" in msg_copy and isinstance(msg_copy["doc_id"], ObjectId):
                        msg_copy["doc_id"] = str(msg_copy["doc_id"])
                    batch_cleaned.append(msg_copy)

                response = await self.llm.beta.chat.completions.parse(
                    model="gpt-4.1-mini",
                    messages=prompt_chat + batch_cleaned,
                    response_format=response_cla
                )

                output = response.choices[0].message.parsed
                topic_output = {
                    "topic_id": hashlib.sha256(str(output.topic).encode('utf-8')).hexdigest(),
                    "topics": [o.model_dump() for o in output.topic]
                }

                update_data = {
                    "response.topic": topic_output,
                    "response.summary": output.summary
                }

                # Get the document IDs from the original data structure and convert back to ObjectId
                doc_ids = [ObjectId(msg["doc_id"]) for msg in batch_cleaned if msg.get("doc_id")]

                # Update database
                if doc_ids:
                    await current_user.async_db.ai_response.update_many(
                        {"_id": {"$in": doc_ids}, "request.user_id": user_id},
                        {"$set": update_data}
                    )

                return {
                    "success": True,
                    "data": {
                        "user_id": user_id,
                        "batch": batch_cleaned,
                        "batch_ids": [str(doc_id) for doc_id in doc_ids],
                        "topic_output": topic_output,
                        "summary": output.summary
                    },
                    "message": "Successfully processed and updated topics"
                }
            except Exception as e:
                return {
                    "success": False,
                    "data": None,
                    "message": f"Error processing batch: {str(e)}"
                }

        # Create tasks for all batches across all users
        tasks = []
        prompt_chat = [{"role": "system", "content": prompt}]
        
        for item in data:
            chats = item["data"]
            user_id = item["user_id"]
            
            # Create tasks for each batch of messages
            for i in range(0, len(chats), max_message):
                batch = chats[i:i + max_message]
                tasks.append(process_batch(user_id, batch, prompt_chat))

        # Execute all tasks concurrently
        batch_results = await asyncio.gather(*tasks)
        
        # Aggregate results
        successful_batches = [r for r in batch_results if r["success"]]
        failed_batches = [r for r in batch_results if not r["success"]]
        
        return {
            "success": len(successful_batches) > 0,
            "data": {
                "processed_batches": successful_batches,
                "failed_batches": failed_batches,
                "total_processed": len(successful_batches),
                "total_failed": len(failed_batches)
            },
            "message": "Batch processing completed"
        }


 
