from fastapi import APIRouter, Depends, HTTPException, Path, Body
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.v2.dashboard.topic.services.classifier import TopicClassifierWrapper
from src.v2.dashboard.topic.services.batch import check_batch_status, get_batch_results
from src.v2.dashboard.topic.manage import TopicService, TopicModel, SubtopicModel, TopicResponse
from datetime import datetime
from typing import Dict, Any,Literal
from src.helper.resolve_llm import resolve_llm

topic_router = APIRouter(prefix="/topic", tags=["Topic"])


@topic_router.get("/generate_topics")
async def generate_topics(
    current_user: UserTenantDB = Depends(get_tenant_info),
    mode: Literal["batch", "realtime"] = "batch",
    min_messages: int = 1,  # Minimum number of messages per user (set to 1 to process all users)
    messages_per_batch: int = 3,  # Number of messages per batch (increased to handle more messages)
):
    # Initialize the TopicClassifierWrapper
    topic_classifier = TopicClassifierWrapper()
    await topic_classifier.initialize(current_user)

    pipeline = [
        # Filter all messages without topics (not just today's)
        {
            "$match": {
                "created_at": {
                    "$gte": datetime.now().replace(hour=0, minute=0, second=0, microsecond=0),  # Start of the day
                    "$lt": datetime.now().replace(hour=23, minute=59, second=59, microsecond=999999)  # End of the day
                },
                "response.topic": {"$exists": False},  # Only include messages without topics
                "request.channel": {"$ne": "Playground"},  # Exclude messages from 'Playground' channel
            }
        },

        # Unwind the 'chat_data' array to flatten it
        {
            "$unwind": "$response.chat_data"  # Flatten the chat_data array
        },

        # Group by user_id and collect chat_data, doc_ids, and doc_count
        {
            "$group": {
                "_id": "$request.user_id",  # Group by user_id
                
                "data": {
                    "$push": {
                        "role": "$response.chat_data.role",  # Extract role
                        "content": "$response.chat_data.content",  # Extract content
                        # "created_at": "$response.chat_data.created_at",  # Extract created_at
                        "doc_id": "$_id"  # Collect document IDs
                    }
                },
                # "doc_ids": {
                #     "$push": "$_id"  # Collect all document IDs for the user
                # },
                "doc_count": {"$sum": 1}  # Count messages per user
            }
        },

        # Sort by message count (most active users first)
        {"$sort": {"doc_count": -1}},

        # Project the user_id and the collected data
        {
            "$project": {
                "user_id": "$_id",  # Project the user_id field
                "doc_ids": 1,  # Include the 'doc_ids' array with document IDs
                "data": 1,  # Include the 'data' array with role, content, and created_at
                "doc_count": 1,  # Include the message count if needed
                "_id": 0  # Exclude the default _id field from the result
            }
        }
    ]
    # print(pipeline)
    # Execute the pipeline and get the result directly
    result = await (await current_user.async_db.ai_response.aggregate(pipeline)).to_list(length=None)
    if not result:
        return {"message": "No messages to process"}
    if mode == "batch":
        # Process the result in batches
        ...
    elif mode == "realtime":
        # Process the result in real-time
        from pprint import pprint
        # pprint(result[0])
        
        batch_data=await topic_classifier.create_batch(result, min_messages, messages_per_batch,current_user)
        return batch_data
        ...
    else:
        raise HTTPException(status_code=400, detail="Invalid mode specified")


@topic_router.get("/get_topic")
async def get_topic(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Get all topics in the format:
    [
        {
            "topic": "Topic Name",
            "sub_topics": [
                {"name": "Subtopic 1"},
                {"name": "Subtopic 2"}
            ]
        },
        ...
    ]
    """
    try:
        topic_service = TopicService(current_user.async_db)
        await topic_service.initialize()
        topics = await topic_service.get_all()

        # Format topics using the model_dump_filter method
        formatted_topics = [topic.model_dump_filter() for topic in topics]

        return formatted_topics
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving topics: {str(e)}"
        )


@topic_router.get("/batch/{batch_id}/status")
async def get_batch_status(
    batch_id: str = Path(..., description="The ID of the batch to check"),
    current_user: UserTenantDB = Depends(get_tenant_info),
) -> Dict[str, Any]:
    """
    Check the status of a batch processing job.
    If the batch is completed, this will also save the results to the database.

    Args:
        batch_id: The ID of the batch to check
        current_user: Current user tenant database

    Returns:
        Dictionary with batch status information
    """
    status_info = await check_batch_status(batch_id, current_user, save_results=True)
    if status_info.get("status") == "error":
        raise HTTPException(
            status_code=404,
            detail=f"Error checking batch status: {status_info.get('error')}",
        )
    return status_info


@topic_router.get("/batch/{batch_id}/results")
async def retrieve_batch_results(
    batch_id: str = Path(..., description="The ID of the batch to get results for"),
    current_user: UserTenantDB = Depends(get_tenant_info),
) -> Dict[str, Any]:
    """
    Get the results of a completed batch processing job.

    Args:
        batch_id: The ID of the batch to get results for
        current_user: Current user tenant database

    Returns:
        Dictionary with batch results or error information
    """
    results = await get_batch_results(batch_id, current_user)
    if results.get("status") == "error":
        raise HTTPException(
            status_code=404,
            detail=f"Error retrieving batch results: {results.get('error')}",
        )
    return results







