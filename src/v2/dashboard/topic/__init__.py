"""Topic classification and management package."""
from fastapi import APIRouter
topic_routers = APIRouter()

from src.v2.dashboard.topic.services.classifier import TopicClassifierWrapper
from src.v2.dashboard.topic.models.response import TopicGeneratorResponse
from src.v2.dashboard.topic.manage import TopicService, TopicModel, SubtopicModel, TopicResponse

from src.v2.dashboard.topic.route import topic_router
from src.v2.dashboard.topic.manage_route import manage_topic_router
__all__ = [
    'TopicClassifierWrapper',
    'TopicGeneratorResponse',
    'TopicService',
    'TopicModel',
    'SubtopicModel',
    'TopicResponse'
]

topic_routers.include_router(topic_router)
topic_routers.include_router(manage_topic_router)
