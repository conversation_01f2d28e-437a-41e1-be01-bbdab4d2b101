"""
Routes for topic management operations.
"""
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.v2.dashboard.topic.manage import TopicService, TopicResponse
from typing import Optional, Dict, Any, List, Union, Literal
from pydantic import BaseModel
from bson import ObjectId
from src.helper.logger import setup_new_logging
loggers = setup_new_logging(__name__)

# Create a router with a different prefix and tag than the main topic router
manage_topic_router = APIRouter(prefix="/manage-topics", tags=["Topic Management"])

 
class TopicItem(BaseModel):
    """Base model for topic operations."""
    id: Optional[str] = None
    name: str
    type: Literal["topic", "subtopic"]
    parent_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class TopicUpdateRequest(BaseModel):
    """Request model for updating a topic or subtopic."""
    item: TopicItem


class BatchTopicCreate(BaseModel):
    """Request model for batch creating topics and subtopics."""
    topics: List[TopicItem]


@manage_topic_router.post("/topics", response_model=Dict[str, str])
async def create_topic(
    topic_data: TopicItem = Body(...),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Create a new topic or subtopic based on the type field.

    Args:
        topic_data: The topic data including name, type, optional parent_id and metadata
        current_user: Current user tenant database

    Returns:
        Dictionary with the ID of the created topic or subtopic
    """
    topic_service = TopicService(current_user.async_db)
    await topic_service.initialize()

    # If it's a subtopic, check if parent topic exists
    if topic_data.type == "subtopic":
        if not topic_data.parent_id:
            raise HTTPException(
                status_code=400,
                detail="Parent ID is required for subtopics"
            )

        parent = await topic_service.get_by_id(topic_data.parent_id)
        if not parent or parent.get("type") != "topic":
            raise HTTPException(
                status_code=404,
                detail=f"Parent topic with ID {topic_data.parent_id} not found"
            )

    # Create the topic or subtopic
    item_id = await topic_service.create(
        name=topic_data.name,
        type=topic_data.type,
        parent_id=topic_data.parent_id if topic_data.type == "subtopic" else None,
        metadata=topic_data.metadata
    )
    return {"id": item_id}


@manage_topic_router.get("/topics", response_model=Union[List[TopicResponse], Dict[str, Any]])
async def get_topics(
    topic_id: Optional[str] = Query(None, description="Optional topic ID to retrieve a specific topic"),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Get topics with their subtopics. If topic_id is provided, returns a specific topic.
    Otherwise, returns all topics.

    Args:
        topic_id: Optional ID of a specific topic to retrieve
        current_user: Current user tenant database

    Returns:
        Either a list of all topics or a specific topic with its subtopics
    """
    topic_service = TopicService(current_user.async_db)
    await topic_service.initialize()

    if topic_id:
        # Get a specific topic
        topic = await topic_service.get_topic_with_subtopics(topic_id)
        if not topic:
            raise HTTPException(
                status_code=404,
                detail=f"Topic with ID {topic_id} not found"
            )
        return topic
    else:
        # Get all topics
        return await topic_service.get_all()


@manage_topic_router.put("/topics", response_model=Dict[str, Any])
async def update_topic(
    topic_data: TopicItem = Body(...),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Update a topic or subtopic based on the provided data.

    Args:
        topic_data: The topic data including id, name, type, and optional metadata
        current_user: Current user tenant database

    Returns:
        Dictionary with update status and additional information
    """
    username = current_user.user.id
    if not topic_data.id:
        raise HTTPException(
            status_code=400,
            detail="Topic ID is required for updates"
        )

    topic_service = TopicService(current_user.async_db)
    await topic_service.initialize()

    # Check if document exists
    doc = await topic_service.get_by_id(topic_data.id)
    if not doc:
        raise HTTPException(
            status_code=404,
            detail=f"Document with ID {topic_data.id} not found"
        )

    # Check if the type matches
    if doc.get("type") != topic_data.type:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot change type from {doc.get('type')} to {topic_data.type}"
        )

    # If it's a subtopic, handle parent_id
    if topic_data.type == "subtopic":
        # If parent_id is provided and different from current, reassign
        current_parent_id = doc.get("parent_id")
        if topic_data.parent_id and str(current_parent_id) != topic_data.parent_id:
            # Check if new parent exists
            parent = await topic_service.get_by_id(topic_data.parent_id)
            if not parent or parent.get("type") != "topic":
                raise HTTPException(
                    status_code=404,
                    detail=f"Parent topic with ID {topic_data.parent_id} not found"
                )

            # Reassign to new parent
            reassign_success = await topic_service.reassign_subtopic(
                topic_data.id,
                topic_data.parent_id
                
            )
            if not reassign_success:
                return {
                    "success": False,
                    "message": "Failed to reassign subtopic to new parent"
                }

    # Update name and metadata
    success = await topic_service.update(
        doc_id=topic_data.id,
        new_name=topic_data.name,
        updated_by=username,
        metadata=topic_data.metadata,
    )

    return {
        "success": success,
        "message": "Topic updated successfully" if success else "Failed to update topic"
    }


@manage_topic_router.delete("/topics", response_model=Dict[str, Any])
async def delete_topic(
    topic_type: Literal["topic", "subtopic"] = Query(..., description="The type of the topic to delete"),
    topic_id: str = Query(..., description="The ID of the topic or subtopic to delete"),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Delete a topic or subtopic. If it's a topic, all its subtopics will also be deleted.

    Args:
        topic_id: The ID of the topic or subtopic to delete
        current_user: Current user tenant database

    Returns:
        Dictionary with the count of deleted documents and status message
    """
    try:
        topic_service = TopicService(current_user.async_db)
        await topic_service.initialize()

        # Check if document exists
        doc = await topic_service.get_by_id(topic_id)
        if not doc:
            raise HTTPException(
                status_code=404,
                detail=f"Document with ID {topic_id} not found"
            )
        if doc.get("type") != topic_type:
            raise HTTPException(
                status_code=400,
                detail=f"Document with ID {topic_id} is not a {topic_type}"
            )
        if topic_type=="topic":
            # Check if the topic has subtopics delete them
            await topic_service.delete_many({"parent_id": ObjectId(topic_id)})

        
        deleted_count = await topic_service.delete(topic_id)

        return {
            "deleted_count": deleted_count,
            "success": deleted_count > 0,
            "message": f"Successfully deleted {deleted_count} document(s)" if deleted_count > 0 else "No documents deleted"
        }
    except Exception as e:
        loggers.error(f"Error deleting topic: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting topic: {str(e)}"
        )


@manage_topic_router.post("/batch", response_model=Dict[str, Any])
async def batch_create_topics(
    batch_data: BatchTopicCreate = Body(...),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    """
    Create multiple topics and subtopics in a single batch operation.

    Args:
        batch_data: List of topic and subtopic data
        current_user: Current user tenant database

    Returns:
        Dictionary with the IDs of the created topics and subtopics, counts, and status
    """
    topic_service = TopicService(current_user.async_db)
    await topic_service.initialize()

    # First, validate all parent topics for subtopics
    parent_ids = set()
    for item in batch_data.topics:
        if item.type == "subtopic" and item.parent_id:
            parent_ids.add(item.parent_id)

    # Check if all parent_ids exist in the database
    existing_parent_ids = set()
    for parent_id in parent_ids:
        try:
            parent = await topic_service.get_by_id(parent_id)
            if parent and parent.get("type") == "topic":
                existing_parent_ids.add(parent_id)
        except:
            # Skip invalid ObjectIds
            pass

    # Prepare data for batch creation
    topics_data = []
    skipped_items = []

    # First process all topics
    for item in batch_data.topics:
        if item.type == "topic":
            topic_dict = {
                "name": item.name,
                "type": "topic",
                "metadata": item.metadata
            }
            topics_data.append(topic_dict)

    # Then process all subtopics with valid parent_ids
    for item in batch_data.topics:
        if item.type == "subtopic":
            if not item.parent_id:
                skipped_items.append({
                    "name": item.name,
                    "type": "subtopic",
                    "reason": "Missing parent_id"
                })
                continue

            # Check if parent exists in database or will be created in this batch
            parent_exists = item.parent_id in existing_parent_ids

            if parent_exists:
                subtopic_dict = {
                    "name": item.name,
                    "type": "subtopic",
                    "parent_id": item.parent_id,
                    "metadata": item.metadata
                }
                topics_data.append(subtopic_dict)
            else:
                skipped_items.append({
                    "name": item.name,
                    "type": "subtopic",
                    "parent_id": item.parent_id,
                    "reason": "Parent topic not found"
                })

    # Create the valid topics and subtopics
    ids = await topic_service.batch_create_topics(topics_data) if topics_data else []

    # Count topics and subtopics created
    topic_count = sum(1 for item in topics_data if item["type"] == "topic")
    subtopic_count = sum(1 for item in topics_data if item["type"] == "subtopic")

    return {
        "ids": ids,
        "total_created": len(ids),
        "topics_created": topic_count,
        "subtopics_created": subtopic_count,
        "skipped_items": skipped_items,
        "skipped_count": len(skipped_items),
        "success": len(ids) > 0,
        "message": f"Successfully created {topic_count} topics and {subtopic_count} subtopics" if ids else "No items created"
    }
