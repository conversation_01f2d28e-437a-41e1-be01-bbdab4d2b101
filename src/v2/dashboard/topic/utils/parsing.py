"""
Utility functions for parsing content from API responses.
"""

import re
import json
from src.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)

def parse_content(content):
    """
    Parse content to extract both topics and summary.
    
    Args:
        content: String content to parse
        
    Returns:
        Dictionary with 'topics' and 'summary' keys
    """
    # Parse content to extract both topics and summary
    result = {
        "topics": [],
        "summary": ""
    }

    # First try to extract summary using regex
    summary_pattern = re.compile(r'(?:Summary:|####\s*Summary:(?:---|\s*)|\*\*Summary:\*\*)([\s\S]+?)(?:$|\n\n|\n---)')
    summary_match = summary_pattern.search(content)

    if summary_match:
        summary = summary_match.group(1).strip()
        loggers.debug(f"Extracted summary using initial regex: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary using initial regex: {summary}")
        result["summary"] = summary

    # First try to parse as JSO<PERSON>
    try:
        # Try to load it as a JSON to properly format it
        parsed_data = json.loads(content)
        if isinstance(parsed_data, dict):
            # If it's a dictionary, check for topics and summary keys
            if "topics" in parsed_data:
                result["topics"] = parsed_data["topics"]
            if "summary" in parsed_data:
                result["summary"] = parsed_data["summary"]
            # Return the parsed data
            return result
        else:
            # If it's not a dictionary, assume it's just topics
            result["topics"] = parsed_data
            return result
    except json.JSONDecodeError:
        # If JSON parsing fails, try to parse as a structured text
        try:
            loggers.info(f"Content is not JSON, trying to parse as structured text")

            # Log the raw content for debugging
            loggers.debug(f"Raw content to parse: {content[:200]}..." if len(content) > 200 else f"Raw content to parse: {content}")

            # Check if the content follows the specific format we're seeing
            if content.startswith("Relevant Topics:") and "\n\nSummary:" in content:
                # Split into topics and summary sections
                topics_part, summary_part = content.split("\n\nSummary:", 1)
                topics_section = topics_part
                summary_section = "Summary: " + summary_part

                loggers.debug(f"Directly extracted topics section: {topics_section[:100]}..." if len(topics_section) > 100 else f"Directly extracted topics section: {topics_section}")
                loggers.debug(f"Directly extracted summary section: {summary_section[:100]}..." if len(summary_section) > 100 else f"Directly extracted summary section: {summary_section}")
            else:
                # Fall back to the original section splitting logic
                sections = content.split("---")
                loggers.debug(f"Found {len(sections)} sections after splitting by '---'")

                # Look for Relevant Topics section
                topics_section = None
                summary_section = None

                for section in sections:
                    # Check for various formats of topic headers
                    if any(header in section for header in [
                        "Relevant Topics:",
                        "**Relevant Topics:**",
                        "#### Relevant Topics:",
                        "####  Relevant Topics:"
                    ]):
                        topics_section = section
                    # Check for various formats of summary headers
                    elif any(header in section for header in [
                        "Summary:",
                        "**Summary:**",
                        "#### Summary:",
                        "####  Summary:",
                        "####  Summary:---"
                    ]):
                        summary_section = section

                loggers.debug(f"Found topics section: {bool(topics_section)}")
                loggers.debug(f"Found summary section: {bool(summary_section)}")

            # Parse topics section if found
            if topics_section:
                # Parse the topics section to extract topics and subtopics
                topics = parse_topics_section(topics_section)

                # Process the topics to ensure proper hierarchy
                # Look for subtopics that were incorrectly parsed as main topics
                result["topics"] = reorganize_topics(topics)

                # Log the final topics structure
                loggers.debug(f"Final topics structure: {result['topics']}")

            # Parse summary section if found and we don't already have a summary from regex
            if summary_section and not result["summary"]:
                summary = parse_summary_section(summary_section)
                result["summary"] = summary
                loggers.debug(f"Extracted summary from section: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary from section: {summary}")

            # If we still don't have a summary, try a more aggressive approach
            if not result["summary"]:
                # Look for any line containing 'Summary:' and take everything after it
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'Summary:' in line:
                        # Take all remaining lines as the summary
                        remaining_text = '\n'.join(lines[i+1:])
                        if remaining_text.strip():
                            result["summary"] = remaining_text.strip()
                            loggers.debug(f"Found summary using line-by-line search")
                        break

            return result
        except Exception as parsing_error:
            loggers.error(f"Error parsing content as structured text: {parsing_error}")
            return result

def parse_topics_section(topics_section):
    """
    Parse the topics section to extract topics and subtopics.
    
    Args:
        topics_section: String containing the topics section
        
    Returns:
        List of topic dictionaries
    """
    # Log the topics section for debugging
    loggers.debug(f"Parsing topics section: {topics_section[:200]}..." if len(topics_section) > 200 else f"Parsing topics section: {topics_section}")

    # Initialize the topics list
    topics = []

    # First, try to parse using regex for the specific format we're seeing
    # Pattern to match: "- Topic: X\n  - Sub-topics: [Y, Z]" or similar formats
    topic_pattern = re.compile(r'- (?:Topic: )?(.*?)(?:\n\s+- Sub-topics: \[(.*?)\]|$)', re.MULTILINE)
    matches = topic_pattern.findall(topics_section)

    if matches:
        loggers.debug(f"Found {len(matches)} topic matches using regex")

        # Process each match (topic and its subtopics)
        for topic_name, subtopics_str in matches:
            # Clean up the topic name
            topic_name = topic_name.strip()
            if not topic_name:
                continue

            # Create a new topic
            current_topic = {"topic": topic_name, "sub_topics": []}
            topics.append(current_topic)
            loggers.debug(f"Added main topic: {topic_name}")

            # Process subtopics if any
            if subtopics_str:
                # Split by comma to get individual subtopics
                subtopics = [s.strip() for s in subtopics_str.split(',')]

                # Add each subtopic
                for subtopic_name in subtopics:
                    if subtopic_name:  # Skip empty names
                        current_topic["sub_topics"].append({"name": subtopic_name})
                        loggers.debug(f"Added subtopic from list: {subtopic_name}")

    # If regex didn't find any topics, fall back to line-by-line parsing
    if not topics:
        loggers.debug("Falling back to line-by-line parsing")
        lines = topics_section.strip().split('\n')
        current_topic = None

        for line in lines:
            line = line.strip()
            if not line or any(line.startswith(header) for header in [
                "Relevant Topics:",
                "**Relevant Topics:**",
                "#### Relevant Topics:",
                "####  Relevant Topics:"
            ]):
                continue

            # Check if it's a main topic (starts with '- ')
            if line.startswith('- '):
                # Extract the topic name
                topic_text = line[2:].strip()

                # Check if it contains 'Sub-topics:' - this is a special case
                if 'Sub-topics:' in topic_text and current_topic is not None:
                    # This is actually a subtopic line for the current topic
                    subtopic_text = topic_text[topic_text.find('Sub-topics:')+len('Sub-topics:'):].strip()

                    # Check if the subtopic text contains a list in square brackets
                    if '[' in subtopic_text and ']' in subtopic_text:
                        # Extract the list part
                        list_part = subtopic_text[subtopic_text.find('[')+1:subtopic_text.find(']')]
                        # Split by comma to get individual subtopics
                        subtopics = [s.strip() for s in list_part.split(',')]

                        # Add each subtopic
                        for subtopic_name in subtopics:
                            if subtopic_name:  # Skip empty names
                                current_topic["sub_topics"].append({"name": subtopic_name})
                                loggers.debug(f"Added subtopic from list: {subtopic_name}")
                else:
                    # This is a main topic
                    # Check if it starts with 'Topic:'
                    if topic_text.startswith('Topic:'):
                        topic_name = topic_text[len('Topic:'):].strip()
                    else:
                        topic_name = topic_text

                    # Create a new topic
                    current_topic = {"topic": topic_name, "sub_topics": []}
                    topics.append(current_topic)
                    loggers.debug(f"Added main topic: {topic_name}")

            # Check if it's a subtopic line (starts with '  - ' or more spaces)
            elif (line.startswith('  - ') or line.startswith('   - ')) and current_topic is not None:
                subtopic_text = line.lstrip(' -').strip()

                # Check if it contains 'Sub-topics:'
                if 'Sub-topics:' in subtopic_text:
                    subtopic_text = subtopic_text[subtopic_text.find('Sub-topics:')+len('Sub-topics:'):].strip()

                # Check if the subtopic text contains a list in square brackets
                if '[' in subtopic_text and ']' in subtopic_text:
                    # Extract the list part
                    list_part = subtopic_text[subtopic_text.find('[')+1:subtopic_text.find(']')]
                    # Split by comma to get individual subtopics
                    subtopics = [s.strip() for s in list_part.split(',')]

                    # Add each subtopic
                    for subtopic_name in subtopics:
                        if subtopic_name:  # Skip empty names
                            current_topic["sub_topics"].append({"name": subtopic_name})
                            loggers.debug(f"Added subtopic from list: {subtopic_name}")
                else:
                    # Regular subtopic format
                    subtopic_name = subtopic_text
                    # Add as a subtopic to the current main topic
                    current_topic["sub_topics"].append({"name": subtopic_name})
                    loggers.debug(f"Added regular subtopic: {subtopic_name}")

    # Post-process to handle any remaining "Sub-topics: [...]" entries
    processed_topics = []

    for topic in topics:
        topic_name = topic["topic"]

        # Check if this is a "Sub-topics: [...]" entry
        if topic_name.startswith("Sub-topics:") and "[" in topic_name and "]" in topic_name:
            # This should be subtopics for the previous topic
            if processed_topics:  # Make sure we have a previous topic
                previous_topic = processed_topics[-1]

                # Extract the subtopics
                subtopics_text = topic_name[topic_name.find('[')+1:topic_name.find(']')]
                subtopics = [s.strip() for s in subtopics_text.split(',')]

                # Add each subtopic to the previous topic
                for subtopic_name in subtopics:
                    if subtopic_name:  # Skip empty names
                        previous_topic["sub_topics"].append({"name": subtopic_name})
                        loggers.debug(f"Added subtopic to previous topic: {subtopic_name}")
        else:
            # This is a regular topic
            processed_topics.append(topic)

    # Log the parsed topics for debugging
    loggers.debug(f"Parsed topics after post-processing: {processed_topics}")

    return processed_topics

def parse_summary_section(summary_section):
    """
    Parse the summary section to extract the summary text.
    
    Args:
        summary_section: String containing the summary section
        
    Returns:
        String containing the summary
    """
    lines = summary_section.strip().split('\n')
    summary_lines = []

    # Log the summary section for debugging
    loggers.debug(f"Parsing summary section: {summary_section}")

    for line in lines:
        line = line.strip()
        # Skip header lines with various formats
        if any(line.startswith(header) for header in [
            "Summary:",
            "**Summary:**",
            "#### Summary:",
            "####  Summary:",
            "####  Summary:---"
        ]):
            continue
        if line:
            summary_lines.append(line)

    summary = "\n".join(summary_lines).strip()
    loggers.debug(f"Extracted summary: {summary[:100]}..." if len(summary) > 100 else f"Extracted summary: {summary}")
    return summary

def reorganize_topics(topics):
    """
    Reorganize topics to ensure proper hierarchy.
    This method identifies subtopics that were incorrectly parsed as main topics
    and moves them under their parent topics.
    
    Args:
        topics: List of topic dictionaries
        
    Returns:
        List of reorganized topic dictionaries
    """
    # First, get the predefined topic hierarchy from the prompt
    # This is a simplified approach - in a real implementation, you might want to
    # pass the predefined hierarchy as a parameter
    predefined_hierarchy = {}

    # Create a mapping of topic names to their proper structure
    for topic in topics:
        topic_name = topic["topic"]
        predefined_hierarchy[topic_name] = {
            "is_main": True,  # Assume it's a main topic by default
            "parent": None,   # No parent by default
            "data": topic     # The original topic data
        }

        # Check if this topic appears as a subtopic in any other topic
        for other_topic in topics:
            if other_topic["topic"] != topic_name:  # Don't compare with itself
                for subtopic in other_topic["sub_topics"]:
                    if subtopic["name"] == topic_name:
                        # This topic is actually a subtopic of another topic
                        predefined_hierarchy[topic_name]["is_main"] = False
                        predefined_hierarchy[topic_name]["parent"] = other_topic["topic"]
                        break

    # Now reorganize the topics based on the hierarchy
    reorganized_topics = []
    processed_topics = set()  # Track topics that have been processed

    # First, add all main topics
    for topic_name, info in predefined_hierarchy.items():
        if info["is_main"] and topic_name not in processed_topics:
            reorganized_topics.append(info["data"])
            processed_topics.add(topic_name)

    # Now, for each main topic, find its subtopics that were incorrectly parsed as main topics
    for topic in reorganized_topics:
        topic_name = topic["topic"]

        # Look for topics that should be subtopics of this topic
        for other_name, info in predefined_hierarchy.items():
            if not info["is_main"] and info["parent"] == topic_name and other_name not in processed_topics:
                # This is a subtopic that was incorrectly parsed as a main topic
                # Add its data as a subtopic to the current topic
                subtopic_data = {"name": other_name}

                # If the subtopic has its own subtopics, preserve them
                if info["data"]["sub_topics"]:
                    subtopic_data["sub_topics"] = info["data"]["sub_topics"]

                topic["sub_topics"].append(subtopic_data)
                processed_topics.add(other_name)

    # Log the reorganized topics for debugging
    loggers.debug(f"Reorganized topics: {reorganized_topics}")

    return reorganized_topics
