"""
Utility functions for calculating API usage costs.
"""

from datetime import datetime
import os
import json
from src.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)

# GPT-4.1-mini pricing constants (as of April 2025)
GPT41_MINI_PROMPT_PRICE_PER_1K = 0.0004  # $0.0004 per 1K prompt tokens
GPT41_MINI_COMPLETION_PRICE_PER_1K = 0.0016  # $0.0016 per 1K completion tokens

def calculate_cost(prompt_tokens, completion_tokens, model="gpt-4.1-mini"):
    """
    Calculate the cost of API usage based on token counts and model.

    Args:
        prompt_tokens: Number of prompt tokens used
        completion_tokens: Number of completion tokens used
        model: Model name (currently only supports 'gpt-4.1-mini')

    Returns:
        Dictionary containing cost breakdown and total cost
    """
    if model == "gpt-4.1-mini":
        prompt_price = (prompt_tokens / 1000) * GPT41_MINI_PROMPT_PRICE_PER_1K
        completion_price = (completion_tokens / 1000) * GPT41_MINI_COMPLETION_PRICE_PER_1K
        total_price = prompt_price + completion_price

        return {
            "model": model,
            "prompt_tokens": prompt_tokens,
            "completion_tokens": completion_tokens,
            "total_tokens": prompt_tokens + completion_tokens,
            "prompt_cost": prompt_price,
            "completion_cost": completion_price,
            "total_cost": total_price
        }
    else:
        # For unsupported models, return None or raise an exception
        loggers.error(f"Cost calculation for model '{model}' is not supported")
        return None

def save_batch_info_to_file(batch_info, file_path=None):
    """
    Utility function to save batch information to a file.

    Args:
        batch_info: List of dictionaries containing batch information
        file_path: Path to save the file. If None, a default path with timestamp will be used.

    Returns:
        Path to the saved file
    """
    if file_path is None:
        # Create a default file path with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"batch_info_{timestamp}.json"

    # Create directory if it doesn't exist
    directory = os.path.dirname(file_path)
    if directory and not os.path.exists(directory):
        os.makedirs(directory)

    # Save batch information to file
    try:
        with open(file_path, 'w') as f:
            json.dump(batch_info, f, indent=2)
        loggers.info(f"Batch information saved to {file_path}")
        return file_path
    except Exception as e:
        loggers.error(f"Error saving batch information to file: {e}")
        return None
