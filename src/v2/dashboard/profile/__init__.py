from bson import ObjectId
from fastapi import APIRouter, Depends, HTTPException
from src.helper import logger,convert_objectid_to_str
from src.models.user import UserTenantDB
from src.core.security import get_tenant_info
from src.models.profile import CustomerProfileResponse, AdditionalInformation
import asyncio

loggers = logger.setup_new_logging(__name__)

router = APIRouter(prefix="/Profile",tags=["Profile"])


@router.get("/chat-message/profile/{chat_data_id}")
async def get_chat_message(chat_data_id: str, current_user: UserTenantDB = Depends(get_tenant_info)):

    """
    Fetch the chat message for a given chat data ID.

    Args:
        chat_data_id (str): The ID of the chat data.

    Returns:
        dict: The chat message.
    """
    chat_data_col = current_user.db.chat_messages
    print(current_user)

    chat_message = chat_data_col.find_one({"_id": ObjectId(chat_data_id)})
    chat_message = convert_objectid_to_str(chat_message)
    return chat_message

@router.get("/{customer_id}/profile", response_model=CustomerProfileResponse)
async def get_customer_profile(
    customer_id: int, 
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Fetch the customer profile and the latest AI response for a given customer ID.

    Args:
        customer_id (int): The ID of the customer.

    Returns:
        CustomerProfileResponse: The customer profile data with the latest AI response.
    """
    customers_col = current_user.db.customers
    bot_responses_col = current_user.db.ai_response

    customer = customers_col.find_one({"customer_id": customer_id})
    if not customer:
        raise HTTPException(status_code=404, detail="Customer not found")

    latest_message = bot_responses_col.find_one(
        {"request.user_id": customer_id},
        {
            "customer_id": "$request.user_id",
            "customer_name": "$request.user_name",
            "latest_message": "$request.message",
            "chat_data_ids": "$request.chat_data",
            "primary_product": "$response.identified_product",
            # "purchased_product": "$request.purchased_product",
            "categories": "$response.categories",
            "latest_ai_summary": "$response.formatted_summary",
            "summary_id": "$response.summary_id",
            "sentiment": "$response.sentiment",
            "language": "$response.language",
            "created_at": 1
        },
        sort=[("created_at", -1)]
    )

    # Handle case where no AI response exists
    if not latest_message:
        raise HTTPException(status_code=404, detail="No AI response found for the customer")

    # Convert ObjectId to string
    latest_message = convert_objectid_to_str(latest_message)
    latest_message["created_at"] = latest_message["created_at"].strftime("%Y-%m-%d %H:%M:%S")

    # Fetch related chat data asynchronously
    latest_message["chat_data"] = await asyncio.gather(
        *[get_chat_message(chat_data_id, current_user) for chat_data_id in latest_message["chat_data_ids"]]
    )
    latest_message.pop("chat_data_ids")

    # Ensure name is a valid string
    customer_name = customer.get("name") or "Unknown Customer"

    # Prepare the response using the Pydantic model
    customer_profile = CustomerProfileResponse(
        name=customer_name,
        trending_product=latest_message.get("primary_product"),
        trending_category=latest_message.get("categories", []),
        latest_message=latest_message.get("latest_message"),
        latest_summary=latest_message.get("latest_ai_summary"),
        purchased_product=latest_message.get("purchased_product"),
        summary_id=str(latest_message["summary_id"]) if latest_message.get("summary_id") else None,
        sentiment=latest_message.get("sentiment"),
        language=latest_message.get("language"),
        # additional_information=AdditionalInformation(
        #     phone_number=customer["additional_information"].get("phone_number", "not provided"),
        #     is_course_activated=customer["additional_information"].get("is_course_activated", False),
        #     is_payment_done=customer["additional_information"].get("is_payment_done", False)
        # )
        additional_information=customer["additional_information"]
    )

    return customer_profile
