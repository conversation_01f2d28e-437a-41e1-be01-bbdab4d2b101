from pydantic import BaseModel
from typing import Optional, Literal,Any
from fastapi import APIRouter, Depends
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from bson import ObjectId
from datetime import datetime
from src.reply.minio_client import MinIO<PERSON>lient, MinIOConfig
from src.routes.send_reply import send_reply_
from src.v2.external_hooks.whatsapp_webhook.send_message import send_whatsapp_message
from src.models.chat_hist import ChatHistMessage
from pymongo.errors import PyMongoError
from fastapi.responses import JSONResponse
from fastapi.exceptions import HTTPException
from src.helper.logger import setup_new_logging
import re
from src.models.credit import CreditManager, get_credit_info

loggers = setup_new_logging(__name__)


class new_customer(BaseModel):
    name: Optional[str]
    country_code: Optional[str] = None
    phone_number: str
    email: Optional[str] = None
    address: Optional[str] = None
    message: Optional[str]=None
    channel: Literal["whatsapp"] = "whatsapp"
    media_ids: Optional[list[str]]= []
    template_id:str = None
    template_vars:Optional[dict]={}

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "name": "test name",
                    "country_code": "+977",
                    "phone_number": "9762429895",
                    "message": "Hello ",
                    "channel": "whatsapp",
                    "send_message": False,
                    "template_id": "23141241241",
                    "template_vars": {"1": "User", "2": "Variable2"}
                }
            ]
        }
    }


class WhatsAppMessageRequest:
    def __init__(self, user_id: str,country_code: str ,message: str, phone_number: str):
        self.user_id = user_id
        self.message = message
        self.media_url = []
        self.ai_response_id = ObjectId()
        self.country_code = country_code
        self.phone_number = phone_number


cust_router = APIRouter(tags=["Customers"])


@cust_router.post("/new_customer")
async def new_customer(
    request: new_customer, current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Create a new customer by sending a message to their WhatsApp number.

    args:
        request (new_customer): The request object containing the customer details.
        what request contains:
            name (str,optional): The name of the customer.
            phone_number (str): The phone number of the customer.
            email (str, optional): The email address of the customer.
            address (str, optional): The address of the customer.
            message (str): The message to be sent to the customer.
            channel (str, optional): The channel to send the message (default is "whatsapp").
            send_message (bool, optional): Whether to send the message or not (default is False).

            False is to test the endpoint
            True is to send the message

        current_user (UserTenantDB): The current user context with database access.

    returns:
        JSONResponse: A JSON response indicating the success or failure of the operation.
    """
    try:
        credit_manager = CreditManager(current_user.db)
        per_cost, remaining_credit = get_credit_info(cost_type="whatsapp_msg_cost",current_user=current_user)
        if remaining_credit < per_cost:
            raise HTTPException(status_code=402, detail=f"Insufficient credits. Required: {per_cost}, Available: {remaining_credit}")
        flag=current_user.db.settings.find_one({"name": "message_flag"}).get("flag")
        if not flag:
            return JSONResponse(status_code=200, content={"message": "Activate the flag to send messages"})

        # check if the number exists in the database
        customer_sid=re.sub(r'\D', '', request.country_code + request.phone_number)
        customer = current_user.db.customers.find_one(
            {"customer_id": customer_sid}
        )

        if request.channel == "whatsapp":
            if not customer:
                customer_id = ObjectId()
                # insert the new customer
                customer = request.model_dump(exclude={"send_message", "message", "media_ids"})
                # Add whatsapp_number field for WhatsApp messaging
                customer["_id"] = customer_id
                customer["customer_id"] = customer_sid
                customer["created_at"] = datetime.now()
                try:
                    customer["whatsapp_number"] = f"whatsapp:{(request.country_code + request.phone_number) if request.country_code and request.country_code.strip() else request.phone_number}"
                except Exception as e:
                    loggers.error(f"Error setting whatsapp_number: {str(e)}")
                    raise Exception(f"Error setting whatsapp_number: {str(e)}")
                current_user.db.customers.insert_one(customer)

            # MinIO Client setup
            minio_config = current_user.db.settings.find_one({"name": "env"}).get(
                "minio_config"
            )
            minio = MinIOClient(
                config=MinIOConfig(
                    access_key=minio_config.get("access_key"),
                    secret_key=minio_config.get("secret_key"),
                    minio_url=minio_config.get("minio_url"),
                    bucket_name=minio_config.get("bucket_name"),
                )
            )

            response_id = ObjectId()

            # AI Response to be inserted
            ai_response = {
                "_id": response_id,
                "request": {
                    "user_id": customer_sid,
                    "user_name": request.name,
                    "message": " ",
                    "channel": "Whatsapp",
                    "media_ids": [],
                    "media_values": "",
                    "chat_data": [],
                    "tags": [],
                    "mode": "elaborated",
                    "primary_product": None,
                    "primary_product_code": None,
                    "incoming_chat": [],
                    "chat_data_format": "role_data",
                },
                "response": {
                    "request_time": datetime.now(),
                    "processing_time": 0,
                    "reply": request.message,
                    "identified_product": None,
                    "information_gathering": [],
                    "chat_ids": [],
                    "chat_data": [],
                    "latest_message": None,
                    "metadata": [],
                    "language": None,
                    "verified": False,
                    "background_processing_completed": False,
                    "reply_urls": [],
                    "source_nodes": [],
                    "call_to_action": [],
                    "has_credit":True
                },
                "created_at": datetime.now(),
            }

            # Fix for MongoDB insert - correct syntax for ai_response_id
            ai_response = current_user.db.ai_response.insert_one(ai_response)

            # Send the message
            send_request = send_reply_(
                user_id=customer_sid,
                message=request.message,
                ai_response_id=str(response_id),
                media_url=request.media_ids,
                channel=request.channel,
            )
            send_ = await send_whatsapp_message(send_request, minio, current_user,template_id=request.template_id,template_vars=request.template_vars)
            

            loggers.info(f"Response: {send_}")
            credit_result = credit_manager.deduct_credits(
                amount=per_cost,
                description="Whatsapp Message(Created New Customer)",
                message_id=response_id,
            )
            if not credit_result["success"]:
                loggers.error(f"Failed to deduct credits while sending message to new customer: {credit_result['message']}")

            return JSONResponse(
                status_code=200,
                content={"message": "Whatsapp message sent successfully"},
            )
        else:
            raise HTTPException(status_code=500, detail="Invalid channel")
    except HTTPException as e:
        raise e
    except PyMongoError as e:
        # Specific exception handling for MongoDB errors
        return {"error": f"Database error: {str(e)}"}
    except Exception as e:
        # General exception handling
        import traceback
        traceback.print_exc()
        return {"error": f"Failed to create new customer: {str(e)}"}



@cust_router.put("/change_msg_flag")
async def change_msg_flag(flag:bool=False,current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        current_user.db.settings.update_one({"name": "message_flag"}, {"$set": {"flag": flag}})
        return JSONResponse(status_code=200, content={"message": "success", "flag": flag})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@cust_router.get("/get_msg_flag")
async def get_msg_flag(current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        flag = current_user.db.settings.find_one({"name": "message_flag"}).get("flag")
        return JSONResponse(status_code=200, content={"flag": flag})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))



