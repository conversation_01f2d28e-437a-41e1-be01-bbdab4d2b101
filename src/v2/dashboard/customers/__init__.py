from fastapi import APIRouter, HTTPException, Depends, Query
from src.helper.filter_access import get_filter_access
from src.helper import logger, convert_objectid_to_str
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.models.customers import CustomerModel, AllCustomersRequest
from src.models.setup import SavedReply
from pymongo import DESCENDING, ASCENDING
from pymongo.collection import Collection
from typing import List, Dict, Optional
from bson import ObjectId
import asyncio
from datetime import datetime, timedelta

loggers = logger.setup_new_logging(__name__)

router = APIRouter(prefix="/customers", tags=["Customers"])


@router.get("/ai_responses")
async def fetch_ai_responses(
    search: Optional[str] = Query(default=None),
    tags: Optional[list] = Query(default=None),
    cta: Optional[list] = Query(default=None),
    status: Optional[list] = Query(default=None),
    identified_product: Optional[List] = Query(default=None),
    topic: Optional[List] = Query(default=None),
    subtopic: Optional[List] = Query(default=None),
    start_date: Optional[datetime] = Query(default=None),
    end_date: Optional[datetime] = Query(default=None),
    language: Optional[list] = Query(default=None),
    categories: Optional[list] = Query(default=None),
    verified: Optional[bool] = Query(default=None),
    sentiment: Optional[list] = Query(default=None),
    user_id: Optional[str] = Query(default=None),
    ai_replied: Optional[bool] = Query(default=None),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    bot_responses_col = current_user.db.ai_response
    
    blocked_filters = get_filter_access(current_user, "ai_response",False)
        
    
    # Calculate pagination parameters
    skip = (page - 1) * page_size
    
    # Base match query for initial filtering
    base_match = {}
    # Define verified query if needed
    verified_query = {}
    if verified :
        verified_query = {"latest_document.response.verified": verified}
    else:
        base_match["verified"] = {"$nin": blocked_filters.get("channel", [])}
    
    
    # Date range filter
    if start_date is not None and end_date is not None:
        base_match["created_at"] = {
            "$gte": start_date.replace(hour=0, minute=0, second=0),
            "$lte": end_date.replace(hour=23, minute=59, second=59),
        }
    elif start_date is not None or end_date is not None:
        raise HTTPException(status_code=400, detail="Both start_date and end_date must be provided together")
    
    # User ID filter (direct match)
    if user_id:
        base_match["request.user_id"] = user_id
        
    
    # Exclude Playground by default
    # if not tags:
    #     base_match["request.channel"] = {"$ne": "Playground"}
    
    # Search filter
    if search:
        # First perform a lookup to find matching customers by email
        customer_matches = list(current_user.db.customers.aggregate([
            {
                "$match": {
                    "$or": [
                        {"email": {"$regex": search, "$options": "i"}},
                        {"whatsapp_id": {"$regex": search, "$options": "i"}},
                        {"phone_number": {"$regex": search, "$options": "i"}}
                    ]
                }
            },
            {
                "$project": {
                    "customer_id": 1,
                    "_id": 0
                }
            }
        ]))
        
        # Extract the matching customer IDs
        matching_customer_ids = [c["customer_id"] for c in customer_matches]
        
        # Update the query to include these customer IDs in the search
        base_match["$or"] = [
            {"request.user_name": {"$regex": search, "$options": "i"}},
            {"request.user_id": {"$regex": search, "$options": "i"}},
            {"request.user_id": {"$in": matching_customer_ids}},
            {"response.language": {"$regex": search, "$options": "i"}},
            {"response.sentiment": {"$regex": search, "$options": "i"}},
            {"response.identified_product": {"$regex": search, "$options": "i"}},
            {"request.channel": {"$regex": search, "$options": "i"}},
            {"response.call_to_action.type": {"$regex": search, "$options": "i"}},
            {"response.call_to_action.status": {"$regex": search, "$options": "i"}},
            {"response.topic": {"$regex": search, "$options": "i"}},
        ]
    
    # Build the aggregation pipeline
    pipeline = [
        # Initial match to filter by date range and user_id (if provided)
        {"$match": base_match},
        
        # Group by user_id to collect all messages for each user
        {
            "$group": {
                "_id": "$request.user_id",
                "customer_id": {"$first": "$request.user_id"},
                "customer_name": {"$first": "$request.user_name"},
                "created_at": {"$max": "$created_at"},
                "channel": {"$first": "$request.channel"},
                "documents": {"$push": "$$ROOT"},
                "doc_count": {"$sum": 1}
            }
        }
    ]
    
    # Add filter conditions for grouped data
    filter_conditions = []
    
    # Channel filter
    if tags:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"request.channel": {"$in": tags}}}}
            ]
        })
    else:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"request.channel": {"$nin": blocked_filters.get("channel", [])}}}}
            ]
        })
    # Topic filter
    if topic:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"response.topic.topics.topic": {"$in": topic}}}}
            ]
        })

    # Subtopic filter
    if subtopic:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"response.topic.topics.sub_topics.name": {"$in": subtopic}}}}
            ]
        })

    # Language filter
    if language:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"response.language": {"$in": language}}}}
            ]
        })

    # CTA type and status filter
    if cta and status:
        valid_cta = [c for c in cta if c is not None]
        valid_status = [s for s in status if s is not None]
        if valid_cta and valid_status:
            filter_conditions.append({
                "$or": [
                    {"documents": {"$elemMatch": {
                        "response.call_to_action.type": {"$in": valid_cta},
                        "response.call_to_action.status": {"$in": valid_status}
                    }}}
                ]
            })

    # CTA type filter
    elif cta:
        valid_cta = [c for c in cta if c is not None]
        if valid_cta:
            filter_conditions.append({
                "$or": [
                    {"documents": {"$elemMatch": {"response.call_to_action.type": {"$in": valid_cta}}}}
                ]
            })

    # CTA status filter
    elif status:
        valid_status = [s for s in status if s is not None]
        if valid_status:
            filter_conditions.append({
                "$or": [
                    {"documents": {"$elemMatch": {"response.call_to_action.status": {"$in": valid_status}}}}
                ]
            })

    # Product filter
    if identified_product:
        if current_user.tenant_id == "6762cd86b4e4379d71ef93ee":
            filter_conditions.append({
                "$or": [
                    {"documents": {"$elemMatch": {"request.primary_product": {"$in": identified_product}}}}
                ]
            })
        else:
            filter_conditions.append({
                "$or": [
                    {"documents": {"$elemMatch": {"response.identified_product": {"$in": identified_product}}}}
                ]
            })

    # Categories filter
    if categories:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"response.categories": {"$in": categories}}}}
            ]
        })

    # Sentiment filter
    if sentiment:
        filter_conditions.append({
            "$or": [
                {"documents": {"$elemMatch": {"response.sentiment": {"$in": sentiment}}}}
            ]
        })
    
    # Apply all filter conditions if any exist
    if filter_conditions:
        pipeline.append({"$match": {"$and": filter_conditions}})
    
    # print("\n\n pipeline",pipeline)
    
    # Continue with the rest of the pipeline to format the results
    pipeline.extend([
        # Sort by latest message date
        {"$sort": {"created_at": -1}},
        
        # Lookup customer info
        {
            "$lookup": {
                "from": "customers",
                "let": {"userId": "$customer_id"},
                "pipeline": [
                    {
                        "$match": {
                            "$expr": {"$eq": ["$customer_id", "$$userId"]}
                        }
                    },
                    {
                        "$project": {
                            "email": 1,
                            "phone_number": 1,
                            "country_code": 1,
                            "whatsapp_id": 1,
                            "_id": 0
                        }
                    }
                ],
                "as": "customer_info"
            }
        },
        
        # Add customer info fields
        {
            "$addFields": {
                "email": {"$ifNull": [{"$arrayElemAt": ["$customer_info.email", 0]}, None]},
                # add country cod ei phone number
                "contact_number": {"$ifNull": [{"$arrayElemAt": ["$customer_info.phone_number", 0]}, None]},
                "country_code": {"$ifNull": [{"$arrayElemAt": ["$customer_info.country_code", 0]}, None]},
               
                "whatsapp_number": {"$ifNull": [{"$arrayElemAt": ["$customer_info.phone_number", 0]}, None]},
                "profile_picture": None
            }
        },
        
        # Get the latest chat data
       
       
       {
  "$addFields": {
    "latest_document": { "$arrayElemAt": ["$documents", -1] },
    "all_cta": {
      "$cond": {
        "if": {
          "$or": [
            { "$gt": [{ "$size": { "$ifNull": ["$cta", []] } }, 0] },
            { "$gt": [{ "$size": { "$ifNull": ["$status", []] } }, 0] }
          ]
        },
        "then": {
          "$reduce": {
            "input": "$documents",
            "initialValue": [],
            "in": {
              "$concatArrays": [
                "$$value",
                {
                  "$cond": [
                    { "$isArray": "$$this.response.call_to_action" },
                    {
                      "$filter": {
                        "input": "$$this.response.call_to_action",
                        "as": "cta_item",
                        "cond": {
                          "$switch": {
                            "branches": [
                              {
                                "case": {
                                  "$and": [
                                    { "$gt": [{ "$size": { "$ifNull": ["$cta", []] } }, 0] },
                                    {
                                      "$or": [
                                        { "$eq": ["$status", None] },
                                        { "$eq": [{ "$size": { "$ifNull": ["$status", []] } }, 0] }
                                      ]
                                    }
                                  ]
                                },
                                "then": { "$in": ["$$cta_item.type", "$cta"] }
                              },
                              {
                                "case": {
                                  "$and": [
                                    { "$gt": [{ "$size": { "$ifNull": ["$status", []] } }, 0] },
                                    {
                                      "$or": [
                                        { "$eq": ["$cta", None] },
                                        { "$eq": [{ "$size": { "$ifNull": ["$cta", []] } }, 0] }
                                      ]
                                    }
                                  ]
                                },
                                "then": { "$in": ["$$cta_item.status", "$status"] }
                              },
                              {
                                "case": {
                                  "$and": [
                                    { "$gt": [{ "$size": { "$ifNull": ["$cta", []] } }, 0] },
                                    { "$gt": [{ "$size": { "$ifNull": ["$status", []] } }, 0] }
                                  ]
                                },
                                "then": {
                                  "$and": [
                                    { "$in": ["$$cta_item.type", "$cta"] },
                                    { "$in": ["$$cta_item.status", "$status"] }
                                  ]
                                }
                              }
                            ],
                            "default": True
                          }
                        }
                      }
                    },
                    []
                  ]
                }
              ]
            }
          }
        },
        "else": {
          "$reduce": {
            "input": "$documents",
            "initialValue": [],
            "in": {
              "$concatArrays": [
                "$$value",
                {
                  "$cond": [
                    { "$isArray": "$$this.response.call_to_action" },
                    "$$this.response.call_to_action",
                    []
                  ]
                }
              ]
            }
          }
        }
      }
    }
  }
}
       ,
        
        # Format the latest chat content
        {
            "$addFields": {
                "latest_chat_content": {
                    "$let": {
                        "vars": {
                            "latest_chat_data": {
                                "$arrayElemAt": [
                                    {
                                        "$filter": {
                                            "input": {"$ifNull": ["$latest_document.response.chat_data", []]},
                                            "as": "chat",
                                            "cond": {"$ne": ["$$chat", None]}
                                        }
                                    },
                                    -1
                                ]
                            }
                        },
                        "in": {
                            "role": "$$latest_chat_data.role",
                            "content": "$$latest_chat_data.content",
                            "created_at": "$$latest_chat_data.created_at"
                        }
                    }
                },
                "ai_replied": {
                    "$gt": [
                        {
                            "$size": {
                                "$filter": {
                                    "input": {"$ifNull": ["$latest_document.response.chat_data", []]},
                                    "as": "chat",
                                    "cond": {
                                        "$and": [
                                            {"$ne": ["$$chat.role", None]},
                                            {"$in": ["$$chat.role", ["assistant", "agent", "system"]]}
                                        ]
                                    }
                                }
                            }
                        },
                        0
                    ]
                }
            }
        },
        
        # Calculate CTA statistics
        {
            "$addFields": {
                "cta_count": {"$size": "$all_cta"},
                "cta_distinct_count": {
                    "$map": {
                        "input": {
                            "$setUnion": {
                                "$map": {
                                    "input": "$all_cta",
                                    "as": "cta",
                                    "in": "$$cta.type"
                                }
                            }
                        },
                        "as": "cta_type",
                        "in": {
                            "type": "$$cta_type",
                            "total": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_cta",
                                        "as": "cta",
                                        "cond": {"$eq": ["$$cta.type", "$$cta_type"]}
                                    }
                                }
                            },
                            "resolved_count": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_cta",
                                        "as": "cta",
                                        "cond": {
                                            "$and": [
                                                {"$eq": ["$$cta.type", "$$cta_type"]},
                                                {"$eq": ["$$cta.status", "resolved"]}
                                            ]
                                        }
                                    }
                                }
                            },
                            "unresolved_count": {
                                "$size": {
                                    "$filter": {
                                        "input": "$all_cta",
                                        "as": "cta",
                                        "cond": {
                                            "$and": [
                                                {"$eq": ["$$cta.type", "$$cta_type"]},
                                                {"$ne": ["$$cta.status", "resolved"]}
                                            ]
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        
        # Filter by verified status if specified
        {"$match": verified_query if verified is not None else {}},
        
        # Filter by AI replied status if specified
        {"$match": {"ai_replied": ai_replied} if ai_replied is not None else {}},
        
        # Count total records for pagination
        {
            "$facet": {
                "data": [
                    {"$skip": skip},
                    {"$limit": page_size},
                    {
                        "$project": {
                            "_id": "$customer_id",
                            "customer_id": 1,
                            "customer_name": 1,
                            "email": 1,
                            "country_code": 1,
                            # "phone_number": 1,
                            "whatsapp_number": 1,
                            "profile_picture": 1,
                            "created_at": 1,
                            "channel": 1,
                            "latest_chat_content": 1,
                            "ai_replied": 1,
                            "cta_count": 1,
                            "cta_distinct_count": 1
                        }
                    }
                ],
                "count": [{"$count": "total"}]
            }
        }
    ])
    
    # Execute the pipeline
    result = list(bot_responses_col.aggregate(pipeline))
    
    # Extract data and count
    data = result[0]["data"] if result and "data" in result[0] else []
    total_records = result[0]["count"][0]["total"] if result and "count" in result[0] and result[0]["count"] else 0
    
    # Return the paginated response
    return {
        "page": page,
        "page_size": page_size,
        "total_records": total_records,
        "total_pages": (total_records + page_size - 1) // page_size,
        "data": data
    }





@router.get("/{customer_id}/latest-messages")
async def get_latest_messages(
    customer_id: int, current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Fetch the latest message for a given customer ID.

    Args:
        customer_id (int): The ID of the customer.

    Returns:
        dict: The latest message for the customer.
    """
    # Query MongoDB for the latest message of the customer
    bot_responses_col = current_user.db.ai_response

    latest_message = list(
        bot_responses_col.find(
            {"request.user_id": customer_id},
            {
                "customer_id": "$request.user_id",
                "customer_name": "$request.user_name",
                "latest_message": "$request.message",
                "chat_data_ids": "$request.chat_data",
                "primary_product": "$request.primary_product",
                "latest_ai_reply": "$response.reply",
                "suggested_ai_reply": "$response.suggested_reply",
                "verified": "$response.verified",
            },
        )
        .sort("created_at", DESCENDING)
        .limit(1)
    )
    if latest_message:
        latest_message = latest_message[0]
    else:
        raise HTTPException(
            status_code=404, detail="No messages found for the customer"
        )

    loggers.info(latest_message)

    latest_message = convert_objectid_to_str(latest_message)
    latest_message["created_at"] = latest_message["created_at"].strftime(
        "%Y-%m-%d %H:%M:%S"
    )

    chat_data = await get_chat_message(latest_message["chat_data_ids"], current_user)
    system_message = {
        "role": "system",
        "content": latest_message["latest_ai_reply"],
        "sender": "Bot",
        "created_at": latest_message["created_at"],
        "user_id": latest_message["customer_id"],
        "media_ids": [],
        "media_values": None,
        "summary_id": None,
        "chat_ids": None,
    }
    # chat_data.append(system_message)

    # Sort chat data by created_at

    latest_message["chat_data"] = chat_data
    latest_message.pop("chat_data_ids")
    for msg in latest_message["chat_data"]:
        if msg["role"] == "system" and msg["sender"] == "system":
            msg["chat_ids"].reverse()

    return latest_message


@router.post("/chat-messages/")
async def get_chat_message(
    chat_data_ids: list[str], current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Fetch the chat message for a given chat data ID.

    Args:
        chat_data_id (str): The ID of the chat data.

    Returns:
        dict: The chat message.
    """
    chat_data_col = current_user.db.chat_messages
    # Validate and convert IDs to ObjectId
    try:
        object_ids = [ObjectId(chat_data_id) for chat_data_id in chat_data_ids]
    except Exception as e:
        raise HTTPException(status_code=400, detail="Invalid chat_data_id in the list.")

    # Fetch and sort chat messages
    chat_messages_cursor = chat_data_col.find({"_id": {"$in": object_ids}}).sort(
        "created_at", ASCENDING
    )

    # Convert messages and return as a list
    chat_messages = [
        convert_objectid_to_str(message) for message in chat_messages_cursor
    ]

    return chat_messages


@router.get("/responses_summary_flow")
async def fetch_responses_summary_flow(
    customer_id: int,
    start_date: datetime = Query(default=datetime.now() - timedelta(days=7)),
    end_date: datetime = Query(default=datetime.now()),
    # no_of_summaries: int = Query(5, ge=1, le=10),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: UserTenantDB = Depends(get_tenant_info),
):

    # Calculate pagination offsets
    skip = (page - 1) * page_size
    # total_records = bot_responses_col.count_documents(query)
    total_records = len(
        list(
            current_user.db.ai_response.aggregate(
                [
                    {
                        "$match": {
                            "created_at": {"$gte": start_date, "$lt": end_date},
                            "request.user_id": customer_id,
                        }
                    },
                    {"$group": {"_id": "$_id"}},
                ]
            )
        )
    )

    pipeline = [
        # Match documents within the date range
        {
            "$match": {
                "created_at": {"$gte": start_date, "$lt": end_date},
                "request.user_id": customer_id,
            }
        },
        # Sort by created_at in descending order to get latest entries
        {"$sort": {"created_at": -1}},
        {"$skip": skip},
        # Limit to the specified number of summaries
        {"$limit": page_size},
        # Lookup the summary details from the summaries collection
        {
            "$lookup": {
                "from": "chat_messages",  # Name of the summaries collection
                "localField": "response.summary_id",  # Field in the current collection
                "foreignField": "_id",  # Field in the summaries collection
                "as": "summary_details",  # Alias for joined data
            }
        },
        # Project the required fields
        {
            "$project": {
                "contextualize": "$response.contextualize",  # Fetch contextualize
                "summary": {
                    "$arrayElemAt": ["$summary_details.content", 0]  # Fetch the summary
                },
                "identified_product": {
                    "$arrayElemAt": [
                        "$response.identified_product",
                        0,
                    ]  # Fetch the summary
                },  # Fetch identified product
                "primary_product": "$request.primary_product",  # Fetch primary product
                "categories": "$response.categories",  # Fetch categories
                "created_at": 1,
            }
        },
    ]

    # Query with pagination
    bot_responses = list(current_user.db.ai_response.aggregate(pipeline))

    # Prepare paginated response
    response = {
        "page": page,
        "page_size": page_size,
        "total_records": total_records,
        "total_pages": (total_records + page_size - 1) // page_size,  # Ceil division
        "data": convert_objectid_to_str(bot_responses),
    }

    try:
        return response
    except Exception as e:
        # loggers.error(f"Error fetching responses summary flow: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching responses summary flow {e}"
        )


@router.get("/get_tags")
async def get_tags(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Fetch distinct tags from the ai_response collection.

    Returns:
        list: A list of distinct tags.
    """
    bot_responses_col = current_user.db.ai_response

    try:
        # Use MongoDB's distinct method to find unique tags
        distinct_tags = bot_responses_col.distinct("request.channel")
        tags = [channel for channel in distinct_tags if channel != "Playground"]
        return {"tags": tags}
    except Exception as e:
        loggers.error(f"Error fetching distinct tags: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching distinct tags: {e}"
        )


@router.get("/get_call_to_action")
async def get_tags(current_user: UserTenantDB = Depends(get_tenant_info)):
    """
    Fetch distinct tags from the ai_response collection.

    Returns:
        list: A list of distinct tags.
    """
    bot_responses_col = current_user.db.ai_response

    try:
        # Use MongoDB's distinct method to find unique tags
        distinct_tags = bot_responses_col.distinct("response.call_to_action")
        # Filter out None values
        filtered_tags = [tag for tag in distinct_tags if tag is not None]
        return {"call_to_action": filtered_tags}
    except Exception as e:
        loggers.error(f"Error fetching distinct tags: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching distinct tags: {e}"
        )


@router.get("/saved_replies", response_model=List[SavedReply])
async def get_saved_replies(current_user: UserTenantDB = Depends(get_tenant_info)):
    saved_reply_col: Collection = current_user.db.saved_reply
    try:
        cursor = saved_reply_col.find()
        saved_replies = [SavedReply.from_mongo(reply) for reply in cursor]
        return saved_replies
    except Exception as e:
        loggers.error(f"Error fetching saved replies: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error fetching saved replies: {e}"
        )


@router.post("/save_reply", response_model=dict)
async def save_reply(
    request: SavedReply, current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        saved_reply, created = await SavedReply.get_or_create(
            current_user.db.saved_reply, request
        )

        return {
            "message": "Reply created" if created else "Reply updated",
            "id": saved_reply.id,
            "saved_reply": saved_reply,
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error saving reply: {str(e)}")


from pydantic import BaseModel


class MessageMedia(BaseModel):
    message: str
    media: list


@router.get("/get_message_media", response_model=MessageMedia)
async def get_message_media(
    message_id: str, current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        mesage_data = current_user.db.chat_messages.find_one(
            {"_id": ObjectId(message_id)}
        )

        return {"message": mesage_data["content"], "media": mesage_data["media_ids"]}
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error fetching message media: {e}"
        )
