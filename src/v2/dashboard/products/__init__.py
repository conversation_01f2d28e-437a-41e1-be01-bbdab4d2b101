from bson import ObjectId
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse
from src.helper import logger,convert_objectid_to_str
from src.models.user import UserTenantDB
from src.models.products import Products
from src.core.security import get_tenant_info
from typing import Dict, List

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Product"])

@router.post("/product/all")
async def get_all_products(search_filters: Dict | None =None,  current_user: UserTenantDB = Depends(get_tenant_info)):
    """ Get all categories
    Args:
        search_filters (Dict, optional): Search filters for the mongodb find. Defaults to {}.
    """
    if search_filters is None:
        search_filters = {}

    try:
        products_mongo = list(current_user.db.products.find(search_filters))
    except Exception as e:
        loggers.error(f"Error while fetching all categories: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
    if not products_mongo:
        return []
    products_mongo_ = convert_objectid_to_str(products_mongo)
    for i in  products_mongo_:
        i["_id"] = str(i["_id"])
    # print(f'{products_mongo_=}')
    return products_mongo_
    # try:
    #     products_ = [Products(**i) for i in  products_mongo_]
    #     print("Products",products_)
    #     return products_
    
    # except Exception as e:
    #     loggers.error(f"Error while creating categories: {e}")
    #     raise HTTPException(status_code=500, detail="Internal Server Error")

#endpoints to add the product
@router.post("/product/add")
async def add_product(product: Products, current_user: UserTenantDB = Depends(get_tenant_info)):
    """ Add a product
    Args:
        product (Products): Product to be added
    """
    print("here")
    try:
        current_user.db.products.insert_one(product.dict())
    except Exception as e:
        loggers.error(f"Error while adding product: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
    return {"message": "Product added successfully"}

#endpoints to update the products
@router.put("/product/update/{product_id}")
async def update_product(product_id:str, product: Products, current_user: UserTenantDB = Depends(get_tenant_info)):
    """ Update a product
    Args:
        product (Products): Product to be updated
    """
    try:
        product_update = current_user.db.products.update_one({"_id": ObjectId(product_id)}, {"$set": product.dict()})
        if product_update:
            return JSONResponse(status_code=200, content={"updated_count": product_update})
        return JSONResponse(status_code=402, content={"error": "Failed to update product"})
    except Exception as e:
        loggers.error(f"Error while updating product: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")

@router.delete("/product/delete/{product_id}")
async def delete_product(product_id:str, current_user: UserTenantDB = Depends(get_tenant_info)):
    """ Delete a product
    Args:
        product_id (str): Product id to be deleted
    """
    try:
        current_user.db.products.delete_one({"_id": ObjectId(product_id)})
    except Exception as e:
        loggers.error(f"Error while deleting product: {e}")
        raise HTTPException(status_code=500, detail="Internal Server Error")
    return {"message": "Product deleted successfully"}