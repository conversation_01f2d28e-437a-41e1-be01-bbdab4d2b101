from fastapi import <PERSON><PERSON><PERSON><PERSON>,Depends,HTTPException
from fastapi.responses import JSONResponse
from src.models.evaluate import Eval_Request,Fetch_Evaluations,Update_Evaluation,Flag_Evaluations
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
from src.core.security import get_tenant_info
from bson import ObjectId
from functools import lru_cache
from collections.abc import Iterable
from datetime import datetime
loggers = setup_new_logging(__name__)

router = APIRouter(tags=["evaluate"])
 

@router.post("/evaluate")
async def evaluate_response(response: Eval_Request, current_user: UserTenantDB = Depends(get_tenant_info)):
    try:    
        client = current_user.async_db
        if response.evaluation == "like":
            response.resolved = True
        # Find AI response and validate
        try:
            ai_response = await client.ai_response.find_one({"_id": ObjectId(response.message_id)})
            if not ai_response:
                
                raise HTTPException(status_code=404, detail="AI response not found")
        except Exception as e:
            loggers.error(f"Error finding AI response: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Invalid message_id format: {str(e)}")
        
        # Extract AI response data with proper error handling
        response_data = ai_response.get("response", {})
        if not response_data:
            raise HTTPException(status_code=400, detail="Response data is missing")
            
        # Extract all required fields with defaults
        evaluation_data = {
            "username":ai_response.get("request",{}).get("user_name",""),
            "message":response_data.get("latest_message",""),
            "reply":response_data.get("reply",""),
            "products": response_data.get("identified_product", ""),
            "language": response_data.get("language", ""),
            "sentiment": response_data.get("sentiment", ""),
            "personality": response_data.get("personality", ""),
            "call_to_action": response_data.get("call_to_action", []),
            "current_spin_state": response_data.get("current_spin_state", ""),
            "resolved": response.resolved
        }
        
        # Convert request to dict and update with AI response data
        eval_data = response.model_dump_mongo()
        eval_data.update(evaluation_data)
        
        # Insert evaluation
        result = await client.evaluations.insert_one(eval_data)
        
        if not result.inserted_id:
            raise HTTPException(status_code=500, detail="Failed to insert evaluation")
        try:
            # Check if response field exists, if not add it
            update_query = {
                "$set": {
                    "response.evaluation_id": str(result.inserted_id),
                    "response.evaluation_status": response.evaluation
                }
            }
            
         
            
            # Update evaluation fields
            await client.ai_response.update_one(
                {"_id": ObjectId(response.message_id)},
                update_query
            )
        except Exception as e:
            loggers.error(f"Error updating evaluation: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to update evaluation: {str(e)}")
        return JSONResponse(
            status_code=200,
            content={
                "message": "Evaluation stored successfully",
                "evaluation_id": str(result.inserted_id)
            }
        )
    except HTTPException:
        loggers.error(f"Error storing evaluation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    except Exception as e:
        loggers.error(f"Error storing evaluation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# @lru_cache(maxsize=1000,typed=True)
@router.post("/fetch_evaluations")
async def fetch_evaluations(
    fetch_evaluations: Fetch_Evaluations, 
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        client = current_user.async_db
        
        # Build filter query
        filter_query = Fetch_Evaluations.creat_filter_match(fetch_evaluations)
        print(filter_query)
        # Calculate skip for pagination
        skip = (fetch_evaluations.page - 1) * fetch_evaluations.limit
        
        # Execute query with pagination
        cursor = client.evaluations.find(filter_query).sort("created_at",-1)
        
        # Get total count for pagination
        total_count = await client.evaluations.count_documents(filter_query)
        
        #_id is object id make it string
        data = await cursor.skip(skip).limit(fetch_evaluations.limit).to_list(length=fetch_evaluations.limit)
        for item in data:
            item["_id"] = str(item["_id"])
            if item.get("created_at"):
                item["created_at"] = item["created_at"].isoformat()
            if item.get("resolved_at"):
                item["resolved_at"] = item["resolved_at"].isoformat()
            
        #sort by created_at descending
        data.sort(key=lambda x: x["created_at"], reverse=True)
        
        return JSONResponse(
            status_code=200, 
            content={
                "data": data,
                "meta": {
                    "page": fetch_evaluations.page,
                    "limit": fetch_evaluations.limit,
                    "total_count": total_count,
                    "total_pages": (total_count // fetch_evaluations.limit) + (1 if total_count % fetch_evaluations.limit else 0)
                }
            }
        )
        
    except Exception as e:
        loggers.error(f"Error fetching evaluations: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


from typing import List, Dict
from fastapi import HTTPException, Depends  
from pydantic import BaseModel, Field, field_validator
from bson import ObjectId
from functools import lru_cache

class Eval_Filters(BaseModel):
    categories: List[str]
    products: List[str]
    sentiment: List[str]
    personality: List[str]
    call_to_action: List[str]
    current_spin_state: List[str]
    language: List[str]
    reviewers: List[Dict]

    @field_validator(
        "categories", "products", "sentiment", "personality", 
        "call_to_action", "current_spin_state", "language", "reviewers", 
        mode="before"
    )
    @classmethod
    def ensure_list(cls, v):
        if isinstance(v, list):
            return [i for i in v if i]  # Filter out empty strings and None values
        return v


async def unnest_list(data: list):
    stack = [data]
    result = []
    
    while stack:
        current = stack.pop()
        if isinstance(current, Iterable) and not isinstance(current, (str, bytes)):
            for item in reversed(current):  # Reverse to maintain order
                if item not in result:  # Check for duplicates
                    stack.append(item)
        else:
            if current not in result:  # Check for duplicates
                result.append(current)
    
    return result
@lru_cache(maxsize=1000, typed=True)
@router.get("/evaluate_filters", response_model=Eval_Filters)
async def evaluate_filters(
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        client = current_user.async_db

        # Aggregation pipeline to fetch unique values for each field
        pipeline = [
            {
                "$facet": {
                    "categories": [{"$group": {"_id": None, "unique": {"$addToSet": "$categories"}}}],
                    "products": [{"$group": {"_id": None, "unique": {"$addToSet": "$products"}}}],
                    "sentiment": [{"$group": {"_id": None, "unique": {"$addToSet": "$sentiment"}}}],
                    "personality": [{"$group": {"_id": None, "unique": {"$addToSet": "$personality"}}}],
                    "call_to_action": [{"$group": {"_id": None, "unique": {"$addToSet": "$call_to_action"}}}],
                    "current_spin_state": [{"$group": {"_id": None, "unique": {"$addToSet": "$current_spin_state"}}}],
                    "language": [{"$group": {"_id": None, "unique": {"$addToSet": "$language"}}}],
                    "reviewer_ids": [{"$group": {"_id": None, "unique": {"$addToSet": "$reviewer_id"}}}]
                }
            },
            {
                "$project": {
                    "categories": {"$reduce": {"input": "$categories.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "products": {"$reduce": {"input": "$products.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "sentiment": {"$reduce": {"input": "$sentiment.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "personality": {"$reduce": {"input": "$personality.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "call_to_action": {"$reduce": {"input": "$call_to_action.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "current_spin_state": {"$reduce": {"input": "$current_spin_state.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "language": {"$reduce": {"input": "$language.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}},
                    "reviewer_ids": {"$reduce": {"input": "$reviewer_ids.unique", "initialValue": [], "in": {"$concatArrays": ["$$value", "$$this"]}}}
                }
            },
            {
                "$project": {
                    "categories": {"$setDifference": ["$categories", [None, ""]]},
                    "products": {"$setDifference": ["$products", [None, ""]]},
                    "sentiment": {"$setDifference": ["$sentiment", [None, ""]]},
                    "personality": {"$setDifference": ["$personality", [None, ""]]},
                    "call_to_action": {"$setDifference": ["$call_to_action", [None, ""]]},
                    "current_spin_state": {"$setDifference": ["$current_spin_state", [None, ""]]},
                    "language": {"$setDifference": ["$language", [None, ""]]},
                    "reviewer_ids": {"$setDifference": ["$reviewer_ids", [None]]}
                }
            }
        ]

        # Execute the aggregation pipeline
        result = await client.evaluations.aggregate(pipeline)
        result = await result.to_list(length=None)
  

        if not result:
            raise ValueError("No data found in evaluations collection")

        # Extract unique values from the result
        unique_values = {
            "categories": await unnest_list(result[0]["categories"]),
            "products": await unnest_list(result[0]["products"]),
            "sentiment": await unnest_list(result[0]["sentiment"]),
            "personality": await unnest_list(result[0]["personality"]),
            "call_to_action": await unnest_list(result[0]["call_to_action"]),
            "current_spin_state": await unnest_list(result[0]["current_spin_state"]),
            "language": await unnest_list(result[0]["language"]),
        }

        # Fetch reviewer details
        reviewer_ids = await unnest_list(result[0]["reviewer_ids"])
        reviewers = await client.users.find(
            {"_id": {"$in": [ObjectId(reviewer_id) for reviewer_id in reviewer_ids]}},
            {"username": 1}
        ).to_list(length=None)

        unique_values["reviewers"] = [
            {"user_name": reviewer["username"], "_id": str(reviewer["_id"])}
            for reviewer in reviewers
        ]

        return unique_values

    except Exception as e:
        loggers.error(f"Error fetching unique values: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
    
    
    
    
    
@router.delete("/evaluate")
async def delete_evaluation(
    update_evaluation: Update_Evaluation,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        client = current_user.async_db
        #update ai_response
        evaluation_id = await client.ai_response.find_one({"_id": ObjectId(update_evaluation.message_id)})
        evaluation_id = evaluation_id.get("response",{}).get("evaluation_id",None)
        if not evaluation_id:
            raise HTTPException(status_code=404, detail="Evaluation not found")
        await client.ai_response.update_one(
            {"_id": ObjectId(update_evaluation.message_id)},
            {"$unset": {"response.evaluation_id": "",
                        "response.evaluation_status": ""}}
        )
        result = await client.evaluations.delete_one({"_id": ObjectId(evaluation_id)})
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Evaluation not found")
        return JSONResponse(
            status_code=200,
            content={"message": "Evaluation deleted successfully"}
        )
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error deleting evaluation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")




@router.post("/flag_evaluations")
async def flag_evaluations(
    request:Flag_Evaluations,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    try:
        client = current_user.async_db
        
        result = await client.evaluations.update_one(
            {"_id": ObjectId(request.evaluation_id)},
            {"$set": {
                "resolved": request.resolved,
                "resolved_by": request.resolved_id,
                "resolved_at": datetime.now()
            }}
        )
        
        return JSONResponse(status_code=200,content="Evaluation Flagged Successfully")
    except Exception as e:
        loggers.error(f"Error flagging evaluation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        