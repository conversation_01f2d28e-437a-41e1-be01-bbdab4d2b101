"""
API Documentation and Health Check Routes
Provides endpoints for API documentation and system health monitoring
"""

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, JSONResponse
import json

router = APIRouter(tags=["API Documentation"])

@router.get("/api-info", response_class=JSONResponse)
async def api_info():
    """
    Get API information and available endpoints.
    This endpoint provides an overview of the API structure and key endpoints.
    """
    return {
        "api_name": "Eko Backend API",
        "version": "0.1.0",
        "description": "AI-powered backend for document processing and chat functionality",
        "key_endpoints": {
            "document_processing": {
                "process_documents": "/process-documents",
                "setup_files_websocket": "/setup_files",
                "check_status": "/check_status",
                "add_documents": "/add-documents",
                "delete_documents": "/delete-documents"
            },
            "chat_and_response": {
                "ask_guru": "/ask_guru/*",
                "respond": "/respond/*",
                "reply": "/reply/*"
            },
            "dashboard": {
                "users": "/users/*",
                "customers": "/customers/*",
                "overview": "/overview/*"
            },
            "health": {
                "health_check": "/health",
                "api_info": "/api-info"
            }
        },
        "document_processing_flow": {
            "step_1": "Connect to WebSocket at /setup_files with token parameter",
            "step_2": "Call /process-documents endpoint with files or URLs",
            "step_3": "Receive real-time status updates via WebSocket",
            "note": "Sticky sessions ensure same instance handles both WebSocket and processing"
        },
        "ports": {
            "main_api": "8000 (internal)",
            "api_docs": "8201 (external)",
            "traefik_dashboard": "8202 (external)",
            "https": "443",
            "http": "80"
        }
    }

@router.get("/flow-diagram", response_class=HTMLResponse)
async def flow_diagram():
    """
    Display a visual diagram of the document processing flow.
    """
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Eko API - Document Processing Flow</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .flow-step { background: #e3f2fd; padding: 20px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #2196f3; }
            .step-number { background: #2196f3; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 15px; }
            .endpoint { background: #f3e5f5; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
            .websocket { background: #e8f5e8; padding: 10px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
            .sticky-session { background: #fff3e0; padding: 15px; border-radius: 5px; border: 2px solid #ff9800; margin: 20px 0; }
            h1 { color: #1976d2; text-align: center; }
            h2 { color: #424242; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Eko API - Document Processing Flow</h1>
            
            <h2>📋 Complete Processing Flow</h2>
            
            <div class="flow-step">
                <span class="step-number">1</span>
                <strong>WebSocket Connection</strong>
                <div class="websocket">WebSocket: /setup_files?token=YOUR_TOKEN</div>
                <p>Client establishes WebSocket connection for real-time status updates. Token is required for authentication.</p>
            </div>
            
            <div class="flow-step">
                <span class="step-number">2</span>
                <strong>Document Processing Request</strong>
                <div class="endpoint">POST: /process-documents</div>
                <p>Client sends files or URLs for processing. This endpoint validates input and starts the processing pipeline.</p>
            </div>
            
            <div class="flow-step">
                <span class="step-number">3</span>
                <strong>Real-time Status Updates</strong>
                <p>Processing status is sent via WebSocket in real-time:</p>
                <ul>
                    <li>📤 Uploading Files: In Progress → Completed</li>
                    <li>📝 Extracting Text: In Progress → Completed</li>
                    <li>🧠 Setting up Knowledge Base: In Progress → Completed</li>
                    <li>📚 Generating Table of Content: In Progress → Completed</li>
                    <li>💾 Setting up Database: In Progress → Completed</li>
                    <li>✅ Finishing Up: Completed</li>
                </ul>
            </div>
            
            <div class="sticky-session">
                <strong>🔒 Sticky Sessions Guarantee</strong>
                <p>Traefik load balancer ensures that both the WebSocket connection and the document processing request are handled by the <strong>same application instance</strong>. This is critical for maintaining state consistency and real-time communication.</p>
                <p><strong>Cookie:</strong> eko-session (1 hour duration)</p>
            </div>
            
            <h2>🌐 Port Configuration</h2>
            <ul>
                <li><strong>Port 8201:</strong> API Documentation & General Access</li>
                <li><strong>Port 8202:</strong> Traefik Dashboard</li>
                <li><strong>Port 443:</strong> HTTPS Main API</li>
                <li><strong>Port 80:</strong> HTTP (redirects to HTTPS)</li>
            </ul>
            
            <h2>🔧 Additional Endpoints</h2>
            <div class="endpoint">GET: /check_status - Check WebSocket connection status</div>
            <div class="endpoint">POST: /add-documents - Add documents via JSON</div>
            <div class="endpoint">DELETE: /delete-documents - Remove documents</div>
            <div class="endpoint">GET: /health - Health check for load balancer</div>
            
            <p style="text-align: center; margin-top: 40px; color: #666;">
                <strong>Eko Backend API v0.1.0</strong> | Powered by FastAPI & Traefik
            </p>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@router.get("/health-detailed", response_class=JSONResponse)
async def health_detailed():
    """
    Detailed health check with system information.
    """
    return {
        "status": "healthy",
        "service": "eko-backend",
        "version": "0.1.0",
        "components": {
            "api": "operational",
            "websocket": "operational",
            "document_processing": "operational",
            "database": "operational",
            "minio": "operational"
        },
        "ports": {
            "internal": 8000,
            "api_docs": 8201,
            "traefik_dashboard": 8202
        },
        "features": {
            "sticky_sessions": "enabled",
            "websocket_support": "enabled",
            "document_processing": "enabled",
            "real_time_updates": "enabled"
        }
    }
