from typing import List
from llama_index.llms.openai import OpenAI



def contextualize_messages(current_user, message: str, chat_hist: List[str]) -> str:

    if current_user.db.settings.find_one({"name": "env","contextualize":False}):
        return message
    
    prompt=current_user.db["prompt"].find_one({"name":"Contextualize"})["text"]
    model=current_user.db["prompt"].find_one({"name":"Contextualize"})["model"]
    
    try:
        llm = OpenAI(model=model)
        return llm.complete(
            prompt.format(message=message, chat_hist=chat_hist),
            formatted=True
        ).text
    except Exception as e:
        raise e
    



def convert_to_BaseMessages(chat):

    def concatnate_chat(data) -> str:
        content = ""
        for i in data:
            content += i.content + ". "
        return content

    messages = []
    for chat_data in chat:
        if chat_data.role == "user":
            messages.append(HumanMessage(content=concatnate_chat(chat_data.data)))
        else:
            messages.append(AIMessage(content=concatnate_chat(chat_data.data)))
    return messages
