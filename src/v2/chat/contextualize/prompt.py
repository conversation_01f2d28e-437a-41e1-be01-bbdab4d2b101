    question: {message}
    
    chat_history: {chat_hist}

    For a given Input, which may be in Nepali, generate a detailed contextual summary of the conversation, focusing mostly on the customer's final 'question'. The conversation will be related to courses, classes, and fall under sales, marketing, or support for academic courses for an EdTech company.  

    When the Input is about requesting or providing reading material and/or books, mention such things as 'study material' in the Output.  When the Input is about booking, registering, or enrolling for a service, mention such things as 'reservation' in the Output. Put more attention to Input where user mentioned if they have already made payment or asking information related to how to make payment, they are distinct cases and should be treated as such.


    Possible Course names: [cee, see, Teaching License, Teacher Service Commission, Teaching Service Commission, ASI, Loksewa, Bini, Na<PERSON>, K<PERSON><PERSON>, <PERSON><PERSON><PERSON>ha, <PERSON> bi, <PERSON> bi, mavi, nimavi, ni mavi, ni ma b, ma b]. 


    Example:
    ```
    Example Input 1: 
    question: "Ke Tapai lai yo course ko fee ko barema jannu cha?"
    response: <positive response>

    Example Output 1: 
    "The user is responding with confirmation to learn more about Fee of the course."


    Example Input 2:
    question: "Payment garera chadai class linu hola".
    response: <positive response>

    Example Output 2:
    "The user is responding with confirmation to payment process."
    ```

    Important: Only contextualize from given Input. Do not Assume anything beyond provided Input. 
    Important: DO NOT hallucinate.
    Important: Provide details on greetings used in any.
    Important: If the Input contains word 'written' and does not include word 'exam', consider it as written form of syllabus or curriculum.
    Important: Mention 'acknowledgement' in the Output, if the question is only acknowledging to the previous message.
    Important: There is difference between 'acknowledgement' and 'agreement'. 'acknowledgement' is for those that is simply acknowledging the previous message. Agreement is if the Input has agreement to the question or intent mentioned in previous message. 'Umm', 'aah', 'ah', 'hmm' in the Input refers to 'agreement'.
    Important: Mention 'confirming' in the Output, if the question is only affirming or confirming or agreement to  the previous message.
    Important: Mention 'inquiry' in the Output, if the question is making and inquiry or agreement to the intent of inquiry. Make sure the if the question is related to inquiry or intent to understand or learn or find information.
    Important: Pay additional attention to if user is responding with already paid or joined the class or planning to or interested in joining the class. Mention that difference clearly.
    Important: 'hajur', 'hjr', 'hajr' is not greeting, its more like affirmation. 

    Important: Begin output sentence with "the user is responding".
