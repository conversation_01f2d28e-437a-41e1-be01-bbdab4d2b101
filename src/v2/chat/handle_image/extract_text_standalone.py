import base64
import os
import google.generativeai as genai
from google.generativeai import types
import os,shutil
import PIL
import json
from io import BytesIO
import requests



# from app.core.config import GOOGLE_API_KEY

# genai.configure(api_key=GOOGLE_API_KEY)

def gemini_model(system_instructions=None, model_name=None):
    # Create the model

    if model_name is None:
        model_name = "gemini-1.5-flash"

    if system_instructions is None:
        system_instructions = "Extract the information from the image."

    generation_config = {
        "temperature": 1,
        "top_p": 0.95,
        "top_k": 64,
        "max_output_tokens": 8192,
        "response_mime_type": "application/json",
    }

    model = genai.GenerativeModel(
        model_name=model_name,
        generation_config=generation_config,
        system_instruction=system_instructions,
    )

    return model

async def extract_text(current_user, image_urls:list):

    CREDENTIALS = current_user.db.settings.find_one({"name":"env"}, {
            "config.GOOGLE_API_KEY":1,
        }).get("config")
    GOOGLE_API_KEY = CREDENTIALS.get("GOOGLE_API_KEY")
    genai.configure(api_key=GOOGLE_API_KEY)
    
    resp_ = current_user.db.prompt.find_one({"name":"extract_text_from_image"})
    SYS_PROMPT = resp_.get("text")

    work = [SYS_PROMPT, PIL.Image.open(BytesIO(requests.get((image_urls[0])).content))]
    model = gemini_model(system_instructions=SYS_PROMPT, model_name=resp_.get("model"))

    result = model.generate_content(work)


    return result.text, image_urls,{"prompt_tokens":result.usage_metadata.prompt_token_count,"completion_tokens":result.usage_metadata.candidates_token_count,"model":"gemini-2.0"}
