import datetime
import openai
from typing import List
import retry
import requests
import os 
from src.helper import get_DB
from src.helper.logger import setup_new_logging
from dotenv import load_dotenv
load_dotenv()

logger = setup_new_logging(__name__)

class AromaCall:
    def __init__(self, image_urls:List[str]):
        # self.image =  open(requests.get(image_url).content, "rb")

        from urllib.parse import urlparse, urlunparse

        def get_base_url(full_url):
            parsed_url = urlparse(full_url)
            parsed_url = urlunparse(parsed_url._replace(query=""))
            return parsed_url

        image_urls=[get_base_url(url) for url in image_urls]
        self.FOLDER_NAME = "images/"
        os.makedirs(self.FOLDER_NAME, exist_ok=True)

        images_ = []
        for image_url in image_urls:

            print("Image url",image_url)
            image_name = self.FOLDER_NAME +image_url.split("/")[-1]

            with open (image_name, "wb") as f:
                f.write(requests.get(image_url).content)

            images_.append(('files',(image_name, open(image_name, "rb"), f'image/{image_name.split(".")[-1]}')))

        self.images = images_
        self.TOKEN = None
        self.URL = os.getenv("AROMA_BACKEND_URL")
        self.project_id = None

    def cleanup(self):
        try:
            for image in self.images:
                os.remove(image[1][0])
        except Exception as e:
            print("Error Cleaning up", e)

    def login(self, CREDENTIALS:dict) -> str | None:
        """Returns the project id"""
        AROMA_TENANT_USERNAME = CREDENTIALS.get("AROMA_TENANT_USERNAME")
        AROMA_TENANT_PASSWORD = CREDENTIALS.get("AROMA_TENANT_PASSWORD")
        AROMA_TENANT_SLUG = CREDENTIALS.get("AROMA_TENANT_SLUG")
        response = requests.post(
            url=f"{self.URL}/login",
            data={
                "username": AROMA_TENANT_USERNAME,
                "password": AROMA_TENANT_PASSWORD,
                "client_id": AROMA_TENANT_SLUG,
            },
        )
        print("LOGIN", response.json())
        TOKEN = response.json().get("access_token","")
        self.TOKEN = TOKEN

        HEADERS = {
            "Authorization": "Bearer " + TOKEN
        }
        self.HEADERS = HEADERS
        self.project_id = CREDENTIALS.get("AROMA_BASE_PROJECT_ID", None)

    def _get_project_id(self):

        if self.project_id:
            return self.project_id

        project_create_resp = requests.post(url=f"{self.URL}/projects/create", json = {
            "name": "AskGuruMessages",
            # "type": "Book/Notes",
            "description": "Askguru acedemic messages",
        }, headers=self.HEADERS)

        project_id = project_create_resp.json()["_id"]
        print("PROJECT CREATE" ,project_id, type(project_id))

        self.project_id = project_id
        return project_id 

    @retry.retry(tries=5, delay=1)
    def _get_job_status(self, job_id) -> dict:
        response = requests.get(url=f"{self.URL}/job/{job_id}", headers=self.HEADERS)
        return response.json()

    def create_and_run_job(self, current_user=None):

        headers = {
            'accept': 'application/json',
            'Authorization': 'Bearer '+ self.TOKEN,
        }

        if not current_user:
            db = get_DB("eko_ask")
        else:
            db = current_user.db

        extract_prompt = db.prompt.find_one({"name":"extract_text_from_image"})
        desc = extract_prompt["text"]
        columns = extract_prompt.get("extra",{}).get("columns", "entire_text")
        type_ = extract_prompt.get("extra",{}).get("type", "Book/Notes")

        files = [
            ('name', (None, datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))),
            ('description',(None, desc)),
            ('columns', (None, columns)),
            ('type', (None, type_)),
        ]
        files.extend(self.images)

        print("Initial Files", files)
        response = requests.post(
            f'{self.URL}/jobs/create-with-images/{self._get_project_id()}',
            headers=headers,
            files=files,
        )
        print("Job Create Response", response.json())
        image_path = response.json()["job"]["image_paths"]
        print()

        job_id = response.json()["job"]["_id"]
        response = requests.post(url=f"{self.URL}/jobs/{job_id}/action", headers=self.HEADERS)

        print("Job Action Response", response.json())
        print()


        resp = {"status":"in-progress"}
        while resp["status"] != "completed":
            print("In Progress. . ")
            response = self._get_job_status(job_id)
            resp = {"status":response["status"]}

        job_output = response["job_output"]
        self.job_output = job_output
        return job_output, image_path

    def job_output_formatted(self):
        data_ = []  # Initialize as a list, not a string
        print("Job Output", self.job_output)
        try:
            for idx, img_data in enumerate(self.job_output):
                if 'data' in img_data:  # Ensure the 'data' key exists
                    for item in img_data['data']:
                        if isinstance(item, str):
                            data_.append(f"image_{idx}: {item}")
                        else:
                            img_info = [f"{key}: {value}" for key, value in item.items()]
                            data_.append(f"image_{idx}: {', '.join(img_info)}")
        except Exception as e:
            print("Error", e)
            data_=str(self.job_output)
        formatted_output = "\n".join(data_)  # Join the list to form a single string with line breaks
        print("Job Output", formatted_output)
        return formatted_output

    def get_input_hosted_images(self):
        image_paths = get_DB("fsem_db").jobs.find_one({"project_id":str(self._get_project_id())}).get("image_paths")
        urls = [f"https://aroma.nextai.asia/api/{image_path}" for image_path in image_paths]
        return urls 

def call_openai(sys_prompt):
    response = openai.OpenAI().chat.completions.create(
        messages=[
            {"role": "system", "content": sys_prompt},
        ],
        model="gpt-4o-mini",

    )
    return response.choices[0].message.content

async def extract_text(current_user, image_urls:list):
    # print("**Generate Academic Reply**")
    aroma = AromaCall(image_urls)
    # print("Current User", current_user)

    try:
        CREDENTIALS = current_user.db.settings.find_one({"name":"env"}, {
            "config.AROMA_TENANT_USERNAME":1,
            "config.AROMA_TENANT_PASSWORD":1,
            "config.AROMA_TENANT_SLUG":1,
            "config.AROMA_BASE_PROJECT_ID":1
        }).get("config")

        aroma.login(CREDENTIALS)
    except Exception as e:
        raise Exception("Error Logging in", e)


    if aroma.TOKEN is None:
        return None, []

    # aroma.project_id = "67359133eadb882d35f5e26e" # from fsemdb
    # aroma.project_id = "67a485aeab1971baa7c3e3fe" # from AG_aroma 
    aroma.project_id = CREDENTIALS.get("AROMA_BASE_PROJECT_ID")
    job_output ,images_paths = aroma.create_and_run_job(current_user=current_user) 
    # job_images = aroma.get_input_hosted_images()
    # logger.log_list_of_dicts("Job Output", job_output)
    text = aroma.job_output_formatted()
    print(f"Job Output", text[:10])
    aroma.cleanup()
    print("**End Generate Academic Reply**")
    images_paths = [f"https://aroma.nextai.asia/api/{image_path}" for image_path in images_paths]
    print(f"{images_paths}")

    # raise Exception("Error")

    return text, images_paths
