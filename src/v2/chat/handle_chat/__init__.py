from src.helper import get_DB
from src.models.chat_history import ChatHistMessage
from src.helper.logger import setup_new_logging

from typing import Tuple, List
from bson.objectid import ObjectId
from openai import OpenAI
from datetime import datetime
from llama_index.core.base.llms.types import ChatMessage
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.llms.openai import OpenAI
from llama_index.core import PromptTemplate
import tiktoken
import re

llm=OpenAI(model="gpt-4o-mini")

loggers = setup_new_logging(__name__)

def summarize_chat(current_user, chat_history):
    """
    Summarizes the provided chat history using OpenAI's model.
    """
    try:
        # loggers.info(f"Summarizing chat history for ...")
#         prompt=f"""
#     The following is a conversation between the user and assistant.

#     Summarize the entire conversation between the customer and the support agent in a serial manner, focusing only on the actual content of the discussion without making assumptions about the customer's intentions or perspective.

#     - Clearly specify what the customer has asked or stated and what the support agent has replied or explained in descriptive manner.
#     - Use the words "customer" and "support agent" explicitly in the summary.
#     - Ensure accurate attribution of queries and responses to either the customer or the support agent.
#     - Summarize all the chat_history without missing the context of the conversation.

#     Output:
# """

        prompt=f"""
        Given the Conversation generate a summary of chat history. Do not assume anything from customer's point of view. Simply summarize the given conversation.

        In given conversation customer has raised a question for which support agent has answered. Provide information on what customer has asked and the answer of support agent. 

        Always mention the word 'customer' and 'support agent' in your final output. 

        Make sure you correctly determine who is asking or responding to what between Customer and Support Agent.
        

        """
        model = current_user.db["prompt"].find_one({"name": "summary_prompt"})["model"]
        # prompt = current_user.db["prompt"].find_one({"name": "summary_prompt"})["prompt"]
        summarizer_llm = OpenAI(model_name=model, max_tokens=256,temperature=0.0)
        tokenizer_fn = tiktoken.encoding_for_model(model).encode
        # from pprint import pprint as print
        if len(chat_history) == 1:
            start_=datetime.now()
            summarize_prompt = f"Summarize the incoming conversation between the user and assistant, ensuring the summary is concise and highlights the key topics discussed. The incoming user message is: ```{chat_history[0].content}```"
            response = llm.complete(summarize_prompt)
            end_=datetime.now()
            loggers.info(f"Summarizing chat history took {(end_-start_).total_seconds()} seconds")
            return response.text
            # memory = ChatSummaryMemoryBuffer.from_defaults(
            # chat_history=chat_history[:-1] if len(chat_history) > 1 else chat_history)
        else:
            start_=datetime.now()
            memory = ChatSummaryMemoryBuffer.from_defaults(
                chat_history=chat_history[:-1],
                llm=summarizer_llm,
                token_limit=1,
                summarize_prompt="""
                Summarize the conversation between the user and the assistant, ensuring the summary is concise and highlights the key topics discussed. Include all products mentioned during the conversation, retaining their original names (e.g., Kharidar, Bini, Nayab Subba, etc.) without translating them into English.

                Avoid surrounding romanized words in quotation marks. Rather, provide either a smooth paraphrase or the closest English meaning based on context.

                Where possible, interpret or paraphrase colloquial terms instead of quoting them directly.
                """,
                tokenizer_fn=tokenizer_fn,
            )
            end_=datetime.now()
            loggers.info(f"Summarizing chat history took {(end_-start_).total_seconds()} seconds")
        loggers.info(f"Summarized chat history..")

        return memory.get()[0]
    except Exception as e:
        loggers.error(f"Error summarizing chat history: {e}")
        raise e

def check_existing_summary(current_user, chat_history: List[ChatHistMessage]) -> Tuple[List[ChatHistMessage], ChatHistMessage]:
    """
    Checks for existing summaries and retrieves unsaved chats.

    Args:
        chat_history (list): List of ChatHistMessage objects representing chat history.

    Returns:
        tuple: A list of unsaved chats and the existing summary if found.
    """
    unsaved_chats = []
    loggers.debug(f"Checking summary ")
    for chat in reversed(chat_history):  # Process chats in reverse order
        existing_chat = current_user.db["chat_messages"].find_one({"_id": ObjectId(chat.id)})
        if existing_chat and existing_chat.get("summary_id"):
            loggers.info(f"Found existing summary for chat ID: {chat.id}")
            existing_summary=current_user.db["chat_messages"].find_one({"_id": ObjectId(existing_chat["summary_id"])})
            existing_summary = ChatHistMessage(
                role=existing_summary.get("role"),
                sender=existing_summary.get("sender"),
                content=existing_summary.get("content"),
                created_at=existing_summary.get("created_at"),
                user_id=existing_summary.get("user_id"),
                id=existing_summary.get("_id"),
                chat_ids=[str(id) for id in existing_summary.get("chat_ids")],
            )
            if not unsaved_chats:
                loggers.info("Found existing summary, no unsaved chats.")
                return [], existing_summary
            return unsaved_chats[::-1], existing_summary
        else:
            unsaved_chats.append(chat)
    loggers.info("No existing summary found.")
    return unsaved_chats[::-1], None


def create_summary_of_new_chats(current_user, unsaved_chats, chat_raw_hist, user_id, user, existing_summary) -> ChatHistMessage:
    """
    Handles the generation of new summaries and updates the database.

    Args:
        unsaved_chats (list): List of unsaved chats.
        chat_raw_hist (list): List of all chat messages.
        user_id (str): ID of the user.
        user (dict): User data.
        existing_summary (dict): Existing summary data.

    Returns:
        tuple: The generated summary content and the formatted summary object.
    """
    # print(f'{unsaved_chats=}')
    if unsaved_chats:
        loggers.info(f"New chat history found for user ID: {user_id}")
        unsaved_chats.reverse()  # Reverse for chronological order

        chat_ids = [chat.id for chat in unsaved_chats]
        if existing_summary:
            chat_ids.append(str(existing_summary.id))

        # Generate a summary
        generated_summary = summarize_chat(current_user, [chat for chat in chat_raw_hist if chat.role != "system" and chat.content])
        if isinstance(generated_summary, ChatMessage):
            summary_content = generated_summary.content
        else:
            summary_content = generated_summary
            
        try:
            formatted_summary = ChatHistMessage(role="system", content=summary_content,id=None ,user_id=user_id, chat_ids=chat_ids,sender="system",created_at=datetime.now())
        except Exception as e:
            loggers.error(f"Error formatting summary: {e}")
        # Replace or add summary
        if existing_summary:
            # Add summary_id of old summary to new summary
            formatted_summary.summary_id = existing_summary.id

        # Insert the new summary into the database
        summary_data = formatted_summary.model_dump_mongo()
        current_user.db["chat_messages"].insert_one(summary_data)
        
        # Update chats with summary_id
        loggers.debug(f"Inserting chats ")
        for chat in chat_raw_hist:
            chat.summary_id = formatted_summary.id
            existing_chat = current_user.db["chat_messages"].find_one({"_id": ObjectId(chat.id)})
            if not existing_chat:
                insert = current_user.db["chat_messages"].insert_one(chat.model_dump_mongo())
                # print(insert.inserted_id)
            else:
                loggers.debug(f"Updating chat ")
                # Update existing chat messages with the new summary_id
                current_user.db["chat_messages"].update_one(
                    {"_id": ObjectId(chat.id)},
                    {"$set": {"summary_id": formatted_summary.id}}
                )

        # Update bot response
        if user:
            user["response"]["summary_id"] = formatted_summary.id
            user["response"]["summary"] = formatted_summary.content

            current_user.db["bot_responses"].insert_one(user)

        return formatted_summary
    else:
        # same here
        loggers.info(f"No new chat history found for user ID: {user_id}")
        return "No new chat history to summarize.", None



def contextualize_chathistmessages(current_user, summary: ChatHistMessage, user_message: ChatHistMessage):
    """ Create  contextualized sentence from previous summary and present user message"""
    loggers.info("Contetualizing")
    try:
        llm=OpenAI(model=current_user.db["prompt"].find_one({"name":"Contextualize"})["model"])
        qa_prompt_tmpl_str = current_user.db["prompt"].find_one({"name":"Contextualize"})["prompt"]
        template_var_mappings = {"context_str": "chat_summary", "query_str": "question"}
        prompt_tmpl = PromptTemplate(
            qa_prompt_tmpl_str, template_var_mappings=template_var_mappings
        )
        fmt_prompt = prompt_tmpl.format(
            context_str=summary.content,
            query_str=user_message.content,
        )
        output=llm.complete(fmt_prompt,formatted=True)
        clean_text = re.sub(r'^[^:]*:\s*', '', output.text).strip()
        loggers.debug(f"Contetualized cleaned :")
        return clean_text
    except Exception as e:
        #error aayo bhane propagate garni last samma purauna paryo error haru ani aggregate garera handle garni
        # yesto return garna hunna k ani contextualize bhanera `Error contetualizing response` return garne?
        loggers.error(f"Error contetualizing response: {e}")
        return "Error contetualizing response"


