import os
import json
from src.models.products import Products
from bson.objectid import ObjectId
from src.models.chat_history import ChatHistMessage
from src.v2.chat.identify_product.suggest_product import suggest_products
from src.helper import logger,get_DB, convert_objectid_to_str
import asyncio

loggers = logger.setup_new_logging(__name__)

from openai import OpenAI
client = OpenAI(
    api_key = os.getenv("OPENAI_API_KEY")
)

from llama_index.llms.openai import OpenAI


def get_possible_products(current_user, query: str, chat_summary: ChatHistMessage):
    possible_prod = current_user.db["prompt"].find_one({"name":"possible_products"})
    identify_prompt = possible_prod["text"]
    identify_model = possible_prod["model"]
    completion = client.beta.chat.completions.parse(
            model=identify_model,
            messages=[
                {"role": "system", "content": identify_prompt},
                {"role": "assistant", "content": f"summary of the conversation:`{chat_summary.content}`"},
                {"role": "user", "content":f"latest user message : `{query}`" },
            ],
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "name": "possible_products",
                    "description": "Identify the possible product name from the query. Use the content to learn about the context of the query. In case of missing product in query, use the summary of conversation to identify the product.",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties": {
                            "possible_products": {
                                "type": "array",
                                "items": {
                                    "type": "string"
                                }
                            }
                            },
                        "additionalProperties": False,
                        "required": ["possible_products"]
                    }
                }
            }
        )
    products = json.loads(completion.choices[0].message.content)
    
    return products

def get_best_product(current_user, query, matched_products, context):
    try:
        verify_prod = current_user.db["prompt"].find_one({"name":"new_best_products"})
        verify_prompt = verify_prod["text"]
        verify_model = verify_prod["model"] 
        llm = OpenAI(
            model=verify_model,
            api_key = os.getenv("OPENAI_API_KEY")
        )
        
        matched_products = "\n".join([f"{product.course}" for product in matched_products])

        print(f"Matched Products: {matched_products}")
        
        formatted_prompt = verify_prompt.format(
            query=query,
            # summary=context,
            matched_products=matched_products
        )
        
        output=llm.complete(formatted_prompt,formatted=True)
        print(f"Output: {output.text}")
        return output.text
       
    except Exception as e:
        raise e
        loggers.error(f"Error getting best product: {e}")
        return []
    
# product ko ni model banayera db kai product lyauna kati garo hunxa ra?
def identify_product_from_query_and_summary(current_user, query: str, context:str, chat_summary: ChatHistMessage, chat_hist: ChatHistMessage) -> list[Products]:
    """
    Identify product from the latest query and chat summary
    """
    try:
        if isinstance(chat_summary,tuple):
            _, chat_summary = chat_summary

        # if current_user.tenant_id=="6799f0f28ea613739afe8dd8":

        #     prod=current_user.db.product.find_one({"_id":ObjectId("dc937b59892604f5a86ac969")})
        #     return[Products(
        #         _id="dc937b59892604f5a86ac969",
        #         course=prod["name"],
        #         status="active"
                
        #     )]
        query=chat_hist.content
        if not current_user.db["settings"].find_one({"identify_product": {"$exists": True, "$eq": True}}):
            loggers.info(f"Product identification is disabled for user: {current_user}")
            return []

        loggers.info(f"Identifying product from {query} ")
    

        responses = []
        
        possible_products = get_possible_products(current_user, query, chat_summary)
        loggers.info(f"Possible products: {possible_products}")
        
        if not possible_products.get('possible_products'):
            loggers.info(f"No possible products found for query: {query}")
            return []
        matched_products=[]
        from langchain_core.runnables import RunnableMap
        chain = {
            prod: lambda x: asyncio.run(suggest_products(current_user, prod))
            for prod in possible_products.get('possible_products')
        }

        chatin_=RunnableMap(chain)

        # tasks = [chain[prod](prod) for prod in possible_products.get('possible_products')]
        suggested_products_list = chatin_.invoke({})

        for suggested_products in suggested_products_list.values():
            matched_products.extend(suggested_products)
        matched_products_ = [current_user.db["product"].find_one({"name":product}) for product in matched_products]
        matched_products_ = convert_objectid_to_str(matched_products_)
        matched_products_obj = [Products(_id=str(product["_id"]), course=product["name"], status=product["status"]) for product in matched_products_]
            # print(matched_products_obj)
            
        if len(matched_products_obj)==1:
            loggers.info(f"Only one product matched: {matched_products_obj}")
            return matched_products_obj
        
        loggers.info(f"Matched products: {matched_products_obj}")
        best_product = get_best_product(current_user, query, matched_products_obj, context)

        import re
        try:
            if isinstance(best_product, str):
                best_product = re.sub(r"[\\[\\]]", "", best_product)
                loggers.info(f"Best product identified str: {best_product}")
                if not best_product:
                    return []
                best_product = [product for product in matched_products_obj if product.course == best_product]
                return best_product
            
            if isinstance(best_product, list):
                loggers.info(f"Best product identified list: {best_product}")
                best_products = [product for product in matched_products_obj if product.course in best_product]
                return best_products
        except Exception as e:
            loggers.error(f"Error evaluating best product: {e}")
            return []
    except  Exception as e:
        import traceback
        traceback.print_exc()
        return []
if __name__=="__main__":
    datas=[{
        "message": "3rd paper KO mtra chahiyo vane",
      "contextualize": "The user is affirming the need for the 3rd paper class only. \n```",
      "formatted_summary": "The user inquired about the cost of the 3rd paper class in the Kharidar course. The assistant responded by asking the user to make the payment soon to book the course. The user then clarified that they only wanted the 3rd paper class."
    },
     {"message": "na su  kharidar ko fee kati xa sir?",
    "contextualize": "The user is inquiring about the fee for the course. \n```",
      "formatted_summary": "The customer inquires about the fee for the course, but the agent asks for the customer's phone number to provide information over a call. The customer does not provide the phone number."
   },
    {
         "message": "Ko class kati bajey dekhi xa?",
      "contextualize": "The user is inquiring about the timing of the Kharidar Second and Third class. \n```",
      "formatted_summary": "The user greeted the assistant and inquired about the timing of the Kharidar Second and Third class. The assistant responded with \"Namaste\" and asked how they could help."
    },
    {
        "message": "Kharidar ra subbako full fi kati ho ni",
    "contextualize": "The user is inquiring about the full fee for both buyers and sellers. \n```",
      "formatted_summary": "The customer inquired about the fee for a full course, to which the agent responded that the fee is currently 8,000 rupees with a 3-day discount, making it available for 7,000 rupees. The agent then asked the customer to make the payment to secure a booking for the class. The customer asked about the fee for a buyer and a seller, but the response to that query is not provided in the transcript."
    },
    {
        "message": "Kharidarko ko tyari garnu parne ho sir",
      "contextualize": "The user is affirming the need to prepare for the Kharidar position. \n```",
      "formatted_summary": "The customer expressed interest in joining the daily classes for Nayab Subba/Kharidar preparation. The customer specifically mentioned wanting to prepare for the Kharidar position. The agent confirmed the availability of two daily classes and asked if the customer was interested in joining."
    },
    {
        "message": "sir lekhako new note ho rw ahile aha vko ?",
      "contextualize": "The user is inquiring about the availability of the new note for the 3rd paper of their purchase. \n```",
      "formatted_summary": "The customer inquired about the arrival of a new note for the 3rd paper of their purchase. The agent greeted the customer and asked how they could assist. The customer repeated their question about the new note.",
    },
    {
        "message": "Class Kari join garn Sakin xa tw sir. Vandinu na",
        "contextualize": "The user is expressing interest in joining a class and is seeking further details about the process. \n```",
      "formatted_summary": "The user inquired about the teachers for a specific course. The assistant provided the following information:\n- IQ: Kuber Adhikari\n- GK: Santosh Khadka\n- Math: Ram Psd Dulal\n- Written: Badri Psd Karki, ShreeKrishna GC\n\nThe user then asked about joining a class and requested further details."
    }
     ]
    responses=[]
    # for data in datas:
    #     response = identify_product_from_query_and_summary( data.get("message"), data.get("contextualize"), data.get("formatted_summary"), None)
    #     responses.append(response)
    
    # for response in responses:
    #     print("\n",response)