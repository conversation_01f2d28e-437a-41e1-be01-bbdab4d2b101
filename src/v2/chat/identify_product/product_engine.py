# Load configuration and connect to MongoDB
# def create_product_query_engine():
#     try:
#         # MongoDB connection setup
#         MONGO_URI = "mongodb://172.16.16.54:8300/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.2.10"
#         DEV_DB = get_DB("DEV", MONGO_URI)
#         loggers.info("Config loaded and DB connected.")
#     except Exception as e:
#         loggers.error("Error loading config or connecting to <PERSON>.")
#         raise Exception(f"DB Connection Error: {e}")

#     try:
#         # Fetch active courses

#         courses_doc = list(DEV_DB["courses"].find({"active": True}))
#         if not courses_doc:
#             loggers.warning("No active courses found.")
#             return

#         # Prepare documents for indexing

#         # Add documents to index
#         add_request = AddToIndexRequest(
#             data_name="courses",
#             vector_token="f1017707-dbe3-41ca-93a7-49bc763ff23d"
#         )
#         courses = [
#         Document(
#             text=course["course"],
#             metadata={"_id": str(course["_id"]), "remarks": course.get("remarks", "")}
#         )
#         for course in courses_doc
#     ]
#         course=[doc["course"] for doc in courses_doc]
#         add_to_index_logic(add_request, documents=courses)

#         loggers.info("All documents added to the index successfully.")
#         # Create query engine setup (optional based on your flow)
#         query_engine_request = CreateQueryEngineRequest(
#             token="f1017707-dbe3-41ca-93a7-49bc763ff23d",
#             query_engine_name="product_query_engine"
#         )
#         create_query_engine=create_query_engine_(query_engine_request)

#         loggers.info("Query engine setup complete.")
#         return create_query_engine
#     except Exception as e:
#         loggers.error(f"Error processing and setting up query engine: {e}")
#         raise Exception(f"Setup Error: {e}")

