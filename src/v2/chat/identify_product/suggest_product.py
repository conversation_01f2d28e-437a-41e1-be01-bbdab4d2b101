# from llama_index.core import PromptTemplate
# from llama_index.llms.openai import OpenAI

# from src.reply.retreiver_call import FaissCall
from src.helper.logger import setup_new_logging
# from src.helper import get_DB
from src.helper.resolve_llm import resolve_llm
loggers = setup_new_logging(__name__)

# llm=OpenAI(model="gpt-4o-mini")

# Suggest products
async def suggest_products(current_user, query):
    # loggers.info(f"Suggesting products for query: {query}")
    db_ = current_user.async_db
    prompt_ = await db_.prompt.find_one({"name":"identify_product"})
    prompt_text = prompt_["text"]
    try:        
        resp_ = await resolve_llm(model_name="gpt-4o").acomplete(
            prompt=prompt_text.format(
                query=query
            )
        )
        # loggers.info(f"\n\nIdentify Prod for Query {query}` = Response: `{resp_.text}`\n")
        return resp_.text
    except Exception as e:
        # raise e
        loggers.error(f"Error suggesting products: {e} \n returning `No Products Found`")
        return "No Products Found"
