import json
from openai import OpenAI

from src.models.category import Category
from src.helper.logger import setup_new_logging
from src.helper import get_DB
import os

client=OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
loggers = setup_new_logging(__name__)


# euta Category bhanne class banayera tyo category db bata yei load garera yaa bata aayesi List[Category] return garne ani tesmai sab kura hunxa db query jaa paye tei hannai pardaina hola ni?
def classify_into_categories(current_user, context_sentence,message,chat_hist) -> list[Category]:
    message=chat_hist.content
    loggers.info(f"Classifying into categories: {context_sentence} \n message : {message}")
    db=current_user.db["categories"]
    all_categories=list(db.find({"properties": {"$exists": True}}))
    categories=[Category.from_name(category.get("name"), current_user.db) for category in all_categories]
    try:

        if current_user.tenant_id=="6762cd86b4e4379d71ef93ee":
            if chat_hist.media_values:

                return [Category.from_id("6763bb64ed8e86b293e05943", current_user.db)]                
    except Exception as e:
        loggers.error(f"Error fetching categories: {e}")
        return []
    try:
        categories_prompt=current_user.db["prompt"].find_one({"name":"categorise_prompt"})
        sys_prompt=categories_prompt.get("text")
        model=categories_prompt.get("model")
        completion = client.beta.chat.completions.parse(
            model=model,
            messages=[
                {"role": "system", "content": sys_prompt},
                {"role":"user","content":f"contextualize_message:{context_sentence}"},
                # {"role": "user", "content": f'message:{message}'},
            ],
            response_format={
                "type": "json_schema",
                "json_schema": {
                    "name": "support_response",
                    "description": "Classify the incoming message into the relevant categories.",
                    "strict": True,
                    "schema": {
                        "type": "object",
                        "properties" : {
                            category.name: {
                                "type": category.properties.get("type"),
                                "description": category.properties.get("description")
                            }
                            for category in categories
                        },
                        "additionalProperties": False,
                        "required": [category.name for category in categories],
                    }
                }
            }
        )
        response = completion.choices[0].message.content
        categories_dict = json.loads(response)
        categories_list=list(filter(lambda x: categories_dict[x], categories_dict.keys()))
        loggers.debug(f"Classified into categories: {categories_list}")
        matching_categories = [
            category for category in categories if category.name in categories_list
        ]

        return matching_categories
    except Exception as e:
        import traceback
        traceback.print_exc()
        loggers.error(f"Error classifying into categories: {e}")
        return []

