from typing import Optional, List, Dict, Union, Literal, Any
from openai import AsyncOpenAI
from pydantic import BaseModel, Field
from datetime import datetime
from pydantic_core import core_schema
from src.models.user import UserTenantDB
from bson import ObjectId
from langchain_core.runnables import RunnableMap
import asyncio
class PydanticObjectId(ObjectId):
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type: Any, handler: Any) -> core_schema.CoreSchema:
        return core_schema.json_or_python_schema(
            json_schema=core_schema.str_schema(),
            python_schema=core_schema.union_schema([
                core_schema.is_instance_schema(ObjectId),
                core_schema.str_schema(),
            ]),
            serialization=core_schema.plain_serializer_function_ser_schema(
                lambda x: str(x), return_schema=core_schema.str_schema()
            ),
        )

class AIConfig(BaseModel):
    model: str
    temperature: float = Field(ge=0.0, le=2.0)
    max_tokens: Optional[int] = None
    top_p: Optional[float] = Field(default=1.0, ge=0.0, le=1.0)

class ReplyConfig(BaseModel):
    type: Literal["documents", "direct_ai","saved_reply"]
    retriever_requirements: Optional[Dict] = None
    documents: Optional[List[Union[str, PydanticObjectId]]] = None
    prompt_template: Optional[str] = None
    ai_config: Optional[AIConfig] = None

    async def generate_reply(self) -> str:
        if self.type == "documents":
            return await self._generate_document_reply()
        return await self._generate_direct_reply()

    async def _generate_document_reply(self) -> str:
        if not self.documents:
            raise ValueError("Documents required for document-based reply")
        # Implement document retrieval and response generation
        return ""

    async def _generate_direct_reply(self) -> str:
        if not self.prompt_template or not self.ai_config:
            raise ValueError("Prompt template and AI config required for direct reply")
        # Implement direct AI response generation
        return ""

class SubdivisionConfig(BaseModel):
    sub_category_ids: List[Union[str, PydanticObjectId]]
    prompt_template: str
    ai_config: AIConfig
    default_descriptions: Optional[Dict[str, str]] = None

    async def classify_message(
        self,
        message: str,
        client: AsyncOpenAI,
        root_category_id: Union[str, PydanticObjectId],
        summary: Any,
        current_user: UserTenantDB
    ) -> Dict[str, Any]:
        from openai import OpenAI
        client=OpenAI()
    
        sub_categories = [Category(**category) for category in 
                     list(current_user.db.category.find({"parent_id": root_category_id}))]
        if not sub_categories:
            return None
        pre_result={}
        
        for category in sub_categories:
            if category.schema_type == "subdivision":
                try:

                    result=await category.sub_divisions.classify_message(message, client, category.id, summary,current_user)
                    sub_categories.remove(category)
                    pre_result.update(result)
                except Exception as e:
                    pass
        if not sub_categories:
            return pre_result
        main_cat=current_user.db.category.find_one({"_id":ObjectId(root_category_id)})
        try:
            print(self.prompt_template.format(summary=summary))
            completion =client.beta.chat.completions.parse(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": self.prompt_template.format(summary=summary)},
                    {"role":"user","content":f"latest user message:{message}"},
                    # {"role": "user", "content": f'message:{message}'},
                ],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "support_response",
                        "description": "Classify the incoming message into the relevant categories.",
                        "strict": True,
                        "schema": {
                            "type": "object",
                            "properties" : {
                                category.name: {
                                    "type": "boolean",
                                    "description": category.refined_prompt
                                }
                                for category in sub_categories
                            },
                            "additionalProperties": False,
                            "required": [category.name for category in sub_categories],
                        }
                    }
                }
            )
            print(completion)
            result=completion.choices[0].message.content
            import json
            result=json.loads(result)
            final_result = {k: v for k, v in result.items() if v == True}
            if pre_result:
                final_result.update(pre_result)

            
            return final_result
        except Exception as e:
            import traceback
            print(f"Error classifying message: {root_category_id}")
            traceback.print_exc()
            # raise RuntimeError(f"Error classifying message: {root_category_id}")

            # raise RuntimeError(f"Classification failed: {str(e)}")

    def get_top_categories(self, classification_result: Dict[str, float], threshold: float = 0.5) -> List[str]:
        return [name for name, conf in classification_result.items() if conf >= threshold]

class CategoryOutput(BaseModel):
    category_id: str
    confidence: float = Field(ge=0.0, le=1.0)
    explanation: Optional[str] = None

class SubdivisionResult(BaseModel):
    selected_category: CategoryOutput
    other_categories: List[CategoryOutput] = []

class Category(BaseModel):
    id: Union[str, PydanticObjectId] = Field(..., alias="_id")
    name: str
    parent_id: Optional[Union[str, PydanticObjectId]] = None
    schema_type: Literal["reply", "subdivision"]
    reply_from: Optional[ReplyConfig] = None
    sub_divisions: Optional[SubdivisionConfig] = None
    personality:Optional[str]=None
    requirements: Optional[Dict[str, bool]] = {}
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    refined_prompt:str
    is_active: bool = False
    doc_id: Optional[Union[str, PydanticObjectId]] = None
    sentiment:Optional[str]=None
    language:Optional[str]=None
    personality:Optional[str]=None

    saved_reply: Optional[str] = None

    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True
    }

    async def resolve(self, initial_message: str,media_values:str, client: AsyncOpenAI, summary: Any,current_user: UserTenantDB) -> List['Category']:
        """
        Recursively resolves the category hierarchy to find all final matching categories.
        Returns a list of all matching leaf categories (schema_type="reply").
        """
        print(f"Resolving for {self.name} , initial messaege {initial_message}")
        matching_categories = []
        if current_user.tenant_id=="6762cd86b4e4379d71ef93ee":
            if media_values:
                print("default category for ask guru media")
                return [Category(**(current_user.db.category.find_one({"_id": ObjectId("6796071fa046d6f229babb70")})))]
        if self.schema_type == "reply":
            # If it's a reply category, add it to the results
            matching_categories.append(self)
        
        elif self.schema_type == "subdivision" and self.sub_divisions:
            # Classify the message to find matching subcategories
            classification = await self.sub_divisions.classify_message(
                initial_message, client, self.id,summary, current_user
            )
            
            # Process each matched category
            for cat_name, is_match in classification.items():
                if is_match:
                    # Find the full category object
                    subcat = current_user.db.category.find_one({"name": cat_name})
                    if subcat:
                        subcat = Category(**subcat)
                        # Recursively resolve the subcategory
                        results = await subcat.resolve(initial_message,media_values, client, summary,current_user)
                        matching_categories.extend(results)
        return matching_categories
    
    @classmethod
    async def create_category(cls, db, name: str, content: str, reply_type: str,parent_id: str):


        
        """Create a support category with proper configuration"""
        import hashlib
        cat_id=hashlib.sha256(name.encode('utf-8')).hexdigest()[:24]

        new_category = db.category.insert_one({
            "_id":ObjectId(cat_id),
            "name": name,
            "parent_id": ObjectId(parent_id),
            "schema_type": "reply",
            "reply_from": None,
            "created_at": datetime.utcnow(),
            "is_active": True,
            "refined_prompt":f"Does the message classify into category {name}?",
        })

        db.category.update_one({"_id": ObjectId(parent_id)}, {"$push": {"sub_divisions.sub_category_ids":ObjectId(cat_id)}})
        return ObjectId(cat_id)

