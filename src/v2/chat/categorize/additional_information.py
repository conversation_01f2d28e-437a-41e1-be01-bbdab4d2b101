from nest_asyncio import apply
from llama_index.llms.openai import OpenAI
from src.models.user import UserTenantDB

from dotenv import load_dotenv
import os
from typing import Optional
import json
from src.v2.chat.categorize.categorise import Category
from src.models.products import Products
from src.helper import(
    get_DB,
    logger)


loggers = logger.setup_new_logging(__name__)
load_dotenv()
apply()



async def check_requirements(categories: list[Category], user_info: dict, coll_name: Optional[str] | None = None,current_user:Optional[UserTenantDB]=None) -> list:
    """
    Check if user_info satisfies the requirements of any category and return unmet requirements.

    Args:
        categories (List[Category]): List of categories with requirements.
        user_info (Dict[str, any]): User data to check against requirements.

    Returns:
        List[Dict[str, any]]: List of unmet requirements for each category, or an empty list if all are met.
    """
    try:
        unmet_requirements = []

        if coll_name:
            categories = current_user.db[coll_name].find()

        for category in categories:
            requirements = category.requirements if isinstance(category, Category) else category.get("requirements")

            if requirements:
                unmet = {key: value for key, value in requirements.items() if value and key not in user_info or not user_info[key]}
                if unmet:
                    unmet_requirements.append(category.get("name") if isinstance(category, dict) else category.name)

        return unmet_requirements
    except Exception as e:
        raise e
        

async def extract_user_information(current_user, required_information, request, formatted_summary):
    try:
        llm=OpenAI(model="gpt-4o-mini")
        extract_info_prompt = current_user.db["prompt"].find_one({"name":"extract_additional_information"})["text"]
        formatted_prompt = extract_info_prompt.format(
                    required_information=" ,".join(required_information),
                    summary=formatted_summary,
                    query=request.message
                )
        output=llm.complete(formatted_prompt,formatted=True)
        cleaned_output = output.text.strip("```json").strip("```").strip()
        return json.loads(cleaned_output)
    except Exception as e:
        raise e
    
async def update_user_additional_information(current_user, request, additional_information):
    try:
        loggers.info("updating user's database")
        for key,value in additional_information.items():
            result = current_user.db["customers"].update_one(
                {"customer_id":request.user_id},
                {"$set":{f"additional_information.{key}": value}},
                upsert=True
                )
            if result.modified_count > 0:
                print(f"updated {key} with {value}")
            
    except Exception as e:
        loggers.error(f"An error occured: {e}")
        
async def request_user_information(current_user, query, formatted_summary, missing_information):
    try:
        llm=OpenAI(model="gpt-4o-mini")
        request_information_prompt = current_user.db["prompt"].find_one({"name":"request_information"})["text"]
        formatted_prompt = request_information_prompt.format(
                    query=query,
                    summary=formatted_summary,
                    missing_information = missing_information
                )
        output=llm.complete(formatted_prompt,formatted=True)
        return output.text
    except Exception as e:
        raise e
    
async def extract_additional_information(current_user, request, contextualize, formatted_summary, categories:list[Category], identified_product:list[Products]):
    """
    Given user's chat summary, contextualized sentence, and latest message, extract useful information for the category.
    """
    try:
        all_requirements = await check_requirements(categories)
        if "product" in all_requirements and not identified_product:
            missing_info = ["product"]
            return missing_info
        else:
            return []
        # if all_requirements:
        #     user_information = await extract_user_information(all_requirements, request, formatted_summary)
        #     loggers.info(f"Extracted information from user query: {user_information}")
            
        #     filtered_information = {key: value for key, value in user_information.items() if value is not None}
        #     if filtered_information:
        #         await update_user_additional_information(request, filtered_information)
        #     else:
        #         print("no new additional information")
    
        #     user = current_user.db["customers"].find_one({"customer_id": request.user_id})
        #     additional_information = user.get("additional_information", {})
        #     required_info = {key:value for key, value in additional_information.items() if key in all_requirements}
        #     missing_info = set(key for key,value in required_info.items() if value is None)
            
            # return missing_info, additional_information
        return set(), set()
    except Exception as e:
        raise e    