from fastapi import APIRouter, UploadFile, File, HTTPException, Form, WebSocket, WebSocketDisconnect, Depends, WebSocketException
from src.models.credit import CreditManager, get_credit_info
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from typing import List, Optional, Dict, Any
from src.helper.file_processing import (
    process_files,
    process_urls,
    extract_text_and_images,
    format_extracted_data,
    create_nodes,
    MinIOClient,
    handle_metadata
    
)
from src.v2.KB.kb_setup.handle_url import handle_urls
from datetime import datetime
from src.v2.KB.kb_setup.sentence_node_process import sentence_with_page_context, create_sentence_nodes
from src.helper.node_create import generate_tableOfContent
from src.reply.minio_client import MinIOClient, MinIOConfig
from dotenv import load_dotenv
import asyncio
import json
from src.models.websocketmanager import ConnectionManager
from src.helper import logger
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
import hashlib
import io
import httpx
load_dotenv()
loggers=logger.setup_new_logging(__name__)

router = APIRouter(tags=["Document Handler"])

# Global status dictionary
status_ = {
    "Uploading Files": "Pending",
    "Extracting Text": "Pending",
    "Setting up Knowledge Base": "Pending",
    "Setting up Database": "Pending",
    "Finishing Up": "Pending"
}

manager = ConnectionManager()

async def update_status(step: str, state: str, current_user: UserTenantDB):
    """Update status and notify the user via WebSocket."""
    status_[step] = state
    if manager.is_connected(current_user.tenant_id):
        # Send the updated status as a JSON object (dictionary)
        loggers.info(f"Status update:{status_}")
        await manager.abroadcast(status_, current_user.tenant_id)

@router.websocket("/setup_files")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket connection for file upload progress tracking.
    The token is passed as a query parameter.
    """
    # Retrieve token from query parameters
    token = websocket.query_params.get("token")
    if not token:
        await websocket.close(code=1008, reason="Missing token")
        return

    try:
        current_user = await get_tenant_info(token)
    except Exception as e:
        await websocket.close(code=1008, reason=str(e))
        return

    # Connect the WebSocket (this calls websocket.accept() internally)
    await manager.connect(websocket, current_user.tenant_id)

    # Optionally, send the current status right after connection
    if manager.is_connected(current_user.tenant_id):
        await manager.send_message(current_user.tenant_id, json.dumps(status_))

    try:
        while True:
            await websocket.receive_text()  # Keep the connection alive
    except WebSocketDisconnect:
        manager.disconnect(current_user.tenant_id)

@router.get("/check_status")
async def check_status(current_user: UserTenantDB = Depends(get_tenant_info)):
    """Check if a user already has an active WebSocket connection."""
    user_id = current_user.tenant_id
    return {"connected": manager.is_connected(user_id)}

@router.post("/process-documents")
async def process_documents(
    files: Optional[List[UploadFile]] = File(default=None),  # Make files optional
    urls: Optional[List[str]] = None,  # Make URLs optional
    test: Optional[bool] = True,
    current_user: UserTenantDB = Depends(get_tenant_info),
):
    # Validate that at least one of files or urls is provided
    if not files and not urls:
        raise HTTPException(
            status_code=400,
            detail="At least one of 'files' or 'urls' must be provided.",
        )
        
    #preprocessing split by , in urls only split by ,
    print(urls) 
    #filter empty strings
    urls = [url for url in urls if url.strip()]
    if urls:
        urls = [url.strip() for url in urls[0].split(",")]
    credit_manager = CreditManager(current_user.db)
    per_cost, remaining_credit = get_credit_info(cost_type="KB",current_user=current_user)
    cost_per_MB = float(current_user.db.settings.find_one({"name": "credit"})["cost_division"]["file_processing_MB"])
    if remaining_credit < per_cost:
        raise HTTPException(status_code=402, detail=f"Insufficient credits.")
    loggers.info(f"Total size of PDFs: ")
    
    global status_
    # if test:
    #     return await simulate(current_user)
    """Processes documents and sends progress updates via WebSocket."""
    try:
        # Reset status dictionary to default values before starting
        status_ = {
            "Uploading Files": "Pending",
            "Extracting Text": "Pending",
            "Setting up Knowledge Base": "Pending",
            "Generating Table of Content": "Pending",
            "Setting up Database": "Pending",
            "Finishing Up": "Pending"
        }
        # if test:
        #     return await simulate(current_user)
        await update_status("Uploading Files", "In Progress", current_user)
 
        # Processing files and URLs
        file_pdfs = await process_files(files)
        url_pdfs = await handle_urls(urls)
        loggers.info(f"Number of PDFs: {len(file_pdfs)}")
        loggers.info(f"Number of URLs: {len(url_pdfs)}")


        all_pdfs = file_pdfs + url_pdfs
        await update_status("Uploading Files", "Completed", current_user)
        loggers.info(f"Number of PDFs: {len(all_pdfs)}")
        if not all_pdfs:
            await update_status("Extracting Text", "Failed", current_user)
            return {"message": "No valid documents processed"}

        # MinIO configuration
        env_var=current_user.db.settings.find_one({"name":"env"})
        api_key_config=env_var.get("config")

        try:
            minio_config = env_var.get("minio_config")
        except Exception as e:
            raise Exception(f"Minio Error {e}") from e

        config = MinIOConfig(
            minio_url=minio_config.get("minio_url"),
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            bucket_name=minio_config.get("bucket_name"),
        )
        minio_client = MinIOClient(config)
        await update_status("Extracting Text", "In Progress", current_user)

        # Extract text and images from PDFs
        raw_data = await extract_text_and_images(all_pdfs, minio_client)
        # return raw_data
        await update_status("Extracting Text", "Completed", current_user)

        # Formatting extracted data
        await update_status("Setting up Knowledge Base", "In Progress", current_user)
        formatted_data = format_extracted_data(raw_data,current_user)
        await update_status("Setting up Knowledge Base", "Completed", current_user)

        await update_status("Generating Table of Content", "In Progress", current_user)
        documents = create_nodes(formatted_data)

        toc, documents_with_toc = generate_tableOfContent(documents, api_key_config)
        await update_status("Generating Table of Content", "Completed", current_user)

        # find if toc already exists

        current_user.db.settings.find_one_and_update(
          {"name": "toc"},  # Filter only by "name"
          {"$set": {"toc": toc, "updated_at": datetime.now()}},  # Update fields
          upsert=True  # Insert if not found
      )

        await update_status("Setting up Database", "In Progress", current_user)        
        # Inserting nodes for the database / Qdrant
        qd_config=env_var.get("qdrant_config")
        qd=Qdrant_Call(config=QdrantConfig(
            host=qd_config.get("host"),
            port=qd_config.get("port"),
            coll_name=qd_config.get("page_collection")
        ))
        await qd.insert_nodes(documents_with_toc, qd_config.get("page_collection"))

        qd.client_().create_payload_index(
            collection_name=qd_config.get("page_collection"),
            field_name="created_at",
            field_schema={"type": "datetime"}
        ) 
        qd.client_().create_payload_index(
            collection_name=qd_config.get("page_collection"),
            field_name="updated_at",
            field_schema={"type": "datetime"}
        )

        sent_nodes = await create_sentence_nodes(
            documents_with_toc,
            qd.client,
            qd_config.get("sentence_len", 3),
            qd_config.get("sentence_split_collection"),
        )
        await sentence_with_page_context(
            sent_nodes,
            qd.client,
            qd_config.get("sentence_split_collection"),
            qd_config.get("sentence_collection"),
            api_key_config,
        )
        await update_status("Setting up Database", "Completed", current_user)

        await update_status("Finishing Up", "Completed", current_user)

        processed_files = [file.filename for file in all_pdfs]

        # post processing
        documents_with_toc=await handle_metadata(documents_with_toc[:10], minio_client,["metadata","text"])
        credit_result = credit_manager.deduct_credits(
            amount=cost_per_MB,
            description="Processing documents"
        )
        
        if not credit_result["success"]:
            loggers.error(f"Failed to deduct credits: while processing documents: {credit_result['message']}")

        return {
            "processed_files": processed_files,
            "documents": documents_with_toc[:10],
            "toc":toc
        }
    except HTTPException as e:
        raise e

    except Exception as e:
        import traceback
        traceback.print_exc()
        await update_status("Finishing Up", f"Failed: {str(e)}", current_user)
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        manager.disconnect(current_user.tenant_id)
        # Reset the status to all "Pending" no matter what
        status_ = {
            "Uploading Files": "Pending",
            "Extracting Text": "Pending",
            "Setting up Knowledge Base": "Pending",
            "Generating Table of Content": "Pending",
            "Setting up Database": "Pending",
            "Finishing Up": "Pending"
        }

from pydantic import BaseModel
class Data(BaseModel):
    text:str
    metadata:dict

    # create a proper text object from the provided metadata field
    def to_markdown(self) -> str:
        """
        Formats a dictionary into a Markdown string.

        Args:
            data (dict): The dictionary to format.

        Returns:
            str: A Markdown-formatted string.
        """
        data = self.metadata
        title = data.get("name")
        markdown_lines = [f"## Name : {title}", ""]
        keys_to_exclude = ["name","images","page_number","source"]
        data = {k: v for k, v in data.items() if k not in keys_to_exclude}
        for key, value in data.items():
            # Capitalize the key and replace underscores with spaces
            key_display = key.replace("_", " ").capitalize()
            # markdown_lines.append(f"- **{key_display}**: {value}\n")
            markdown_lines.append(f"- **{key_display}**: {value}")

        return "\n".join(markdown_lines)

    # utility function which takes current user to add in metadata
    async def add_metadata(self, current_user:UserTenantDB,MinIOClient):
        self.text = self.to_markdown()+"\n\n"+self.text
        self.metadata.update({"updated_by":current_user.user.id})

        self.metadata.update({"hash_id":hashlib.sha256(f"{self.metadata}".encode()).hexdigest()})

        if "images" in self.metadata:
            # Upload images to MinIO and get their URLs
            image_urls = await self.upload_images_urltominio(self.metadata["images"], MinIOClient,self.metadata.get("source","Files"))
            self.metadata["images"] = image_urls


        return self.model_dump()



    async def upload_images_urltominio(self, images, minio_client: MinIOClient,source:str = "Files"):
        """
        Uploads images from URLs to MinIO and returns their uploaded names.
        """
        image_names = []

        headers = {
            "User-Agent": "Mozilla/5.0 (compatible; ImageFetcher/1.0)"
        }

        async with httpx.AsyncClient(follow_redirects=True, headers=headers, timeout=10.0) as client:
            for image_url in images:
                if image_url.startswith("http://") or image_url.startswith("https://"):
                    try:
                        response = await client.get(image_url)
                        response.raise_for_status()

                        image_bytes = response.content

                        image_name = f"{hashlib.sha256(image_url.encode()).hexdigest()}.jpg"
                        await asyncio.to_thread(minio_client.upload_bytes, image_name, image_bytes,"Images/{source}".format(source=source))
                        image_names.append(image_name)

                    except httpx.HTTPStatusError as e:
                        print(f"HTTP error {e.response.status_code} for {image_url}")
                    except httpx.RequestError as e:
                        print(f"Request failed for {image_url}: {e}")
                    except Exception as e:
                        print(f"Unexpected error for {image_url}: {e}")

        return image_names


@router.post("/add-documents")
async def add_documents_json(formatted_data:List[Data],current_user:UserTenantDB=Depends(get_tenant_info)):
    """
    How to send data

    if you explicitly have large text chunks, provide it in the text field

    else Keep text empty

    {
        "text": "",
        "metadata": {
            "price": "1000",
            "category": "books",
            "name":"BookName",
            "description":"books dewcription",
            "images" :[<list of images>]
             . . . <include all the fields you have for each book entry that will be used for the conversation with the user>
        }
    }
    

    """
    try:
        env_var=current_user.db.settings.find_one({"name":"env"})
        minio_config = env_var.get("minio_config")
        api_key_config=env_var.get("config")

        config = MinIOConfig(
            minio_url=minio_config.get("minio_url"),
            access_key=minio_config.get("access_key"),
            secret_key=minio_config.get("secret_key"),
            bucket_name=minio_config.get("bucket_name"),
        )
        minio_client = MinIOClient(config)

        formatted_data = await asyncio.gather(
        *[data.add_metadata(current_user, minio_client) for data in formatted_data]
    )
        
        documents = create_nodes(formatted_data)
        toc, documents_with_toc = generate_tableOfContent(documents, api_key_config)
        await update_status("Generating Table of Content", "Completed", current_user)

        # find if toc already exists

        current_user.db.settings.find_one_and_update(
          {"name": "toc"},  # Filter only by "name"
          {"$set": {"toc": toc, "updated_at": datetime.now()}},  # Update fields
          upsert=True  # Insert if not found
      )

        await update_status("Setting up Database", "In Progress", current_user)        
        # Inserting nodes for the database / Qdrant
        qd_config=env_var.get("qdrant_config")
        qd=Qdrant_Call(config=QdrantConfig(
            host=qd_config.get("host"),
            port=qd_config.get("port"),
            coll_name=qd_config.get("page_collection")
        ))
        await qd.insert_nodes(documents_with_toc, qd_config.get("page_collection"))

        qd.client_().create_payload_index(
            collection_name=qd_config.get("page_collection"),
            field_name="created_at",
            field_schema={"type": "datetime"}
        ) 
        qd.client_().create_payload_index(
            collection_name=qd_config.get("page_collection"),
            field_name="updated_at",
            field_schema={"type": "datetime"}
        )

        sent_nodes = await create_sentence_nodes(
            documents_with_toc,
            qd.client,
            qd_config.get("sentence_len", 3),
            qd_config.get("sentence_split_collection"),
        )
        await sentence_with_page_context(
            sent_nodes,
            qd.client,
            qd_config.get("sentence_split_collection"),
            qd_config.get("sentence_collection"),
            api_key_config,
        )
        await update_status("Setting up Database", "Completed", current_user)

        await update_status("Finishing Up", "Completed", current_user)

        credit_manager = CreditManager(current_user.db)
        cost_per_MB = float(current_user.db.settings.find_one({"name": "credit"})["cost_division"]["file_processing_MB"])
        # post processing
        documents_with_toc=await handle_metadata(documents_with_toc[:10], minio_client,["metadata","text"])
        credit_result = credit_manager.deduct_credits(
            amount=cost_per_MB,
            description="Processing documents"
        )
        

      
        if not credit_result["success"]:
            loggers.error(f"Failed to deduct credits: while processing documents: {credit_result['message']}")

        return {
            "documents": documents_with_toc[:10],
            "toc":toc
        }
    except Exception as e:
        import traceback
        traceback.print_exc()
        await update_status("Finishing Up", f"Failed: {str(e)}", current_user)
        raise HTTPException(status_code=500, detail=str(e))

from src.v2.KB.qdrant.handle_nodes import delete_nodes
@router.delete("/delete-documents")
async def delete_documents(id:str, current_user:UserTenantDB=Depends(get_tenant_info)):
    try:
        response = await delete_nodes(id, current_user)
        return response
    except Exception as e:
        raise HTTPException(detail=str(e), status_code=500)