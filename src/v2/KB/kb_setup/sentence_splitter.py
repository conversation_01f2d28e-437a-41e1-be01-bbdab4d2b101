import re
from llama_index.core import Document
from llama_index.core.schema import TransformComponent
from llama_index.core.vector_stores import (
    MetadataFilter,
    MetadataFilters,
    FilterOperator, FilterCondition
)

from llama_index.llms.openai import OpenAI
from llama_index.core.schema import TextNode, TransformComponent
from typing import List, Set
from itertools import islice
from tqdm import tqdm
from llama_index.vector_stores.qdrant import QdrantVectorStore
import uuid
import asyncio
from src.helper.resolve_llm import resolve_llm

from nltk.tokenize import sent_tokenize
import uuid
from typing import List, Dict

def batch_iterator(iterable, batch_size):
    """Helper function to yield items in batches."""
    iterator = iter(iterable)
    while batch := list(islice(iterator, batch_size)):
        yield batch



# class SentenceSplitter_v2(TransformComponent):

#     def __init__(self, sentence_len=300, overlap_len=20):
#         super().__init__()
#         self._sentence_len = sentence_len
#         self._overlap_len = overlap_len

#     def __call__(self, documents, **kwargs):
#         print("**MetadataSimplify**")
#         nodes_ = []
#         for doc in documents:
#             doc_text = doc.text
#             grouped_sentences = self._split_stuffs(doc_text)
#             grouped_documents = [Document(text=sent['text'], metadata={**doc.metadata, 'sent_id': sent['id']}) for sent in grouped_sentences]
#             nodes_.extend(grouped_documents)
#         return nodes_

#     def _split_stuffs(self, text):
#         # Split the text into chunks of `self._sentence_len` characters with an overlap of `self._overlap_len`
#         chunk_size = self._sentence_len
#         overlap_size = self._overlap_len
#         chunks = []
        
#         start = 0
#         while start < len(text):
#             end = start + chunk_size
#             chunk = {"text": text[start:end], "id": str(uuid.uuid4())}  # Include chunk_id in each chunk
#             chunks.append(chunk)
#             start = end - overlap_size  # Set the next start to overlap
        
#         return chunks




class SentenceSplitter_v2:
    def __init__(self, max_chunk_length: int = 300, overlap_sentences: int = 1, min_sentences_per_chunk: int = 5):
        self.max_chunk_length = max_chunk_length
        self.overlap_sentences = overlap_sentences
        self.min_sentences_per_chunk = min_sentences_per_chunk

    def __call__(self, documents, **kwargs):
        print("**MetadataSimplify**")
        nodes = []
        for doc in documents:
            doc_text = doc.text
            grouped_sentences = self._split_stuffs(doc_text)
            # grouped_documents = [
            #     {"text": chunk["text"], "metadata": {**doc.metadata, "chunk_id": chunk["id"]}}
            #     for chunk in grouped_sentences
            # ]
            grouped_documents = [Document(text=sent['text'], metadata={**doc.metadata, 'sent_id': sent['id']}) for sent in grouped_sentences]

            nodes.extend(grouped_documents)
        return nodes

    def _split_stuffs(self, text: str) -> List[Dict[str, str]]:
        sentences = sent_tokenize(text)
        chunks = []

        current_index = 0
        remaining_sentences = len(sentences)

        while current_index < len(sentences):
            current_chunk_sentences = []
            current_chunk_text = ""

            # Determine max sentences for this chunk based on overlap
            if chunks:
                max_sentences = self.min_sentences_per_chunk - self.overlap_sentences
            else:
                max_sentences = self.min_sentences_per_chunk

            while current_index < len(sentences):
                next_sentence = sentences[current_index]
                # Check character limit
                if len(current_chunk_text + " " + next_sentence) > self.max_chunk_length:
                    # If not enough sentences yet, add anyway
                    if len(current_chunk_sentences) < max_sentences:
                        current_chunk_sentences.append(next_sentence)
                        current_chunk_text += " " + next_sentence
                        current_index += 1
                        remaining_sentences -= 1
                        continue
                    else:
                        break  # Exceeded length but met min sentences

                # Add sentence and check if we've reached max sentences
                current_chunk_sentences.append(next_sentence)
                current_chunk_text += " " + next_sentence
                current_index += 1
                remaining_sentences -= 1

                if len(current_chunk_sentences) >= max_sentences:
                    break  # Stop once we've met the required number

            # Append the chunk
            chunk_id = str(uuid.uuid4())
            chunks.append({"id": chunk_id, "text": current_chunk_text.strip()})

            # Handle overlap with previous chunk
            if len(chunks) > 1:
                # Get overlap from previous chunk
                prev_chunk_sentences = sent_tokenize(chunks[-2]["text"])
                overlap = prev_chunk_sentences[-self.overlap_sentences:]

                # Prepend overlap to current chunk's text
                new_text = " ".join(overlap) + " " + chunks[-1]["text"]
                new_sentences = sent_tokenize(new_text)
                new_text = new_text.replace("\n", " ")
                # Check constraints again after adding overlap
                if (len(new_text) <= self.max_chunk_length and 
                    len(new_sentences) >= self.min_sentences_per_chunk):
                    chunks[-1]["text"] = new_text.strip()

        # Special handling for last chunk (if needed)
        if len(chunks) > 1:
            last_chunk = chunks[-1]
            second_last = chunks[-2]
            last_sentences = sent_tokenize(last_chunk["text"])
            if len(last_sentences) < self.min_sentences_per_chunk:
                merged_text = second_last["text"] + " " + last_chunk["text"]
                merged_sentences = sent_tokenize(merged_text)
                if len(merged_text) <= self.max_chunk_length and len(merged_sentences) <= self.min_sentences_per_chunk:
                    second_last["text"] = merged_text
                    chunks.pop()

        return chunks
    
class AddPageContext(TransformComponent):
    def __init__(self, page_vector_store: QdrantVectorStore, metadata_fields_to_search: Set[str], batch_size: int = 100,api_key_config=None):
        super().__init__()
        self._page_vector_store = page_vector_store
        self._metadata_fields_to_search = metadata_fields_to_search
        self._batch_size = batch_size
        self._llm = resolve_llm("models/gemini-2.0-flash", api_key_config)

    async def __call__(self, nodes: List[TextNode]) -> List[TextNode]:
        """
        Process nodes in batches to prevent memory overload.
        """
        transformed_nodes = []
        with tqdm(total=len(nodes), desc="Processing nodes") as pbar:
            for batch in batch_iterator(nodes, self._batch_size):
                tasks = [self._transform_node(node) for node in batch]
                results = await asyncio.gather(*tasks)
                transformed_nodes.extend(results)
                pbar.update(len(batch))
        return transformed_nodes

    async def _transform_node(self, node: TextNode) -> TextNode:
        """Fetch context and append it to the sentence."""
        return TextNode(text=await self._get_context(node) + node.text, metadata=node.metadata)

    async def _get_context(self, sentence_node: TextNode) -> str:
        """Use OpenAI to generate context for a sentence."""
        prompt = """
        <document>
        {doc_content}
        </document>
        <chunk>
        {chunk_content}
        </chunk>
        Provide a short context for the chunk.
        """
        page_node = self._get_page_node_from_sentence_node(sentence_node)
        response = await self._llm.acomplete(
            prompt=prompt.format(doc_content=page_node.text, chunk_content=sentence_node.text)
        )
        return response.text

    def _get_page_node_from_sentence_node(self, sentence_node: TextNode) -> TextNode:
        """Retrieve the corresponding page node using metadata filters."""
        metadata_filters = MetadataFilters(
            filters=[
                MetadataFilter(key=k, value=v, operator="==" if not isinstance(v, list) else "any")
                for k, v in sentence_node.metadata.items() if k in self._metadata_fields_to_search
            ],
            condition=FilterCondition.AND,
        )
        page_nodes = self._page_vector_store.get_nodes(filters=metadata_filters)
        return page_nodes[0] if page_nodes else None

