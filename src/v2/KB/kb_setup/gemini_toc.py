# from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, APIRouter, Depends
# import os
# from src.core.security import get_tenant_info
# from src.models.user import UserTenantDB
# from src.helper.logger import setup_new_logging
# import google.generativeai as genai
# import re
# import json
# # Setup logging
# loggers = setup_new_logging(__name__)


# async def generate_toc(file: UploadFile, output_dir: str, current_user: UserTenantDB):
#     try:
        
#         api_key=current_user.db.setting.find_one({"name":"env"})["config"]["gemini_api_key"]
#         if not api_key:
#             raise HTTPException(status_code=400, detail="API key not found")

#         genai.configure(api_key=api_key)

#         # Gemini model configuration
#         generation_config = {
#             "temperature": 0.0,
#             "top_p": 0.95,
#             "top_k": 40,
#             "max_output_tokens": 4096,
#         }
#         prompt=current_user.db["prompt"].find_one({"name":"extract_toc"})
#         if prompt:
#             system_instruction = prompt["text"]
#         else:
#             system_instruction = """

#             You are a highly skilled PDF document processor. Your task is to analyze the text and images within uploaded PDF files and extract the table of contents. Present the information in the following JSON format:

#             [
#                 {
#                     "title": "Chapter Title",
#                     "chapter": "Chapter Number (if applicable)",
#                     "summary": "Brief summary of the chapter",
#                     "pages": "Start Page Number - End Page Number" ( Eg: 2-5 ),
#                 }
#             ]
#             """

#         model = genai.GenerativeModel(
#             model_name=prompt.get("model","gemini-1.5-pro"),
#             generation_config=generation_config,
#             system_instruction=system_instruction
#         )
        
#         # Extract information using the model
#         chat_session = model.start_chat(history=[{"role": "user", "parts": [await file.read()]}])
#         response = chat_session.send_message("Extract the information from the given file in a json format")
        
#         # Parse the response
#         cleaned_response = re.search(r"```json\n(.*?)\n```", response.text, re.DOTALL)
#         toc = json.loads(cleaned_response.group(1)) if cleaned_response else json.loads(response.text)
#         loggers.info(f"Gemini Setup: Extracted TOC from the file {file.filename}...")
        
#         return toc
#     except Exception as e:
#         loggers.error(f"Error processing file {file.filename}: {repr(e)}")
#         raise Exception
