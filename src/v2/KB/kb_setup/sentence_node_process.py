from src.v2.KB.kb_setup.sentence_splitter import Sen<PERSON><PERSON><PERSON><PERSON>litter_v2, AddPageContext
from typing import List
from llama_index.core.schema import TextNode
from llama_index.embeddings.openai import OpenAIEmbedding
from qdrant_client import Qdrant<PERSON>lient
from llama_index.vector_stores.qdrant import <PERSON>drant<PERSON>ectorS<PERSON>
from llama_index.core import VectorStoreIndex
from llama_index.core import StorageContext
import asyncio
async def create_sentence_nodes(documents, client: QdrantClient, sentence_len:int , sent_coll_name:str) -> List[TextNode]:
    """
    Split documents into smaller sentences and store them in Qdrant.
    """
    splitter = SentenceSplitter_v2(max_chunk_length=300, overlap_sentences=1, min_sentences_per_chunk=5)
    sent_doc = splitter(documents)
    if not  client.collection_exists(sent_coll_name):
        client.create_collection(collection_name=sent_coll_name, vectors_config={"size": 1536, "distance": "Cosine"})

    vector_store = QdrantVectorStore(client=client, collection_name=sent_coll_name)
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    new_index = VectorStoreIndex.from_vector_store(vector_store, embed_model=OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536))
    new_index.insert_nodes(sent_doc)
    # storage_context.persist()
    return sent_doc  # Return the split sentences

async def sentence_with_page_context(sentence_nodes, client: QdrantClient,sent_coll_name=None,sent_with_context_coll_name=None,api_key_config=None) -> List[TextNode]:
    """
    Retrieve stored sentences, process with context, and generate embeddings.
    """

    if not sent_coll_name or not sent_with_context_coll_name:
        raise Exception("sentence collection name and context name required")

    try:
        vector_store = QdrantVectorStore(client=client, collection_name=sent_coll_name)
        if not client.collection_exists(sent_with_context_coll_name):
            client.create_collection(collection_name=sent_with_context_coll_name, vectors_config={"size": 1536, "distance": "Cosine"})
        # Step 1: Process with additional context
        trans_comp = AddPageContext(page_vector_store=vector_store, metadata_fields_to_search={"page_number", "source"},batch_size=250,api_key_config=api_key_config)
        processed_nodes = await trans_comp(sentence_nodes)

        # Step 2: Generate embeddings
        embed_model = OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536)
        tasks=[]
        async def post_process_nodes(node):
            return TextNode(
                text=node.text, 
                metadata=node.metadata, 
                embedding=await embed_model.aget_text_embedding(node.text)  # Await if async
            )
        for node in processed_nodes:
            tasks.append(post_process_nodes(node))
        embedded_nodes = await asyncio.gather(*tasks)
        
        # embedded_nodes = [
        #     TextNode(
        #         text=node.text, 
        #         metadata=node.metadata, 
        #         embedding=await embed_model.aget_text_embedding(node.text)  # Await if async
        #     )
        #     for node in processed_nodes
        # ]

        # Step 3: Store the embeddings in Qdrant
        vector_store = QdrantVectorStore(client=client, collection_name=sent_with_context_coll_name)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        new_index = VectorStoreIndex.from_vector_store(vector_store, embed_model=OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536))
        new_index.insert_nodes(embedded_nodes)
        # storage_context.persist()
        return embedded_nodes
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise e