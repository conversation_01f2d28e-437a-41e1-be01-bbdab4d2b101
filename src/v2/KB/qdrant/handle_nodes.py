from src.v2.KB.kb_setup.sentence_node_process import create_sentence_nodes, sentence_with_page_context
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call, QdrantConfig
from fastapi import HTTPException,APIRouter,Depends,UploadFile,File
from fastapi.responses import JSONResponse
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper import logger
import requests
from llama_index.core.schema import Document
from src.models.qdrant_model import handle_update,update_image
from datetime import datetime
import asyncio
# Create the pipeline with transformations
from llama_index.core import (
    VectorStoreIndex,
    Document
)
from llama_index.vector_stores.qdrant import QdrantVectorStore
from typing import List,Any,Optional
from src.reply.minio_client import MinIOClient,MinIOConfig
from qdrant_client.http import models as rest
# from src.qdrant.qna import regenerate_answer
from src.models.credit import CreditManager, get_credit_info
from concurrent.futures import ThreadPoolExecutor
import asyncio

loggers=logger.setup_new_logging(__name__)

handle_router=APIRouter(
    tags=["Nodes"]
)

@handle_router.put("/update_title")
async def update_nodes(request: handle_update, current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        qd_config = current_user.db.settings.find_one({"name": "env"})["qdrant_config"]
        qd = Qdrant_Call(config=QdrantConfig(
            host=qd_config["host"],
            port=qd_config["port"],
            coll_name=qd_config.get("page_collection"),
            sent_collection=qd_config.get("sentence_collection"),
            sent_split_collection=qd_config.get("sentence_split_collection")
        )).client_()
        
        if request.title:
            updated_by = current_user.user.id
            updated_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            filter_condition = rest.Filter(
                must=[
                    rest.FieldCondition(key="hash_id", match=rest.MatchValue(value=request.document_id)) 
                ])

            # Define synchronous operations
            def update_sentence_collection():
                return qd.set_payload(
                    collection_name=qd_config.get("sentence_collection"),
                    payload={"title": request.title, "updated_at": updated_at, "updated_by": updated_by},
                    points=filter_condition,
                    wait=True
                )
            
            def update_sentence_split_collection():
                return qd.set_payload(
                    collection_name=qd_config.get("sentence_split_collection"),
                    payload={"title": request.title, "updated_at": updated_at, "updated_by": updated_by},
                    points=filter_condition,
                    wait=True
                )
            
            def update_page_collection():
                return qd.set_payload(
                    collection_name=qd_config.get("page_collection"),
                    payload={"title": request.title, "updated_at": updated_at, "updated_by": updated_by},
                    points=filter_condition,
                    wait=True
                )

            # Run in ThreadPool to avoid blocking the event loop
            with ThreadPoolExecutor() as executor:
                loop = asyncio.get_event_loop()
                tasks = [
                    loop.run_in_executor(executor, update_sentence_collection),
                    loop.run_in_executor(executor, update_sentence_split_collection),
                    loop.run_in_executor(executor, update_page_collection)
                ]
                await asyncio.gather(*tasks)

        return JSONResponse(status_code=200, content={"message": "Successfully updated"})
    except HTTPException as e:
        raise e
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})
    


@handle_router.delete("/delete_nodes")
async def delete_nodes(id:str,current_user:UserTenantDB=Depends(get_tenant_info)):
    loggers.info(f"Deleting nodes: {id} from current_user: {current_user.user.id}")
    qd_config=current_user.db.settings.find_one({"name":"env"})["qdrant_config"]
    config=QdrantConfig(
        host=qd_config["host"],
        port=qd_config["port"],
        coll_name=qd_config.get("page_collection")
    )
    qd=Qdrant_Call(config=config).client_()
    #get that point metadata page number and title and sent_id
    filter=rest.Filter(
        must=[
            rest.FieldCondition(key="hash_id", match=rest.MatchValue(value=id)) 
        ])
    point_metadata = qd.query_points(collection_name=qd_config.get("page_collection"),query_filter=filter)
    
    
    if point_metadata.points:
        point_id = point_metadata.points[0].id
        page_number = point_metadata.points[0].payload.get("page_number")
        title = point_metadata.points[0].payload.get("title")
        section_title = point_metadata.points[0].payload.get("section_title")
        source = point_metadata.points[0].payload.get("source")

        meta={"source":source,"page_number":page_number,"section_title":section_title,"title":title}
        meta = {k: v for k, v in meta.items() if v is not None}  # Filter out None values
        # Create a filter for metadata
        # This will be used to delete from sentence and sentence_split collections
        # Only include keys that are relevant for deletion
        meta_filter=rest.Filter(
        must=[
            rest.FieldCondition(key=key, match=rest.MatchValue(value=value)) for key, value in meta.items() if key  in ["source","page_number","section_title","title"]
        ])
        
        #delete from page collection
        qd.delete(collection_name=qd_config.get("page_collection"),points_selector=[point_id])
        
        #delete using metadata from sentence collection
        search_sentence_coll = qd.query_points(collection_name=qd_config.get("sentence_collection"), query_filter=meta_filter)
        if search_sentence_coll.points:
            sentence_point_ids = [point.id for point in search_sentence_coll.points]
            qd.delete(collection_name=qd_config.get("sentence_collection"), points_selector=sentence_point_ids)
        
        search_sentence_split_coll = qd.query_points(collection_name=qd_config.get("sentence_split_collection"), query_filter=meta_filter)
        if search_sentence_split_coll.points:
            sentence_split_point_ids = [point.id for point in search_sentence_split_coll.points]
            qd.delete(collection_name=qd_config.get("sentence_split_collection"), points_selector=sentence_split_point_ids)
        loggers.info(f"Deleted nodes: {id} from current_user: {current_user.user.id}")
        return JSONResponse(status_code=200, content={"message": "Successfully deleted"})
    
    else:
        loggers.error(f"Point not found: {id} from current_user: {current_user.user.id}")
        return HTTPException(status_code=404, detail="Point not found")
        
#

from pydantic import BaseModel, field_validator
import re
class UpdateNode(BaseModel):
    id_: str
    text: str
    extra_info: dict
    question_id: Optional[str]

    @field_validator("extra_info", mode="before")
    @classmethod
    def remove_keys(cls, value):
        if isinstance(value, dict):
            return {k: v for k, v in value.items() if k not in ["images_url", "source_url"]}
        
        
    @field_validator("text", mode="before")
    @classmethod
    def remove_non_ascii(cls, value):
        if isinstance(value, str):
            value=value.replace("\n", " ")
            return re.sub(r'[^\x00-\x7F]+', '', value)

        return value
   
        return value
@handle_router.post("/update_nodes")
async def update_nodes(request:UpdateNode,collection_name:str,current_user:UserTenantDB=Depends(get_tenant_info)):
    try:
        # credit_manager = CreditManager(current_user.db)
        # per_cost, remaining_credit= get_credit_info(cost_type="KB",current_user=current_user)
        # if remaining_credit < per_cost:
        #     raise HTTPException(status_code=402, detail=f"Insufficient credits. Required: {per_cost}, Available: {remaining_credit}")
        
        collection_name=None
        qd_config=current_user.db.settings.find_one({"name":"env"})["qdrant_config"]
        collection_name = qd_config.get("page_collection")
        qd=Qdrant_Call(config=QdrantConfig(
            host=qd_config["host"],
            port=qd_config["port"],
            coll_name=qd_config.get("page_collection")
            
        )).client_()

        #delete old payload using id
        page_filter=rest.Filter(
        must=[
            rest.FieldCondition(key="hash_id", match=rest.MatchValue(value=request.id_)) 
        ])
        delete_=qd.delete(collection_name=collection_name,points_selector=page_filter)
        index=VectorStoreIndex.from_vector_store(QdrantVectorStore(client=qd, collection_name=collection_name))
        resp=index.insert_nodes([Document(id=request.id_,text=request.text, metadata=request.extra_info)])
        
      
    #   search based of request Metadata as source,pg number and sent_id as key
        metadata_filters = rest.Filter(
        must=[
            rest.FieldCondition(key=key, match=rest.MatchValue(value=value)) for key, value in request.extra_info.items() if key  in ["source","page_number","sent_id"]
        ])
    #   search sentest context collection item on basis of metadata_filter
        search_sentence_coll = qd.query_points(collection_name=qd_config.get("sentence_collection"), query_filter=metadata_filters,)
    #   search split sentence collection item on basis of metadata_filter
        split_sentence_search = qd.query_points(collection_name=qd_config.get("sentence_split_collection"), query_filter=metadata_filters,)
   #   Delete sentest context collection item on basis of metadata_filter searched Items id
        if search_sentence_coll and hasattr(search_sentence_coll, 'points') and search_sentence_coll.points:
            point_ids = [point.id for point in search_sentence_coll.points]
            print(f"\n\n{point_ids=}")
            qd.delete(collection_name=qd_config.get("sentence_collection"), points_selector=point_ids)
            print(f"Deleted points with IDs: {point_ids}")
        else:
            print("No points found in test_sentence_context2.")
   #   Delete split sentence  collection item on basis of metadata_filter searched Items id
        if split_sentence_search and hasattr(split_sentence_search, 'points') and split_sentence_search.points:
            point_ids = [point.id for point in split_sentence_search.points]
            print(f"\n\n{point_ids=}")
            # qd.delete(collection_name="test_sentence_context2", points_selector=point_ids)  #for sentence context 
            qd.delete(collection_name=qd_config.get("sentence_split_collection"), points_selector=point_ids) # for split sentence
            print(f"Deleted split points with IDs: {point_ids}")
        else:
            print("No points found in test_sentence_split.")

        sent_nodes = await create_sentence_nodes(
            [Document(text=request.text, metadata=request.extra_info)],qd,qd_config.get("sentence_len", 3),qd_config.get("sentence_split_collection"))
        await sentence_with_page_context(sent_nodes,qd,qd_config.get("sentence_split_collection"),qd_config.get("sentence_collection"),qd_config.get("config"))

        #question id
        # if request.question_id:
        #     await regenerate_answer(question_id=request.question_id,current_user=current_user)

        
        #find page points id
        page_metadata_filters = rest.Filter(
        must=[
            rest.FieldCondition(key=key, match=rest.MatchValue(value=value)) for key, value in request.extra_info.items() if key  in ["source","page_number"]
        ])
        page_point=qd.query_points(collection_name=collection_name,query_filter=page_metadata_filters)
        page_points_id=page_point.points[0].id
        # credit_result = credit_manager.deduct_credits(
        #         amount=per_cost,
        #         description="Updating Facts",
                
        #     )
        # if not credit_result["success"]:
        #         loggers.error(f"Failed to deduct credits while update Document: {credit_result['message']}")

        qd.set_payload(collection_name=collection_name,payload={"updated_at":datetime.now().strftime("%Y-%m-%d %H:%M:%S"),"updated_by":current_user.user.id},points=[page_points_id])
        return JSONResponse(status_code=200, content={"message": "succesfully updated","page_points_id":page_points_id})
    except HTTPException as e:
        raise e
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"error": str(e)})




async def post_process(response):
    # post process and return only id and payload from response
    processed_response = []
    
    for item in response.get('result', []):
        processed_response.append({
            'id': item.get('id'),
            'payload': item.get('payload')
        })
    return processed_response

import json
@handle_router.post("/image_upload")
async def upload_image(files: List[UploadFile] = File(...), source: str = "", data: str = "", current_user: UserTenantDB = Depends(get_tenant_info)):
    try:
        # Parse incoming JSON data (if present)
        data = json.loads(data) if data else []

        # Fetch MinIO configuration from the database
        minio_config: dict = current_user.db.settings.find_one({"name": "env"}).get("minio_config")
        minio = MinIOClient(config=MinIOConfig(**minio_config))

        # Initialize an empty list to store upload results
        uploaded_files = []

        # Iterate through all files and upload them to MinIO
        for file in files:
            file_content = await file.read()  # Asynchronously read the file content
            
            # Upload the file bytes to MinIO storage under the folder "Images/{source}"
            minio.upload_bytes(object_name=file.filename, file_bytes=file_content, folder=f"Images/{source}")
            
            # Get a presigned URL for accessing the file
            minio_url = minio.get_presigned_url(file.filename, folder=f"Images/{source}")
            
            # Append the file information to the result list
            uploaded_files.append({"name": file.filename, "url": minio_url})
        
        # Return success response with the uploaded file details
        return JSONResponse(status_code=200, content={"message": "Successfully uploaded", "data": uploaded_files})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JSONResponse(status_code=500, content={"error": str(e)})


@handle_router.put("/update_image")
async def update_image_payload(request:update_image,current_user:UserTenantDB=Depends(get_tenant_info)):
    try:
        qd_config=current_user.db.settings.find_one({"name":"env"})["qdrant_config"]

        #page coll name
        collection_name = qd_config.get("page_collection")

        qd=Qdrant_Call(config=QdrantConfig(
            host=qd_config["host"],
            port=qd_config["port"],
            coll_name=collection_name
        )).client_()
        if not request.images:
            request.images=[]
        filter=rest.Filter(
        must=[
            rest.FieldCondition(key="hash_id", match=rest.MatchValue(value=request.document_id)) 
        ])
        resp=qd.set_payload(collection_name=collection_name,payload={"images":request.images,"updated_at":datetime.now().strftime("%Y-%m-%d %H:%M:%S")},points=filter,wait=True)
        
        #sent_collection
        sent_collection=qd_config.get("sentence_collection")
 
        qd.set_payload(collection_name=sent_collection,payload={"images":request.images,"updated_at":datetime.now().strftime("%Y-%m-%d %H:%M:%S")},points=filter,wait=True)

        #sent_split_collection
        sent_split_collection=qd_config.get("sentence_split_collection")
  
        qd.set_payload(collection_name=sent_split_collection,payload={"images":request.images,"updated_at":datetime.now().strftime("%Y-%m-%d %H:%M:%S")},points=filter,wait=True)

        return JSONResponse(status_code=200, content={"message": resp.status})
    except HTTPException as e:
        raise e
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})    
    

class delete_image(BaseModel):
    object_name:str
    source:str

@handle_router.delete("/delete_image")
async def delete_images(source:str,object_name:str,current_user:UserTenantDB=Depends(get_tenant_info)):
    try:
        loggers.info(f"Deleting image: {object_name} from source: {source},current_user: {current_user.user.id}")
        minio_config: dict = current_user.db.settings.find_one({"name": "env"}).get("minio_config")
        minio = MinIOClient(config=MinIOConfig(**minio_config))
        minio.delete_file(object_name=f"Images/{source}/{object_name}")
        loggers.info(f"Deleted image: {object_name} from source: {source},current_user: {current_user.user.id}")
        return JSONResponse(status_code=200, content={"message": "Successfully deleted"})
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})



class add_resource(BaseModel):
    url:List[str]
    id:str

@handle_router.post("/add_resource")
async def add_resource(request:add_resource,current_user:UserTenantDB=Depends(get_tenant_info)):
    try:
        qd_config=current_user.db.settings.find_one({"name":"env"})["qdrant_config"]
        collection_name = qd_config.get("page_collection")
        qd=Qdrant_Call(config=QdrantConfig(
            host=qd_config["host"],
            port=qd_config["port"],
            coll_name=collection_name
        )).client_()
        filter=rest.Filter(
        must=[
            rest.FieldCondition(key="hash_id", match=rest.MatchValue(value=request.id)) 
        ])
        resp=qd.set_payload(collection_name=collection_name,payload={"resource_url":request.url},points=filter,wait=False)
        #sent_collection
        sent_collection=qd_config.get("sentence_collection")
        qd.set_payload(collection_name=sent_collection,payload={"resource_url":request.url},points=filter,wait=False)
        #sent_split_collection
        sent_split_collection=qd_config.get("sentence_split_collection")
        qd.set_payload(collection_name=sent_split_collection,payload={"resource_url":request.url},points=filter,wait=False)

        return JSONResponse(status_code=200, content={"message": resp.status})
    except HTTPException as e:
        raise e
    except Exception as e:
        return JSONResponse(status_code=500, content={"error": str(e)})
