from typing import List, Dict, Optional
from pydantic import BaseModel
import re
from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, QueryBundle
from qdrant_client.http.models import Record  # Qdrant Record type
from llama_index.core.schema import Document
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores import (
    MetadataFilter,
    MetadataFilters,
    FilterOperator,
)
from llama_index.core.retrievers import BaseRetriever, VectorIndexRetriever, KeywordTableSimpleRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from src.helper.resolve_llm import resolve_llm
import json


class QdrantConfig(BaseModel):
    coll_name: str
    host: str = "*************"
    port: int = 6333

class Qdrant_Call:
    def __init__(self, config: QdrantConfig,model_name=None):
        self.config = config
        self.client = QdrantClient(host=self.config.host, port=self.config.port)
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=self.config.coll_name)
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        self.index = VectorStoreIndex.from_vector_store(self.vector_store,     
            embed_model=OpenAIEmbedding(model="text-embedding-3-large", dimensions=1536)
        )
        self.coll_config={
            "size": 1536,
            "distance": "Cosine"}
        self.llm=resolve_llm(model_name)
    def client_(self):
        return self.client
    def delete_collection(self, collection_name: str) -> bool:
        self.client.delete_collection(collection_name=collection_name)
        return True
    
    def create_collection(self, group: str, collection_name: str, vectors_config: dict) -> bool:
        self.client.create_collection(collection_name=collection_name, vectors_config=vectors_config,metadata={"group": group})
        return True

    def add_document_to_index(self, Nodes: List, collection_name: str) -> bool:
        self.client.delete_collection(collection_name=collection_name)
        vectors_config = {"size": 1536, "distance": "Cosine"}
        self.client.create_collection(collection_name=collection_name, vectors_config=vectors_config)
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=collection_name)
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        self.index = VectorStoreIndex.from_documents(Nodes, storage_context=self.storage_context)
        self.index.insert_nodes(Nodes)
        return True

    class KeywordRetriever(KeywordTableSimpleRetriever):
        """Custom keyword retriever with regex matching"""
        def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
            keywords = self._get_keywords(query_bundle.query_str)
            retriever_ = self._index.as_retriever(similarity_top_k=100)
            nodes = []
            
            for keyword in keywords:
                nodes.extend(retriever_.retrieve(keyword))

            # Deduplicate while preserving order
            seen = set()
            nodes = [node for node in nodes if not (node.node.node_id in seen or seen.add(node.node.node_id))]
            
            # Regex match nodes
            matched_nodes = []
            for keyword in keywords:
                pattern = re.compile(rf'\b{re.escape(keyword)}\b', re.IGNORECASE)
                for node_with_score in nodes:
                    if pattern.search(node_with_score.node.text):
                        matched_nodes.append(node_with_score)
            return matched_nodes

    class LostInTheMiddleRetriever(BaseRetriever):
        """Hybrid retriever combining vector and keyword results"""
        def __init__(
            self,
            vector_retriever: VectorIndexRetriever,
            keyword_retriever: 'Qdrant_Call.KeywordRetriever',
            mode: str = "AND"
        ) -> None:
            self._vector_retriever = vector_retriever
            self._keyword_retriever = keyword_retriever
            self._mode = mode
            super().__init__()

        def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
            vector_nodes = self._vector_retriever.retrieve(query_bundle)
            keyword_nodes = self._keyword_retriever._retrieve(query_bundle)

            vector_ids = {n.node.node_id for n in vector_nodes}
            keyword_ids = {n.node.node_id for n in keyword_nodes}

            combined_dict = {n.node.node_id: n for n in vector_nodes + keyword_nodes}

            if self._mode == "AND":
                retrieve_ids = vector_ids & keyword_ids
            else:
                retrieve_ids = vector_ids | keyword_ids

            retrieve_nodes = [combined_dict[rid] for rid in retrieve_ids]
            return sorted(retrieve_nodes, key=lambda x: x.score, reverse=True)

    def qdran_call(self, query: str, metadata: Optional[List[Dict]],levels:Optional[int] = 5, mode: str = "AND") -> str:
        """
        Execute a hybrid search query with metadata filtering
        
        Args:
            query: Search query string
            metadata: Optional list of metadata filters
            mode: Combination mode ("AND" or "OR")
        """
        try:
            # Handle metadata filters
            metadata_filter = None
            if metadata:
                filters = [
                    MetadataFilter(
                        key=m["key"], 
                        value=m["value"], 
                        operator=FilterOperator(m["operator"].upper())
                    ) for m in metadata if m
                ]
                metadata_filter = MetadataFilters(filters=filters)

            # Initialize retrievers
            vector_ret = VectorIndexRetriever(
                index=self.index,
                filters=metadata_filter,
                similarity_top_k=levels
            )
            keyword_ret = self.KeywordRetriever(self.index)
            
            # Create hybrid retriever
            hybrid_retriever = self.LostInTheMiddleRetriever(
                vector_retriever=vector_ret,
                keyword_retriever=keyword_ret,
                mode=mode
            )
            
            # Execute query
            query_engine = RetrieverQueryEngine.from_args(hybrid_retriever,llm=self.llm)
            response = query_engine.query(query)
            return response
        
        except Exception as e:
            raise RuntimeError(f"Query execution failed: {str(e)}") from e
        
    
    async def q_retreive(self, query: str, metadata: Optional[List[Dict]] = None, top_k:int=10,mode: str = "AND"):
        try:
            if metadata:
                filters = [
                        MetadataFilter(
                            key=m["key"], 
                            value=m["value"], 
                            operator=FilterOperator(m["operator"].upper())
                        ) for m in metadata if m
                    ]
                metadata_filter = MetadataFilters(filters=filters)
                ret_engine=self.index.as_retriever(filters=metadata_filter,similarity_top_k=top_k)
                return ret_engine.retrieve(query)
         
            return self.index.as_retriever(similarity_top_k=top_k).retrieve(query)
        except Exception as e:
            raise RuntimeError(f"Query retrieval failed: {str(e)}") from e    
    async def query_engine(self, query: str, metadata: Optional[List[Dict]] = None, top_k:int=1,mode: str = "AND"):
        if metadata:
            filters = [
                    MetadataFilter(
                        key=m["key"], 
                        value=m["value"], 
                        operator=FilterOperator(m["operator"].upper())
                    ) for m in metadata if m
                ]
            metadata_filter = MetadataFilters(filters=filters)
            index_engine=self.index.as_query_engine(filters=metadata_filter,similarity_top_k=top_k)
            return index_engine.query(query)
        return self.index.as_query_engine(similarity_top_k=top_k,llm=self.llm).query(query)

    async def delete_nodes(self,node_ids: List[int]):
        self.index.delete_nodes(node_ids=node_ids,delete_from_docstore=True)
        self.index.storage_context.persist()
        return True
    # 

    def patch_node_metadata_from_qdrant(self, nodes_with_scores: List[NodeWithScore]) -> List[NodeWithScore]:
        ids = [nws.node.node_id for nws in nodes_with_scores]  # node_id is already a string or UUID
        results = self.client.retrieve(collection_name=self.config.coll_name, ids=ids)

        # Build a lookup map
        payload_map = {str(point.id): point.payload for point in results}

        # Patch each node
        for nws in nodes_with_scores:
            node_id = str(nws.node.node_id)
            if node_id in payload_map:
                nws.node.metadata = payload_map[node_id]
        
        return nodes_with_scores





    # 
    async def update_nodes(self):
        return True
    async def insert_nodes(self,Nodes,collection_name: str):
        
        for node in Nodes:
            if "title_embedding" in node.metadata:
                del node.metadata["title_embedding"]

        
        if collection_name :
            if not  self.client.collection_exists(collection_name):
                self.client.create_collection(collection_name=collection_name, vectors_config=self.coll_config)
            vector_store=QdrantVectorStore(client=self.client, collection_name=collection_name)
            storage_context = StorageContext.from_defaults(vector_store=vector_store)
            index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)
            # vector_store.add(nodes=Nodes)
            # storage_context.vector_store.persist()
            index.insert_nodes(Nodes)
            # index.storage_context.persist()
            return True
        # self.index.insert_nodes(Nodes)
        # self.index.storage_context.persist()
        return False
    
    async def find_all(self,collection_name: str):
        self.client.scroll(collection_name=collection_name,limit=10)
    

    async def convert_record_to_document(self,record: Record) -> Document:
        """
        Helper function to process a single Qdrant Record and convert it into a LlamaIndex Document.

        Args:
            record (Record): A single Qdrant Record object.

        Returns:
            Document: A LlamaIndex Document object.
        """
        import json
        payload = record.payload
        data:dict=json.loads(payload.get("_node_content"))
        if not payload:
            return None  # Skip records without payload

        # Extract fields from the payload
        text = data.get("text_resource", {}).get("text", "")  # Default to empty string if "text" is missing
        metadata =data.get("metadata", {})  # Default to empty dict if "metadata" is missing
        
        #ignore key like source_url,source_name,source_type,source_id
        metadata = {k: v for k, v in metadata.items() if k not in ["source_url", "images_url"]}
        # Create and return a LlamaIndex Document object
        return Document(
            text=text,
            metadata=metadata,
            id_=str(record.id),  # Convert ID to string if necessary
        )
        
    async def convert_records_to_documents(self,records: List[Record]) -> List[Document]:
        return [self.convert_record_to_document(record) for record in records]
    async def convert_document_to_record(self,document: Document) -> Record:
        return Record(
            id=document.id,
            payload=json.dumps(document.metadata),
            vector=document.embedding,
        )
    async def convert_documents_to_records(self,documents: List[Document]) -> List[Record]:
        return [self.convert_document_to_record(document) for document in documents]
    
    async def update_document(self,collection_name: str,document: Document):
        #update using points payload
        update_response=self.client.overwrite_payload(collection_name=collection_name,payload=document.metadata,points=[document.id],wait=True)
        return update_response
        
        
    
    async def streaming_engine(self):
        return self.index.as_query_engine(similarity_top_k=5,streaming=True)
    