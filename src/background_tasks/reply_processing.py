"""
Background tasks for processing reply-related operations.
"""
from datetime import datetime
from typing import Dict, List, Set, Any
from bson import ObjectId

from fastapi import BackgroundTasks
from qdrant_client import models

from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.reply.minio_client import MinIOClient

# Initialize logging
loggers = setup_new_logging(__name__)


async def process_source_nodes(
    request_msg_id: str,
    source_nodes: List[Any],
    qd_client: Any,
    qd_config_: Dict,
    minio: MinIOClient,
    current_user: UserTenantDB,
) -> None:
    """
    Process source nodes in the background to extract metadata and update the database.

    Args:
        request_msg_id: The ID of the request message
        source_nodes: List of source nodes to process
        qd_client: Qdrant client instance
        qd_config_: Qdrant configuration
        minio: MinIO client instance
        current_user: Current user tenant database
    """
    try:
        # loggers.info(f"Starting background processing of source nodes for request {request_msg_id}")

        metadata_list = []
        seen = set()
        seen_nodes = set()
        image_urls = set()
        image_names = set()
        resource_urls = set()

        # Skip if no source nodes
        if not source_nodes:
            loggers.info(f"No source nodes to process for request {request_msg_id}")
            return

        # Batch process node IDs to reduce individual queries
        node_hash_ids = []
        node_map = {}

        # First pass: collect all node IDs and build a map
        for node in source_nodes:
            if node.id_ in seen_nodes:
                continue
            seen_nodes.add(node.id_)

            node_hash_id = node.metadata.get("sent_id")
            if node_hash_id:
                node_hash_ids.append(node_hash_id)
                node_map[node_hash_id] = node

        # Batch query if we have nodes to process
        if node_hash_ids:
            try:
                qd = qd_client.client_()
                # Use IN filter for batch processing instead of individual queries
                data = qd.query_points(
                    collection_name=qd_config_.get("sentence_split_collection"),
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="sent_id",
                                match=models.MatchAny(any=node_hash_ids),
                            )
                        ]
                    ),
                    limit=len(node_hash_ids),
                )

                # Update node metadata from query results
                for point in data.points:
                    sent_id = point.payload.get("sent_id")
                    if sent_id in node_map:
                        node_map[sent_id].metadata.update(point.payload)
            except Exception as e:
                loggers.error(f"Error in batch query: {e}")

        # Process nodes and gather metadata
        for node in source_nodes:
            if node.id_ not in seen_nodes:
                continue

            source = node.metadata.get("source")
            if not source:
                continue

            # Process resource URLs (could be images, videos, etc.)
            resource_url = node.metadata.get("resource_url")
            if resource_url:
                loggers.info(f"Found resource_url in node: {resource_url}")
                if isinstance(resource_url, list):
                    for i in resource_url:
                        if isinstance(i, dict):
                            url = i.get("url")
                            if url:
                                loggers.info(f"Adding URL from dict: {url}")
                                image_urls.add(url)
                                resource_urls.add(url)
                        else:
                            loggers.info(f"Adding direct URL: {i}")
                            resource_urls.add(i)
                            image_urls.add(i)
                else:
                    loggers.info(f"Adding single resource URL: {resource_url}")
                    image_urls.add(resource_url)
                    resource_urls.add(resource_url)

            # Only get source URL if we haven't seen this source before
            if source not in seen:
                try:
                    source_url = minio.get_presigned_url(source, "Files")
                except Exception as e:
                    loggers.error(f"Error getting source URL for {source}: {e}")
                    source_url = None

                # Process images (limit to first 5 to reduce processing)
                images = node.metadata.get("images", [])[:5]  # Increased limit for more images

                # Process image URLs in a more efficient way
                source_image_urls = []
                for image in images:
                    try:
                        image_url = minio.get_presigned_url(image, f"Images/{source}")
                        loggers.info(f"Generated image URL: {image_url} for path Images/{source}/{image}")
                        image_names.add(f"Images/{source}/{image}")
                        source_image_urls.append(image_url)
                        # Add all image URLs - we'll handle limiting later if needed
                        image_urls.add(image_url)
                        resource_urls.add(image_url)  # Also add to resource_urls to ensure it's included
                    except Exception as e:
                        loggers.error(f"Error getting image URL for {image}: {e}")

                # Add metadata entry
                seen.add(source)
                metadata_list.append(
                    {
                        "source": source,
                        "source_url": source_url,
                        "resource_url": resource_url,
                        "images": images,
                        "image_urls": source_image_urls,  # Use source-specific URLs
                        "created_at": node.metadata.get("created_at"),
                        "updated_at": node.metadata.get("updated_at"),
                    }
                )

        # Prepare the combined list of URLs (deduplicated)
        combined_urls = list(set(list(resource_urls) + list(image_urls)))
        loggers.info(f"Updating database with {len(combined_urls)} URLs: {combined_urls}")

        # Update the database with processed metadata
        current_user.db.ai_response.update_one(
            {"_id": ObjectId(request_msg_id)},
            {"$set": {
                "response.metadata": metadata_list,
                "response.reply_urls": combined_urls,
                "response.background_processing_completed": True,
                "response.background_processing_time": (datetime.now() - datetime.now()).total_seconds(),  # Just use 0 for simplicity
            }}
        )

        # Update chat messages with image information
        chat_messages = current_user.db.chat_messages.find({"message_id": str(request_msg_id)})
        for message in chat_messages:
            if message.get("role") == "assistant":
                loggers.info(f"Updating chat message {message['_id']} with {len(image_names)} media IDs")
                # Update both media_ids and media_values to ensure compatibility
                current_user.db.chat_messages.update_one(
                    {"_id": message["_id"]},
                    {"$set": {
                        "media_ids": list(image_names),
                        "media_values": ",".join(combined_urls) if combined_urls else ""
                    }}
                )

        loggers.info(f"Completed background processing for request {request_msg_id}")

    except Exception as e:
        loggers.exception(f"Error in background processing for request {request_msg_id}: {e}")
        # Update the database to indicate processing failed
        try:
            current_user.db.ai_response.update_one(
                {"_id": ObjectId(request_msg_id)},
                {"$set": {
                    "response.background_processing_error": str(e),
                    "response.background_processing_completed": False,
                }}
            )
        except Exception as update_error:
            loggers.error(f"Failed to update error status: {update_error}")


def schedule_source_node_processing(
    background_tasks: BackgroundTasks,
    request_msg_id: str,
    source_nodes: List[Any],
    qd_client: Any,
    qd_config_: Dict,
    minio: MinIOClient,
    current_user: UserTenantDB,
) -> None:
    """
    Schedule the processing of source nodes as a background task.

    Args:
        background_tasks: FastAPI BackgroundTasks instance
        request_msg_id: The ID of the request message
        source_nodes: List of source nodes to process
        qd_client: Qdrant client instance
        qd_config_: Qdrant configuration
        minio: MinIO client instance
        current_user: Current user tenant database
    """
    background_tasks.add_task(
        process_source_nodes,
        request_msg_id,
        source_nodes,
        qd_client,
        qd_config_,
        minio,
        current_user,
    )
    loggers.info(f"Scheduled background processing for request {request_msg_id}")
