from llama_index.core.postprocessor.types import BaseNodePostprocessor

# Define a class for top 3 product suggestions
class GiveMeTop3(BaseNodePostprocessor):
    """Similarity-based Node processor."""
    
    @classmethod
    def class_name(cls) -> str:
        return "SimilarityPostprocessor"

    def _postprocess_nodes(self, nodes, query_bundle):
        """Postprocess nodes."""
        nodes = sorted(nodes, key=lambda x: x.score, reverse=True)
        return nodes[:3]
