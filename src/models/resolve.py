from typing import List, Dict, Optional, Literal, Any
from pydantic import BaseModel,validator
from datetime import datetime
from llama_index.core.schema import (
    NodeWithScore,
)
from enum import Enum
from src.helper.logger import setup_new_logging

loggers=setup_new_logging(__name__)

class Relation(str, Enum):
    SIMILAR = "similar"
    DUPLICATE = "duplicate"
    CONFLICTING = "conflicting"
    NO_RELATION = "no_relation"

class Answer(BaseModel):
    answer: str
    source_nodes: List[NodeWithScore]
    relation: Relation
    conflict_reason: Optional[str]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        return cls(**data)
    @validator('conflict_reason', always=True)
    def validate_conflict_reason(cls, value: Optional[str], values: Dict[str, Any]):
        # Accepted conflict reason strings
        valid_conflict_reasons = {"similar","duplicate", "conflicting", "no_relation"}
        
        # If the conflict_reason is None or empty, set it to "conflicting"
        if not value or value.strip() == "":
            return None
        
        # If the conflict_reason doesn't match any of the valid ones, set it to "conflicting"
        if value.lower() not in valid_conflict_reasons:
            return None
        
        return value

class Question(BaseModel):
    question: str
    question_id: str
    metadata: Optional[Dict[str, Any]]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]):
        return cls(**data)

class QnA(BaseModel):
    question: Question
    answer: Answer
    resolved: bool
    created_at: datetime
    resolved_at: Optional[datetime]
    resolved_by: Optional[str]

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        """
        Custom serialization method for QnA.
        Converts the QnA object into a dictionary, ensuring MongoDB compatibility.
        """ 
        # Call the parent class's model_dump() method to get the base dictionary
        qna_dict = super().model_dump(**kwargs)

        # Convert datetime objects to strings for MongoDB compatibility
        qna_dict["created_at"] = self.created_at.isoformat()
        if self.resolved_at:
            qna_dict["resolved_at"] = self.resolved_at.isoformat()

        return qna_dict

    @classmethod
    def model_validate(cls, data: Dict[str, Any]) -> "QnA":
        """
        Custom deserialization method for QnA.
        Converts a dictionary into a QnA object.
        """
        # Convert datetime strings back to datetime objects
        data["created_at"] = datetime.fromisoformat(data["created_at"])
        if data.get("resolved_at"):
            data["resolved_at"] = datetime.fromisoformat(data["resolved_at"])

        # Create and return a QnA object
        return cls(**data)


class FormatFilterQnA(BaseModel):
    data: List[QnA]

    def model_dump(self, **kwargs) -> Dict[str, Any]:
        filtered_answers = []
        for ans in self.data:
            from pprint import pprint
            pprint(ans)
            
            if ans.answer.relation in {Relation.DUPLICATE, Relation.CONFLICTING}:
                if len(ans.answer.source_nodes) > 1:
                    filtered_answers.append(ans.model_dump(**kwargs))
                elif len(ans.answer.source_nodes) == 1:
                    try:
                        sent_len= ans.answer.source_nodes[0].metadata.get("sentence",[])
                        if len(sent_len) > 1:
                            filtered_answers.append(ans.model_dump(**kwargs))
                    except Exception as e:
                        loggers.error(f"Error in dumping data: {e}")                        
                        filtered_answers.append(ans.model_dump(**kwargs))

        return filtered_answers

    @classmethod
    def validate_data(cls, data: List[QnA],**kwargs) -> List[QnA]:
        filtered_answers = []
        for ans in data:
            if ans.answer.relation in {Relation.DUPLICATE, Relation.CONFLICTING}:
                if len(ans.answer.source_nodes) > 1:
                    filtered_answers.append(ans.model_dump(**kwargs))
                elif len(ans.answer.source_nodes) == 1:
                    try:
                        sent_len= ans.answer.source_nodes[0].metadata.get("sentence",[])
                        if len(sent_len) > 1:
                            filtered_answers.append(ans.model_dump(**kwargs))
                    except Exception as e:
                        loggers.error(f"Error in validating data: {e}")
                        filtered_answers.append(ans.model_dump(**kwargs))

        return filtered_answers

from bson import ObjectId
class PaginatedResponse(BaseModel):
    data: List[Any]
    meta: Dict[str, Any]

    class Config:
        json_encoders = {ObjectId: str}