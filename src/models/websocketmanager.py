from fastapi import WebSocket, WebSocketDisconnect, WebSocketException
from typing import Dict,Any

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}  # key: tenant_id

    async def connect(self, websocket: WebSocket, user_id: str):
        """Accept a WebSocket connection and store it."""
        try:
            await websocket.accept()
            self.active_connections[user_id] = websocket
        except Exception as e:
            raise WebSocketException(code=1002, reason=f"Failed to connect: {str(e)}")

    def disconnect(self, user_id: str):
        """Remove a WebSocket connection when a user disconnects."""
        self.active_connections.pop(user_id, None)

    async def send_message(self, user_id: str, message: str):
        """Send a direct message to a specific user if they are connected."""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(message)
            except Exception as e:
                raise WebSocketException(code=1011, reason=f"Message sending failed: {str(e)}")
        else:
            raise WebSocketException(code=1008, reason=f"User {user_id} is not connected.")

    async def broadcast(self, message: str):
        """Broadcast a message to all connected users."""
        disconnected_users = []
        for user_id, connection in self.active_connections.items():
            try:
                await connection.send_text(message)
            except Exception:
                disconnected_users.append(user_id)
        for user_id in disconnected_users:
            self.disconnect(user_id)

    async def abroadcast(self, message: Any, user_id: str):
        """Send a JSON message to a specific user."""
        if user_id in self.active_connections:
            await self.active_connections[user_id].send_json(message)

    def is_connected(self, user_id: str) -> bool:
        """Check if a user is connected."""
        return user_id in self.active_connections

    def get_active_connections(self) -> Dict[str, WebSocket]:
        """Return all active WebSocket connections."""
        return self.active_connections

    def get_active_users_count(self) -> int:
        """Return the number of active connections."""
        return len(self.active_connections)
