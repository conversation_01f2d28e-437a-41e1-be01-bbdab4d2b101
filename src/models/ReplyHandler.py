from datetime import datetime
from typing import List, Optional, Dict
import asyncio
import logging
from pydantic import ValidationError

from llama_index.llms.openai import OpenAI
from src.reply.custom_chat_engine import custom_chat_engine
from src.reply.sentiment_language import fetch_saved_reply, refine_reply
# from src.reply.retreiver_call import FaissCall
from src.reply.reply_evaluator import evaluate_reply
from src.models.chat_history import ChatHistMessage
from src.helper.logger import apply_logging_to_all_functions
from src.helper.resolve_llm import resolve_llm
from src.v2.chat.categorize.categorise import Category,ReplyConfig,AIConfig
from src.v2.chat.categorize.additional_information import check_requirements
from src.v2.KB.qdrant.qdrant_client import Qdrant_Call,QdrantConfig

loggers = logging.getLogger(__name__)
apply_logging_to_all_functions(globals())

class ReplyHandler:
    def __init__(self, current_user, request, categories, identified_product, 
                 contextualize, summary,chat_raw_hist, language_response, db,final_response):
        self.current_user = current_user
        self.latest_message = request
        self.categories: List[Category] = categories
        self.identified_product = identified_product
        self.contextualize = contextualize
        self.chat_raw_hist = chat_raw_hist[0]
        self.summary = summary
        self.language_response = language_response
        self.db = db

        # Response tracking
        self.reply = None
        self.saved_reply = None
        self.ai_reply = None
        self.suggested_reply = None
        self.retrieval_reply = None
        self.reply_from: List[str] = []
        self.missing_doc_reply = []
        self.final_response :dict= final_response
        self.images=[]

        # Evaluation responses
        self.relevance_response: Dict = {}
        self.faithfulness_response: Dict = {}
        self.retrieval_response: Dict = {}

    @property
    def personality(self):
        PROMPT_NAME = "generate_personality"    
        prompt=self.current_user.db["prompt"].find_one({"name":PROMPT_NAME})
            
        try:
            llm = resolve_llm(prompt.get("model"))
            return llm.complete(
                prompt.get("text").format(summary_data=self.summary),
                formatted=True
            ).text
        except Exception as e:
            raise e

    def _generate_query(self):
        """Uses Summary and Latest user message to generate a query using a LLM Call"""

        try:
            user_sentence_prompt = self.current_user.db["prompt"].find_one({"name":"context_for_retriever"})
            llm=OpenAI(model=user_sentence_prompt["model"])

            formatted_prompt = user_sentence_prompt["text"].format(
                        summary=self.summary,
                        latest_user_message=self.latest_message
                    )

            user_sentence = llm.complete(formatted_prompt)

            # Now generate query

            ret_query_prompt = self.current_user.db["prompt"].find_one({"name":"retriever_query_prompt"})
            llm=OpenAI(model=ret_query_prompt["model"])

            formatted_prompt = ret_query_prompt["text"].format(
                summary_sentence=user_sentence
            )

            query_ = llm.complete(formatted_prompt)
            query_=query_.text
        except Exception as e:
            loggers.error(f"Error generating query: {e}, using the latest user message as query")
            query_ = self.latest_message

        return query_

    async def _fetch_saved_reply(self):
        """Fetches and sets saved replies while updating categories."""
        categories_, saved_reply = fetch_saved_reply(
            self.current_user, 
            self.categories, 
            self.identified_product
        )
        self.categories = categories_
        if saved_reply:
            self.saved_reply = saved_reply
            self.reply_from.append('saved')

    async def _generate_ai_reply(self):
        """Generates AI-based suggested reply for relevant categories."""
        ai_categories = []
        print("self.categories",self.categories)
        product_id = self.identified_product[0].id if self.identified_product else None

        for cat in self.categories:
            query = {
                "category": str(cat.id),
                "reply_config.reply_from": "direct_ai",
            }
            if cat.requirements.get("product")==True:
                query["product"] = str(product_id)
            if self.current_user.db.reply_setup.find_one(query):
                db_record = self.current_user.db.reply_setup.find_one(query)['reply_config']

                reply_config_data = ReplyConfig(
                    type=db_record.get("reply_from", "direct_ai"),
                    retriever_requirements=db_record.get("retriever_requirements", None),
                    documents=db_record.get("documents", None),
                    prompt_template=db_record.get("prompt_template", None),
                    ai_config=AIConfig(
                        model=db_record["ai_config"]["model"],
                        temperature=db_record["ai_config"]["temperature"],)
                )
                try:
                    cat.reply_from = reply_config_data
                except ValidationError as e:
                    raise ValueError(f"Invalid reply_config data: {e}")                
                ai_categories.append(cat)

        field=self.db.settings.find_one({"name": "final_reply_field"}).get("field")
        if ai_categories:
            loggers.info("Retrieving document-based replies for {} categories".format(ai_categories))

            if field=="reply":
                self.ai_reply = custom_chat_engine(
                self.current_user,
                self.latest_message,
                self.contextualize,
                self.chat_raw_hist,
                ai_categories,
                self.summary,
                self.language_response
            )
            elif field=="suggested_reply":
                self.suggested_reply = custom_chat_engine(
                    self.current_user,
                    self.latest_message,
                    self.contextualize,
                    self.chat_raw_hist,
                    None,
                    None,
                    self.language_response
      
                )

    async def _retrieve_document_reply(self):
        """Retrieves and evaluates document-based replies."""
        doc_categories = []
        
        #qdrant call
            
        
        for cat in self.categories:
            product_id = self.identified_product[0].id if self.identified_product else None
            query={
                "category": str(cat.id),
                "reply_config.reply_from": "documents",
            }
            if cat.requirements.get("product")==True:
                query["product"] = str(product_id)
            if self.current_user.db.reply_setup.find_one(query):
                db_record = self.current_user.db.reply_setup.find_one(query)['reply_config']

                reply_config_data = ReplyConfig(
                    type=db_record.get("reply_from", "documents"),
                    retriever_requirements=db_record.get("retriever_requirements", None),
                    documents=db_record.get("documents", None),
                    prompt_template=db_record.get("prompt_template", None),
                    ai_config=AIConfig(
                        model=db_record["ai_config"]["model"],
                        temperature=db_record["ai_config"]["temperature"],)
                ) if db_record.get("documents", None) else None
                try:
                    cat.reply_from = reply_config_data
                except ValidationError as e:
                    raise ValueError(f"Invalid reply_config data: {e}")          
                doc_categories.append(cat)
        # if not doc_categories:
        #     return
        qdrant_config=self.current_user.db.settings.find_one({"name": "env"})["qdrant_config"]
        qd=Qdrant_Call(
            config=QdrantConfig(
                host=qdrant_config.get("host"),
                port=qdrant_config.get("port"),
                coll_name=qdrant_config.get("page_collection")
            )
        )
        
        if self.current_user.tenant_id=="6799f0f28ea613739afe8dd8":
            response=qd.qdran_call(query=self._generate_query(),metadata=None)
            print("qdrant response",response)
            if response.response:
                self.retrieval_reply=response.response
                self.reply_from=["document"]
                
                self.images=[node.metadata.get("images") for node in response.source_nodes]
                return
            
        if doc_categories:
            loggers.info("Retrieving document-based replies for {} categories".format(doc_categories))
            metadata=None
            if product_id:
                metadata=[{"key":"product","value":[str(product_id)],"operator":"=="}]
            response=qd.qdran_call(query=self._generate_query(),metadata=metadata)
            self.retrieval_reply=response.response
            self.reply_from=["document"]
            return
        
        # if doc_categories:
        #     loggers.info("Retrieving document-based replies for {} categories".format(doc_categories))
        #     faiss_call = FaissCall(
        #         self._generate_query(),
        #         self.identified_product,
        #         "echo_reply_prompt",
        #         None,
        #         doc_categories,
        #         self.contextualize,
        #         language=self.language_response
        #     )
        #     reply_ = await faiss_call.execute(self.current_user.db)
        # if not doc_categories:
        #     loggers.info("No document-based replies for any categories")
        #     faiss_call = FaissCall(
        #         self._generate_query(),
        #         self.identified_product,
        #         "echo_reply_prompt",
        #         None,
        #         [],
        #         self.contextualize,
        #         language=self.language_response
        #     )  
        #     reply_ = await faiss_call.execute(self.current_user.db)

        #     if reply_ and reply_.response and len(reply_.response.strip()) > 20:
        #         self.retrieval_reply = reply_.response
        #         self.images=reply_.metadata.get("images",[])
        #         #fro source node extract images
        #         try:
        #             for node in reply_.source_nodes:
        #                 print(node.metadata)
        #                 if node.metadata.get("images"):
        #                     self.images.extend(node.metadata["images"])
        #         except Exception as e:
        #             loggers.error(f"Error processing source nodes: {e}")
        #         # self.retrieval_reply ="\n".join(node.text for node in reply_.source_nodes)

        #         self.retrieval_response = reply_
        #         self.reply_from.append('document')
        #     else:
        #         self.missing_doc_reply = ["no_document_reply"]

    async def _process_final_reply(self):
        """Determines final reply based on priority of sources."""
        if self.saved_reply and not self.retrieval_reply:
            self.reply = self.saved_reply.response
            return
        base_reply="No reply Found"
        reply_components = []
        if self.retrieval_reply:
            reply_components.append(f"**Retrieval Reply**: {self.retrieval_reply}")
        if self.saved_reply:
            reply_components.append(f"**Saved Reply**: {self.saved_reply.response}")
        if self.ai_reply:
            reply_components.append(f"**AI Reply**: {self.ai_reply}")

        # if not reply_components and self.ai_reply:
        #     self.reply = self.ai_reply
        #     return

        if reply_components:
            base_reply = "\n\n".join(reply_components)
        call_to_action = await check_requirements(
            self.categories,
            self.final_response,
            "call_to_action",
            self.current_user,
        )

        self.reply = refine_reply(base_reply, self.latest_message, self.db,self.chat_raw_hist, call_to_action,self.language_response)

    async def handle(self) -> Dict:
        """Orchestrates the complete reply generation workflow."""
        # Concurrently fetch different reply types
        async with asyncio.TaskGroup() as tg:
            tg.create_task(self._fetch_saved_reply())
            tg.create_task(self._generate_ai_reply())
            tg.create_task(self._retrieve_document_reply())

        # Process final reply combination
        await self._process_final_reply()

        # Evaluate reply quality if needed
        # if self.retrieval_reply:
        #     self.relevance_response, self.faithfulness_response = await evaluate_reply(
        #         self.current_user,
        #         self.contextualize,
        #         self.retrieval_response,
        #         self.reply
        #     )

        return {
            "reply": self.reply,
            "reply_from": self.reply_from,
            "suggested_reply": self.suggested_reply,
            "retreival_reply": self.retrieval_reply,
            "relevance": self.relevance_response,
            "faithfulness": self.faithfulness_response,
            "call_to_action": self.missing_doc_reply,
            "suggested_images": self.images
        }
