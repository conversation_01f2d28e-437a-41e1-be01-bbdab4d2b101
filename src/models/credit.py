from datetime import datetime
from typing import List, Optional, Dict, Any, Annotated,Literal,Union
from pydantic import BaseModel, Field, PlainSerializer
from bson import ObjectId
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging

loggers = setup_new_logging(__name__)

# Define a serializer for ObjectId
def object_id_serializer(obj_id: ObjectId) -> str:
    """Serialize ObjectId to string."""
    return str(obj_id)

# Create an annotated type for ObjectId
PyObjectId = Annotated[
    ObjectId,
    PlainSerializer(object_id_serializer)
]

class TransactionBase(BaseModel):
    """Base model for credit transactions"""
    transaction_id: PyObjectId = Field(default_factory=ObjectId)
    amount: Union[float,int]
    timestamp: datetime = Field(default_factory=datetime.now)
    description: str

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat(),
            ObjectId: str
        }
    }

class CreditUsage(TransactionBase):
    """Model for tracking individual credit usage transactions"""
    message_id: Optional[str] = None
    transaction_type: str = "usage"

class CreditAddition(TransactionBase):
    """Model for tracking credit additions"""
    transaction_type: str = "addition"
    source: Optional[str] = None  # e.g., "purchase", "bonus", "refund"

class CreditTransaction(BaseModel):
    """Model for a unified credit transaction record"""
    transaction_id: PyObjectId = Field(default_factory=ObjectId)
    transaction_type: str  # "addition" or "usage"
    amount: Union[float,int]
    original_amount: Optional[Union[float,int]] = None  # Original amount before currency conversion
    currency: Optional[str] = None  # Currency used for the transaction
    timestamp: datetime = Field(default_factory=datetime.now)
    description: str
    message_id: Optional[str] = None  # For usage transactions
    source: Optional[str] = None  # For addition transactions
    cost_breakdown: Optional[Dict[str, float]] = None  # Cost breakdown by type
    # Additional fields that might be present in the transaction records
    customer_id: Optional[str] = None
    customer_name: Optional[str] = None
    channel: Optional[str] = None

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat(),
            ObjectId: str
        },
        # Allow extra fields to be included
        "extra": "ignore"
    }

    @classmethod
    def from_usage(cls, usage: CreditUsage) -> "CreditTransaction":
        return cls(
            transaction_id=usage.transaction_id,
            transaction_type="usage",
            amount=usage.amount,
            timestamp=usage.timestamp,
            description=usage.description,
            message_id=usage.message_id
        )

    @classmethod
    def from_addition(cls, addition: CreditAddition) -> "CreditTransaction":
        return cls(
            transaction_id=addition.transaction_id,
            transaction_type="addition",
            amount=addition.amount,
            timestamp=addition.timestamp,
            description=addition.description,
            source=addition.source,
            # New fields will be None by default
        )

class CreditBalance(BaseModel):
    """Model for credit balance information"""
    total_credits: Union[float,int] = 0.0
    remaining: Union[float,int] = 0.0
    cost_division: dict = {}
    last_updated: datetime = Field(default_factory=datetime.now)

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat()
        }
    }

class CreditHistory(BaseModel):
    """Model for credit history"""
    transactions: List[CreditTransaction] = Field(default_factory=list)

    model_config = {
        "arbitrary_types_allowed": True
    }

class CreditSystem(BaseModel):
    """Main model for the credit system"""
    id: PyObjectId = Field(default_factory=ObjectId, alias="_id")
    name: str = "credit"
    total_credits: Union[float,int] = 0.0
    remaining: Union[float,int] = 0.0
    history: List[Dict[str, Any]] = Field(default_factory=list)
    addition_history: List[Dict[str, Any]] = Field(default_factory=list)
    usage_history: List[Dict[str, Any]] = Field(default_factory=list)
    last_updated: datetime = Field(default_factory=datetime.now)
    per_cost: Union[float,int] = 2.0  # Default per-message cost
    credit_map: Dict[str, str] = Field(default_factory=dict)  # Currency to credit conversion map
    cost_division: Dict[str, Any] = Field(default_factory=dict)  # Cost division percentages

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            ObjectId: str,
            datetime: lambda dt: dt.isoformat()
        },
        "populate_by_name": True
    }

class AddCreditsRequest(BaseModel):
    """Request model for adding credits"""
    amount: Union[float,int]
    description: Optional[str] = "Credit addition"
    source: Optional[str] = None
    currency: Optional[Literal["USD", "INR"]] = "INR"  # Default currency is USD

    model_config = {
        "arbitrary_types_allowed": True
    }

class CreditResponse(BaseModel):
    """Response model for credit operations"""
    success: bool
    message: str
    balance: Optional[CreditBalance] = None
    transaction: Optional[CreditTransaction] = None

    model_config = {
        "arbitrary_types_allowed": True
    }

class TransactionResponse(BaseModel):
    """Response model for transaction list"""
    transactions: List[CreditTransaction] = Field(default_factory=list)
    total: int = 0
    page: int = 1
    limit: int = 50
    total_pages: int = 1
    has_more: bool = False
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    transaction_type: Optional[str] = None
    total_cost: dict = {}

    model_config = {
        "arbitrary_types_allowed": True,
        "json_encoders": {
            datetime: lambda dt: dt.isoformat()
        }
    }
    
    def model_dump_pagination(self):
        # data and meta 
        return{
            "data":self.transactions,
            "meta":{
                "total":self.total,
                "page":self.page,
                "limit":self.limit,
                "total_pages":self.total_pages,
            }
        }

class CreditManager:
    """Generic class for managing credits"""

    def __init__(self, db):
        """
        Initialize the credit manager

        Args:
            db: MongoDB database connection
        """
        self.db = db
        self.now = datetime.now()

        # Get credit document from settings collection
        credit_doc = db.settings.find_one({"name": "credit"})

        # Get per_cost from settings, default to 2 if not found
        self.per_cost = credit_doc.get("per_cost", 2) if credit_doc else 2

        # Get cost_division from settings, default to empty dict if not found
        self.cost_division = credit_doc.get("cost_division", {}) if credit_doc else {}

        # Get credit_map from settings, default to empty dict if not found
        self.credit_map = credit_doc.get("credit_map", {}) if credit_doc else {}

    def _get_credit_document(self):
        """Get the credit document from the database"""
        return self.db.settings.find_one({"name": "credit"})

    def _create_credit_document_if_not_exists(self):
        """Create a new credit document if it doesn't exist"""
        credit_doc = self._get_credit_document()
        if not credit_doc:
            # Create a new credit document
            credit_system = CreditSystem(name="credit")
            # Ensure _id is properly set as ObjectId
            credit_data = credit_system.model_dump(by_alias=True)
            # Make sure _id is an ObjectId instance, not a string
            if "_id" in credit_data and not isinstance(credit_data["_id"], ObjectId):
                credit_data["_id"] = ObjectId(credit_data["_id"]) if isinstance(credit_data["_id"], str) else ObjectId()
            self.db.settings.insert_one(credit_data)
            return self._get_credit_document()
        return credit_doc

    def add_credits(self, amount: Union[float,int], description: str = "Credit addition", source: Optional[str] = None, currency: str = "usd") -> dict:
        """
        Add credits and record the transaction

        Args:
            amount: Amount of credits to add
            description: Description of the credit addition
            source: Source of the credits (e.g., "purchase", "bonus", "refund")
            currency: Currency of the amount (default: "usd")

        Returns:
            dict: Result of the operation with success status and transaction details
        """
        # Get or create credit document
        credit_doc = self._create_credit_document_if_not_exists()

        total_credits = credit_doc.get("total_credits", 0)
        remaining = credit_doc.get("remaining", 0)

        # Apply currency conversion if needed
        credit_amount = amount
        if currency:
            # Get credit_map from the credit document
            credit_map = credit_doc.get("credit_map", {})
            # Get conversion rate for the specified currency (default to 1:1 if not found)
            currency__key=[i for i in credit_map.keys() if currency.lower() in i.lower()]
            if not currency__key:
                raise ValueError(f"Currency {currency} not found in credit map")

            conversion_rate = credit_map.get(currency__key[0], 1)
            try:
                loggers.info(f"Conversion rate for {currency}: {conversion_rate}")
                # Convert to integer to ensure we're dealing with whole credits
                conversion_rate = int(conversion_rate)
                credit_amount = amount * conversion_rate
            except (ValueError, TypeError):
                # If conversion fails, use the original amount
                credit_amount = amount

        # Add credits
        new_total = total_credits + credit_amount
        new_remaining = remaining + credit_amount

        # Create transaction ID
        transaction_id = ObjectId()

        # Create transaction record
        transaction = {
            "transaction_id": transaction_id,
            "transaction_type": "addition",
            "amount": credit_amount,
            "original_amount": amount,
            "currency": currency.lower(),
            "timestamp": self.now,
            "description": description,
            "source": source
        }

        # Create addition record for history
        addition_record = {
            "amount": credit_amount,
            "original_amount": amount,
            "currency": currency.lower(),
            "timestamp": self.now,
            "description": description,
            "source": source,
            "transaction_id": transaction_id,  # Store as ObjectId, not string
            "transaction_type": "addition"
        }

        # Update credit document
        update_result = self.db.settings.update_one(
            {"name": "credit"},
            {
                "$set": {
                    "total_credits": new_total,
                    "remaining": new_remaining,
                    "last_updated": self.now
                },
                "$push": {
                    "addition_history": addition_record,
                    "history": addition_record
                }
            }
        )

        if update_result.modified_count == 0:
            return {
                "success": False,
                "message": "Failed to update credit document"
            }

        return {
            "success": True,
            "message": f"Successfully added {amount} credits",
            "transaction": transaction,
            "total_credits": new_total,
            "remaining": new_remaining
        }

    def deduct_credits(self, amount: Optional[Union[float,int]] = None, message_id: Optional[str] = None, description: str = "Message processing") -> dict:
        """
        Deduct credits and record the transaction

        Args:
            amount: Amount of credits to deduct (default: uses per_cost from settings)
            message_id: ID of the message associated with this deduction
            description: Description of the credit usage

        Returns:
            dict: Result of the operation with success status and transaction details
        """
        # Use per_cost from settings if amount is not provided
        if amount is None:
            amount = self.per_cost
        # Get current credit document
        credit_doc = self._get_credit_document()
        loggers.info(f"\n\nTransaction amount: {amount}")


        if not credit_doc:
            return {
                "success": False,
                "message": "No credit document found"
            }

        total_credits = credit_doc.get("total_credits", 0)
        remaining = credit_doc.get("remaining", 0)

        # Check if there are enough credits
        if remaining < amount:
            return {
                "status_code": 402,
                "success": False,
                "message": f"Insufficient credits. Required: {amount}, Available: {remaining}"
            }

        # Deduct credits
        new_remaining = remaining - amount

        # Create transaction ID
        transaction_id = ObjectId()

        # Calculate cost division if available
        cost_breakdown = {}
        if self.cost_division:
            for cost_type, percentage in self.cost_division.items():
                try:
                    # Convert percentage to float if it's a string
                    if isinstance(percentage, str):
                        percentage = float(percentage)
                    # Calculate the cost for this type
                    cost_breakdown[cost_type] = (percentage / 100) * amount
                except (ValueError, TypeError):
                    # Skip if conversion fails
                    continue

        # Create transaction record
        transaction = {
            "transaction_id": transaction_id,
            "transaction_type": "usage",
            "amount": amount,
            "timestamp": self.now,
            "description": description,
            "message_id": str(message_id) if message_id else None,
            "cost_breakdown": cost_breakdown
        }

        # Create usage record for history
        usage_record = {
            "amount": amount,
            "timestamp": self.now,
            "description": description,
            "message_id": str(message_id) if message_id else None,
            "transaction_id": transaction_id,  # Store as ObjectId, not string
            "transaction_type": "usage",
            "cost_breakdown": cost_breakdown
        }

        # Update credit document
        update_result = self.db.settings.update_one(
            {"name": "credit"},
            {
                "$set": {
                    "remaining": new_remaining,
                    "last_updated": self.now
                },
                "$push": {
                    "usage_history": usage_record,
                    "history": usage_record
                }
            }
        )

        if update_result.modified_count == 0:
            return {
                "success": False,
                "message": "Failed to update credit document"
            }

        return {
            "success": True,
            "message": f"Successfully deducted {amount} credits",
            "transaction": transaction,
            "total_credits": total_credits,
            "remaining": new_remaining
        }

    def get_balance(self) -> dict:
        """
        Get the current credit balance

        Returns:
            dict: Credit balance information
        """
        credit_doc = self._get_credit_document()

        if not credit_doc:
            return {
                "success": False,
                "message": "No credit document found",
                "balance": {
                    "total_credits": 0,
                    "remaining": 0,
                    "last_updated": self.now
                }
            }

        # Calculate used credits
        total_credits = credit_doc.get("total_credits", 0)
        remaining = credit_doc.get("remaining", 0)
        used_credits = total_credits - remaining

        # Calculate cost division if available
        cost_division = {}
        if self.cost_division:
            for cost_type, percentage in self.cost_division.items():
                try:
                    # Convert percentage to float if it's a string
                    if isinstance(percentage, str):
                        percentage = float(percentage)
                    # Calculate the cost for this type
                    cost_division[cost_type] = (percentage / 100) * used_credits
                except (ValueError, TypeError):
                    # Skip if conversion fails
                    continue

        return {
            "success": True,
            "message": "Credit balance retrieved successfully",
            "balance": {
                "total_credits": total_credits,
                "remaining": remaining,
                "last_updated": credit_doc.get("last_updated", self.now),
                "cost_division": cost_division
            }
        }

    def get_history(self, transaction_type: Optional[str] = None, start_date: Optional[datetime] = None,
                 end_date: Optional[datetime] = None, limit: int = 50, skip: int = 0) -> dict:
        """
        Get transaction history

        Args:
            transaction_type: Filter by transaction type (addition or usage)
            start_date: Start date for filtering transactions
            end_date: End date for filtering transactions
            limit: Maximum number of transactions to return
            skip: Number of transactions to skip

        Returns:
            dict: Transaction history
        """
        credit_doc = self._get_credit_document()

        if not credit_doc:
            return {
                "success": False,
                "message": "No credit document found",
                "transactions": [],
                "total": 0
            }

        addition_history = credit_doc.get("addition_history", [])
        usage_history = credit_doc.get("usage_history", [])
        # Use specific history arrays based on transaction_type
        if transaction_type == "addition":
            transactions = credit_doc.get("addition_history", [])
        elif transaction_type == "usage":
            transactions = credit_doc.get("usage_history", [])
        else:
            # If no specific type is requested, combine both arrays
            # addition_history = credit_doc.get("addition_history", [])
            # usage_history = credit_doc.get("usage_history", [])
            transactions = addition_history + usage_history

        # Apply date filtering if provided
        if start_date or end_date:
            addition_history=list(filter(lambda x: (
                (not start_date or x["timestamp"] >= start_date) and
                (not end_date or x["timestamp"] <= end_date)
            ), addition_history))
            usage_history=list(filter(lambda x: (
                (not start_date or x["timestamp"] >= start_date) and
                (not end_date or x["timestamp"] <= end_date)
            ), usage_history))
            filtered_transactions = []
            for t in transactions:
                # Get timestamp from transaction
                timestamp = t.get("timestamp")

                # Convert string timestamp to datetime if needed
                if isinstance(timestamp, str):
                    try:
                        timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    except (ValueError, TypeError):
                        # Skip if conversion fails
                        continue

                # Apply date filtering
                if start_date and end_date:
                    if start_date <= timestamp <= end_date:
                        filtered_transactions.append(t)
                elif start_date:
                    if start_date <= timestamp:
                        filtered_transactions.append(t)
                elif end_date:
                    if timestamp <= end_date:
                        filtered_transactions.append(t)

            transactions = filtered_transactions

        # Sort by timestamp (newest first)
        transactions.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)

        # Get total count
        total_count = len(transactions)
        total_cost={
            "addition":sum(t.get("amount", 0) for t in addition_history),
            "usage":sum(t.get("amount", 0) for t in usage_history),
            "total":sum(t.get("amount", 0) for t in transactions)
        }
        # Apply pagination
        paginated_transactions = transactions[skip:skip+limit]

        return {
            "success": True,
            "message": "Transaction history retrieved successfully",
            "transactions": paginated_transactions,
            "total": total_count,
            "total_cost": total_cost
        }

def get_credit_info(cost_type:str,current_user: UserTenantDB) -> tuple:
    credit_doc = current_user.db.settings.find_one({"name": "credit"})
    per_cost = credit_doc.get("cost_division", {}).get(cost_type, 1) if credit_doc else 1
    remaining_credit = credit_doc.get("remaining", 0) if credit_doc else 0
    loggers.info(f"per_cost: {per_cost}, remaining_credit: {remaining_credit}")
    return per_cost, remaining_credit

