from typing import Any, Dict,Literal,Optional
from pydantic import BaseModel
from typing import List
class Category(BaseModel):
    _id: Dict[str, str]
    name: str
    requirements: Dict[str, bool] | None=None
    personality: str
    language: str
    properties: Dict[str, Any]
    reply_from: Literal["documents", "ai", "human","saved_reply"]

    retriever_requirements: Dict[str, bool]| None=None

    @classmethod
    def from_mongo(cls, data: dict):
        if "_id" in data:
            data["_id"] = str(data.pop("_id"))
        return cls(**data)

class Category_type(BaseModel):
    id: str
    type: str
    description: Optional[str] = None
    categories: Optional[List[str]] = None

    @classmethod
    def from_mongo(cls, data: dict):
        if "_id" in data:
            data["id"] = str(data.pop("_id"))
        

        return cls(**data)
