from pydantic import BaseModel, Field
from bson import ObjectId
from typing import Any, Dict, Optional, Union
from pymongo.collection import Collection

class SavedReply(BaseModel):
    id: Optional[str]
    description: str
    category: Optional[str] = None

    class Config:
        populate_by_name = True
        json_encoders = {ObjectId: str}

    @classmethod
    def from_mongo(cls, data: Dict[str, Any]) -> Optional["SavedReply"]:
        """Convert MongoDB dict to SavedReply model, handling _id conversion."""
        if not data:
            return None
        if data.get("_id"):
            data["id"] = str(data.pop("_id"))
        return cls(**data)

    def model_dump(self, *args, **kwargs):
        """Custom model_dump to handle ObjectId conversion to str."""
        data = super().model_dump(*args, **kwargs)
        if isinstance(data.get("id"), ObjectId):
            data["id"] = str(data["id"])
        return data

    def model_dump_mongo(self):
        """Convert model to MongoDB-compatible dict with ObjectId."""
        data = self.model_dump(exclude_none=True)
        if data.get("id"):
            data["_id"] = ObjectId(data.pop("id"))
        return data

    @classmethod
    async def get_or_create(
        cls,
        collection: Collection,
        saved_reply: "SavedReply"
    ) -> tuple[Union["SavedReply", None], bool]:
        """
        Get existing saved reply or create new one.
        Returns tuple of (SavedReply, bool) where bool indicates if created.
        """
        try:
            mongo_data = saved_reply.model_dump_mongo()
            
            # Check if saved reply exists
            if saved_reply.id:
                existing = collection.find_one({"_id": mongo_data["_id"]})
                if existing:
                    print("Updating existing reply")
                    # Update existing reply
                    collection.update_one(
                        {"_id": mongo_data["_id"]},
                        {"$set": mongo_data}
                    )
                    return cls.from_mongo(mongo_data), False  # Return updated data
            
            # Only reach here if no ID or no existing document found
            print("Creating new reply")
            result = collection.insert_one(mongo_data)
            mongo_data["_id"] = result.inserted_id
            return cls.from_mongo(mongo_data), True

        except Exception as e:
            raise Exception(f"Error in get_or_create: {str(e)}")