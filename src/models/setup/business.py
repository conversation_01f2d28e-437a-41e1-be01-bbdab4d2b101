from typing import List, Optional
from pydantic import BaseModel, Field, EmailStr, HttpUrl

class SocialMedia(BaseModel):
    name: str = Field("Facebook", description="Name of the social media")
    handle: str = Field("https://www.facebook.com/", description="Handle of the social media")

class BusinessInfo(BaseModel):
    id: str = Field(..., alias="_id")
    name: str = Field("XYZ Company", description="Name of the business")
    business_category: Optional[str] = Field(None, description="Domain of the business")
    description: Optional[str] = Field(None, description="Description of the business")
    phone: int = Field(4111111, description="Phone number of the business")
    email: Optional[EmailStr] = Field(None, description="Email of the business")
    city: str = Field("Kathmandu", description="City of the business")
    state: str = Field("Bagmati", description="State of the business")
    country: str = Field("Nepal", description="Country of the business")
    postcode: Optional[str] = Field(None, description="Postcode of the business")
    website: Optional[str] = Field("https://www.google.com/", description="Website of the business")
    socials: Optional[List[SocialMedia]] = Field(None, description="Social media handles of the business")
    office_hours: Optional[str] = Field(None, description="Office hours of the business")
    payment_methods: Optional[List[str]] = Field(None, description="Payment methods of the business")
    subscriptions: List[str] = Field(["chatbot"], description="List of client subscriptions")
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed=True


class Business_setup(BaseModel):
    name: str = "Business setup"
    type: str = Field("business", description="Type of setup")
    description: Optional[str] = Field(None, description="Description of the setup")