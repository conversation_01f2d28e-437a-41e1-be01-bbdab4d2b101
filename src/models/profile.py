from pydantic import BaseModel
from typing import List, Optional, Dict

class AdditionalInformation(BaseModel):
    phone_number: str
    is_course_activated: bool
    is_payment_done: bool

class CustomerProfileResponse(BaseModel):
    name: str
    trending_product: Optional[list]
    trending_category: List[str]
    latest_message: Optional[str]
    latest_summary: Optional[str]
    purchased_product: Optional[str]
    summary_id: Optional[str]
    sentiment: Optional[str]
    language: Optional[str]
    additional_information: Dict | None
    

