from fastapi import Form, Request, Depends
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON><PERSON>orm
from pydantic import BaseModel, Field
from typing import Optional, Union, Dict, Any
from urllib.parse import urlparse
from src.helper.logger import setup_new_logging
from fastapi.exceptions import HTTPException
from src.core.database import get_tenant_id_and_name_from_slug
loggers = setup_new_logging(__name__)

class OAuth2PasswordRequestFormWithClientID(OAuth2PasswordRequestForm):
    def __init__(
        self,
        grant_type: str = Form(None, regex="password"),
        username: str = Form(...),
        password: str = Form(...),
        scope: str = Form(""),
        client_id: str = Form(...),  # Add client_id as a required field

    ):
        super().__init__(grant_type=grant_type, username=username, password=password, scope=scope)
        self.client_id = client_id



async def get_login_form_with_referrer_check(
    request: Request,
    form_data: OAuth2PasswordRequestFormWithClientID = Depends(),

) -> OAuth2PasswordRequestFormWithClientID:
    referrer_url = request.headers.get("referer") or request.headers.get("referrer") or request.headers.get("origin")
    hostname = None

    if referrer_url:
        parsed_referrer = urlparse(referrer_url)
        hostname = parsed_referrer.hostname

    # Fallback if no referrer or origin is found
    if not hostname:
        return form_data

    loggers.debug(f"Original client_id: {form_data.client_id}, Hostname: {hostname}")

    # If client_id is provided, respect it
    if not form_data.client_id=="eko":
        print("client_id", type(form_data.client_id))
        return form_data

    # Extract domain and subdomain
    domain_parts = hostname.split(".")
    if len(domain_parts) >= 3:
        # e.g. abc.xyz.com → subdomain = abc, domain = xyz.com
        subdomain = domain_parts[0]
        domain = ".".join(domain_parts[1:])
    elif len(domain_parts) == 2:
        subdomain = None
        domain = ".".join(domain_parts)
    else:
        return form_data  # Invalid domain

    if subdomain:
        loggers.debug(f"Identified subdomain as a slug: {subdomain}")
        a=get_tenant_id_and_name_from_slug(subdomain)
        form_data.client_id = subdomain


    return form_data


class ChangePasswordRequest(BaseModel):
    old_password: str
    new_password: str

class ResetPasswordRequest(BaseModel):
    subordinate_id: Optional[str] = None

class ExtendedTokenRequest(BaseModel):
    username: str
    password: str
    client_id: str
    days: Union[int, float] = Field(..., description="Number of days for token validity", ge=1, le=365)


# Enhanced authentication models based on Nepali app pattern
class GenericOAuth2Form(BaseModel):
    """Generic OAuth2 form for signup and other operations"""
    username: str = Field(..., description="Username for the account")
    email: Optional[str] = Field(None, description="Email address")
    password: str = Field(..., description="Password for the account")
    full_name: Optional[str] = Field(None, description="Full name of the user")
    phone_number: Optional[str] = Field(None, description="Phone number")
    country_code: Optional[str] = Field(None, description="Country code for phone number")
    client_id: str = Field(..., description="Tenant client ID")


class GoogleAuthRequest(BaseModel):
    """Google authentication request model"""
    id_token: str = Field(..., description="Google ID token")
    client_id: str = Field(..., description="Tenant client ID")


class LoginRequest(BaseModel):
    """Login request model for JSON requests"""
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")
    client_id: str = Field(..., description="Tenant client ID")


class SignupRequest(BaseModel):
    """Signup request model"""
    username: str = Field(..., description="Desired username")
    email: str = Field(..., description="Email address")
    password: str = Field(..., description="Password")
    full_name: Optional[str] = Field(None, description="Full name")
    phone_number: Optional[str] = Field(None, description="Phone number")
    country_code: Optional[str] = Field(None, description="Country code")
    client_id: str = Field(..., description="Tenant client ID")


class AuthResponse(BaseModel):
    """Standardized authentication response"""
    id: str = Field(..., description="User ID")
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    username: str = Field(..., description="Username")
    email: Optional[str] = Field(None, description="Email address")
    role: str = Field(..., description="User role")
    tenant_id: str = Field(..., description="Tenant ID")
    tenant_label: str = Field(..., description="Tenant name/label")
    tenant_slug: str = Field(..., description="Tenant slug")
    full_name: Optional[str] = Field(None, description="Full name")
    profile_picture: Optional[str] = Field(None, description="Profile picture URL")
    auth_provider: str = Field(default="password", description="Authentication provider")
    last_login: Optional[str] = Field(None, description="Last login timestamp")
    previous_login: Optional[str] = Field(None, description="Previous login timestamp")
    phone_number: Optional[str] = Field(None, description="Phone number")
    country_code: Optional[str] = Field(None, description="Country code")
    onboarding_completed: bool = Field(default=False, description="Onboarding completion status")
