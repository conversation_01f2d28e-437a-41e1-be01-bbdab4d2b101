from pydantic import BaseModel, field_validator, Field
from typing import List, Literal, Optional
from datetime import datetime,timedelta
from bson import ObjectId

class Eval_Request(BaseModel):
    message_id: str
    evaluation: Literal["like", "dislike"]
    remark: Optional[str] = None
    categories: Optional[List[Literal["Factually incorrect", "Unhelpful", "Off-topic", "Others"]]]
    reviewer_id: str
    created_at: Optional[datetime] = None
    resolved:bool=False
    resolved_id:Optional[str]=None
    resolved_at:Optional[datetime]=None
    
    # required for dislike 
    @field_validator("remark",mode="before")
    def validate_remark(cls, v, info):
        if info.data.get("evaluation") == "dislike" and not v:
            raise ValueError("Remark is required for dislike")
        return v
    
    @field_validator("categories",mode="before")
    def validate_categories(cls, v,info):
        if info.data.get("evaluation") == "dislike" and not v:
            raise ValueError("Categories are required for dislike")
        return v
    
    @field_validator("created_at",mode="before")
    def validate_created_at(cls, v):
        if v is None:
            return datetime.now()
        return v
    
    @field_validator("resolved",mode="before")
    def validate_resolved(cls, v,info):
        print(info.data.get("evaluation"))
        if info.data.get("evaluation") == "like":
            print("resolved is true")
            return True
        return v
    
    def model_dump_mongo(self):
        dump = self.model_dump()
        dump["_id"] = ObjectId()  # Create a new ObjectId instance
        return dump
    class Config:
        extra = "forbid"
class Fetch_Evaluations(BaseModel):
    search_query: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    category: Optional[List[str]] = None
    reviewer_id: Optional[str] = None
    product: Optional[List[str]] = None
    sentiment: Optional[str] = None
    evaluation: Optional[Literal["like", "dislike"]] = None
    resolved: Optional[bool] = None
    
    page: int = Field(default=1, ge=1, le=100)
    limit: int = Field(default=10, ge=1, le=100)
 
 
    @field_validator("start_date")
    def validate_start_date(cls, v):
        if v:
            # Parse the date and set the time to 00:00:00
            return v.replace(hour=0, minute=0, second=0)
        return None
    
    @field_validator("end_date")
    def validate_end_date(cls, v):
        if v:
            # Parse the date and set the time to 23:59:59
            return v.replace(hour=23, minute=59, second=59)
        return None
    

    
    @classmethod
    def creat_filter_match(cls,fetch_evaluations: "Fetch_Evaluations"):
        match = {}
        if fetch_evaluations.resolved is not None:
            match["resolved"] = fetch_evaluations.resolved
        if fetch_evaluations.start_date and fetch_evaluations.end_date:
            match["created_at"] = {"$gte": fetch_evaluations.start_date, "$lte": fetch_evaluations.end_date}
        if fetch_evaluations.category:
            match["categories"] = {"$in": fetch_evaluations.category}
        if fetch_evaluations.reviewer_id:
            match["reviewer_id"] = fetch_evaluations.reviewer_id
        if fetch_evaluations.product:
            match["products"] = {"$in": fetch_evaluations.product}
        if fetch_evaluations.sentiment:
            match["sentiment"] = fetch_evaluations.sentiment
        if fetch_evaluations.evaluation:
            match["evaluation"] = fetch_evaluations.evaluation
     
            
        if fetch_evaluations.search_query:
            match["$or"] = [
                {"categories": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"reviewer_id": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"products": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"message": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"evaluation": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"remark": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"reply": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"language": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {'sentiment': {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                {"username": {"$regex": fetch_evaluations.search_query, "$options": "i"} },
                
                
                
            ]
        return match
    class Config:
        extra = "forbid"

class Update_Evaluation(BaseModel):
    message_id: str
    class Config:
        extra = "forbid"
        
        
        
class Flag_Evaluations(BaseModel):
    evaluation_id: str
    resolved:bool
    resolved_id:Optional[str]
    class Config:
        extra = "forbid"
