from pydantic import BaseModel,field_validator,validator
from bson.objectid import ObjectId
from src.models.chat_history import ChatHistMessage
from typing import List, Any,Optional,Literal
from datetime import datetime
from src.helper import get_DB
data=[
      {
        "role": "agent",
        "content": "ambition guru facebook page follow garnu hajur lai class ko barema inform garne chau"
      },
      {
        "role": "user",
        "content": "Vayo"
      },
      {
        "role": "agent",
        "content": "Hus. Happy learning."
      },
      {
        "role": "user",
        "content": "Class kati baje ho notice xena ako"
      },
      {
        "role": "agent",
        "content": "नमस्कार Niru Pokhrel !  के तपाईं जापानी भाषाको तयारी गर्न चाहानुहुन्छ ? यही चैत्र १ गते (बिहिबार) देखी N4 Level को नयाँ कक्षा सुरु भएको छ ।"
      },
      {
        "role": "user",
        "content": "No. Yas ma <PERSON>in ko loksewapanix ta"
      }
]

class ModelRunRequest(BaseModel):
    user_name: str | None="test_eko"
    user_id: int | None=0
    message: str
    chat_data: Any|None=[]
    chat_data_format: Literal["role_data","role_content"]
    primary_product_code: str | None=None
    primary_product: str | None=None
    media_ids: List[str] | None = None
    media_values: Optional[Any] | None = None
    tags: List[str] | None = ["Facebook"]

    @field_validator("message")
    def validate_message(cls, v):
        if not v.strip():  # Check if the message is empty or contains only whitespace
            raise ValueError("Message cannot be empty")
        return v
    
class ModelRunResponse(BaseModel):
    response_time: str
    reply: str|None
    suggested_reply: str|None
    retreival_reply: str|None
    identified_product_id:list|None=[]
    categories_id:list|None=[]
    call_to_action:list
    contextualize: str
    summary_id: str
    categories: Any
    formatted_summary: str
    identified_product: list
    sentiment: str
    language: str
    relevance: dict
    faithfulness: dict
    verified: bool = True  
    verified_by: Optional[str] = None 
    # verify_flag: bool = False
    chat_message_id: str|None = None
    suggested_images: list|None = []

    @validator('verified', pre=True, always=True)
    def set_verified(cls, v, values):
        if values.get('call_to_action') and len(values.get('call_to_action')) > 0:
            return False
        return v  

    @validator('verified_by', pre=True, always=True)
    def set_verified_by(cls, v, values):
        if values.get('call_to_action') and len(values.get('call_to_action')) > 0:
            return None
        return v  

class ModelResponse(BaseModel):
    request: ModelRunRequest
    response: ModelRunResponse


    
    def model_dump_mongo(self, db):
        """
        Prepares the model data for MongoDB storage.

        Returns:
            dict: MongoDB-compatible data.
        """
        dump = self.model_dump()
        summary_id = dump.get("response", {}).get("summary_id")
        if summary_id and ObjectId.is_valid(summary_id):
            dump["response"]["summary_id"] = ObjectId(summary_id)
            chat_message = db["chat_messages"].find_one_and_update({"_id": ObjectId(summary_id)},{"$addToSet":{"chat_ids":ObjectId(dump["response"].get("chat_message_id"))}})

            
            if chat_message:
                dump["request"]["incomming_chat"] = dump["request"]["chat_data"]
                dump["request"]["chat_data"] = chat_message.get("chat_ids")
                if dump["response"].get("chat_message_id") :
                  dump["request"]["chat_data"].append(ObjectId(dump["response"].get("chat_message_id")))
                  

        dump["created_at"] = datetime.now()
        return dump


class MessageVerificationRequest(BaseModel):
  response_id: str
  is_edited:bool = False
  verified: bool = False
  verified_answer: str = ""
  verified_by: str | None = "Human"
  user_id: int|None = 0
  

class AgentReplyRequest(BaseModel):
  response_id: str
  content: str
  sender: str
  user_id: int



class FollowUpRequest(BaseModel):
  message_id:str
  follow_up_msg:str