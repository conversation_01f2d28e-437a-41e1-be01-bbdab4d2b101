from pydantic import BaseModel, field_validator, Field, model_validator
from typing import List, Optional, Literal, Any, Union, Dict
from bson.objectid import ObjectId
from datetime import datetime
from hashlib import sha256
import asyncio
from enum import Enum

# from src.handle_image.extract_text_from_image import extract_text
from src.v2.chat.handle_image.extract_text_standalone import extract_text
from src.helper import get_base_url, change_port
from src.helper.logger import setup_new_logging
from src.models.user import UserTenantDB
from src.v2.dashboard.cta.models import CTAType
from src.helper.resolve_llm import resolve_llm
from datetime import datetime
import re

logger = setup_new_logging(__name__)


from contextvars import ContextVar
from typing import Optional

class CurrentUserContext:
    """Context class to store and access the current user globally."""
    _current_user: UserTenantDB = ContextVar("current_user", default=None)

    @classmethod
    def set_current_user(cls, user: Any):
        """Set the current user in the context."""
        cls._current_user.set(user)

    @classmethod
    def get_current_user(cls) -> Optional[Any]:
        """Get the current user from the context."""
        return cls._current_user.get()

    @classmethod
    def clear_current_user(cls):
        """Clear the current user from the context."""
        cls._current_user.set(None)

class ChatDataFormat(str, Enum):
    """Enum for supported chat data formats"""

    ROLE_DATA = "role_data"
    ROLE_CONTENT = "role_content"


class MessageRole(str, Enum):
    """Enum for message roles"""

    USER = "user"
    SYSTEM = "system"
    ASSISTANT = "assistant"
    AGENT = "agent"  # Will be normalized to ASSISTANT

    def __str__(self):
        """Return the string value of the enum."""
        return self.value


class ChatHistMessage(BaseModel):
    """
    Represents a message in the chat history.

    Attributes:
        role (MessageRole): Role of the message sender.
        content (str): Message content.
        sender (str): Name of sender.
        created_at (datetime): Timestamp when the message was created.
        user_id (Any): ID of the user who sent or received the message.
        media_ids (List): List of media IDs associated with the message.
        media_values (str): Extracted text from media.
        summary_id (str): ID of the summary the message belongs to if it does.
        id (str): Unique identifier for the message.
        chat_ids (List[str]): If this chat message is a summary (system role),
                            chat_ids are the chat messages that are summarized.
        verified_by (str): ID of user who verified the message.
        verified_at (datetime): Timestamp when the message was verified.
        verified (bool): Whether the message has been verified.
        message_id (str): Alternative message identifier.
        current_user (Any): Reference to the current user.
    """


    role: MessageRole
    content: Optional[str] = ""
    sender: str
    created_at: Optional[datetime] = None
    user_id: Optional[Any] = None
    media_values: Optional[str] = None
    id: Optional[str] = None
    chat_ids: Optional[List[str]] = None
    message_id: Optional[str] = None
    current_user: Optional[Any] = None
    image_process_metadata: Optional[Dict[str, Any]] = None
    ai_enabled: Optional[bool] = True
    has_credit: Optional[bool] = True
    media_ids: List[str] = []
    # summary_id: Optional[str] = None
    verified_by: Optional[str] = None
    verified_at: Optional[datetime] = None
    verified: Optional[bool] = True

    # Set up model config for better validation
    model_config = {
        "arbitrary_types_allowed": True,
        "populate_by_name": True,
        "str_strip_whitespace": True,
    }

    @field_validator("id", mode="before")
    @classmethod
    def convert_object_id_to_str(cls, v):
        """Convert ObjectId to string if needed."""
        if isinstance(v, ObjectId):
            return str(v)
        return v

    @field_validator("content")
    @classmethod
    def validate_content_field(cls, v):
        """
        Ensures the `content` field is a valid string or None.
        Converts input to string if possible, or raises a validation error.
        """
        if v is None:
            return ""  # Return an empty string if content is None
        if isinstance(v, str):
            return v  # Content is already a valid string
        elif hasattr(v, "role") and hasattr(
            v, "content"
        ):  # If it'usages a ChatMessage object
            return str(v.content)  # Extract and return the content
        else:
            try:
                return str(v)  # Try to convert to string
            except Exception:
                raise ValueError(
                    f"Invalid content type: expected string or ChatMessage-like object, got {type(v).__name__}"
                )

    @field_validator("role", mode="before")
    @classmethod
    def normalize_role(cls, v):
        """Normalize role values, converting 'agent' to 'assistant'."""
        if v == "agent":
            return "assistant"
        return v

    @field_validator("created_at", mode="before")
    @classmethod
    def ensure_created_at(cls, v):
        """Ensure created_at has a value."""
        if v is None:
            return datetime.now()
        return v

    @field_validator("sender")
    @classmethod
    def validate_sender(cls, v, info):
        """Ensure sender has a valid value based on role."""
        if isinstance(v, str) and v.strip():
            return v

        # If sender is empty, set default based on role
        role = info.data.get("role")
        if role == "user":
            return "unknown"
        else:
            return "assistant"

    @model_validator(mode="before")
    @classmethod
    def process_media(cls, values):
        """
        Processes media_ids to validate and generate media_values before finalizing the model.
        """
        current_user = CurrentUserContext.get_current_user()  # Retrieve current_user from context
        media_ids = values.get("media_ids", [])

        # return values
        # Early return conditions
        if (
            not media_ids
            or not isinstance(media_ids, list)
            or values.get("media_values")
        ):
            return values

        try:
            # Filter valid URLs
            valid_media_ids = [
                get_base_url(url)
                for url in media_ids
                if isinstance(url, str)
                and (url.startswith("http://") or url.startswith("https://"))
            ]

            logger.debug(f"Valid media IDs: {valid_media_ids}")

            if not valid_media_ids:
                logger.warning(f"No valid media IDs found: {media_ids}")
                values["media_ids"] = []
                return values

            # Extract text from images asynchronously
            text_from_image, job_images, usage_metadata = asyncio.run(
                extract_text(current_user, valid_media_ids)
            )
            logger.debug(
                f"Extracted text from image: {text_from_image[:100] if text_from_image else 'None'}"
            )

            # Update port for media URLs
            processed_media_ids = [change_port(url, 8402) for url in valid_media_ids]

            values["media_ids"] = job_images or processed_media_ids
            values["media_values"] = text_from_image

            # Store usage metadata for cost calculation
            if usage_metadata:
                values["image_process_metadata"] = usage_metadata

        except Exception as e:
            logger.error(f"Error processing media IDs: {e}")
            values["media_values"] = None

        return values

    @field_validator("id")
    @classmethod
    def hash_id_from_role_content(cls, v, info):
        """
        Validates or generates a unique ID for the ChatHistMessage.
        """
        # If ID is already provided, use it
        if v:
            return v if isinstance(v, str) else str(v)

        # Generate a new ID based on message attributes
        return str(ObjectId())

    @classmethod
    def from_role_data_format(
        cls, current_user, user_id, chat_history: List[dict]
    ) -> List["ChatHistMessage"]:
        """
        Converts raw chat history in role_data format into ChatHistMessage objects.

        Args:
            current_user: Current user reference
            user_id: ID of the user
            chat_history: List of raw chat data dictionaries

        Returns:
            List[ChatHistMessage]: A list of ChatHistMessage objects
        """
        messages = []

        if not chat_history:
            return messages

        for chat in chat_history:
            role = chat.get("role")
            chat_datas = chat.get("data")

            # Handle various data formats
            if not chat_datas:
                continue

            if isinstance(chat_datas, dict):
                chat_datas = [chat_datas]

            if not isinstance(chat_datas, list):
                continue

            for chat_data in chat_datas:
                if not isinstance(chat_data, dict):
                    continue

                # Handle media content
                media_ids = chat_data.get("media_ids", [])
                if media_ids and not chat_data.get("content"):
                    chat_data["content"] = "Answer This"

                # Parse created_at field properly
                created_at_raw = chat_data.get("created_at")
                created_at = datetime.now()  # Default fallback

                if created_at_raw:
                    try:
                        if isinstance(created_at_raw, str):
                            # Try to parse different datetime formats
                            for fmt in ["%Y-%m-%d %H:%M:%S", "%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S.%f"]:
                                try:
                                    created_at = datetime.strptime(created_at_raw, fmt)
                                    break
                                except ValueError:
                                    continue
                        elif isinstance(created_at_raw, datetime):
                            created_at = created_at_raw
                    except Exception as e:
                        logger.warning(f"Failed to parse created_at '{created_at_raw}': {e}")
                        created_at = datetime.now()

                # Create message with essential attributes
                message_args = {
                    "current_user": current_user,
                    "role": role,
                    "sender": chat_data.get("sender")
                    or ("unknown" if role == "user" else "assistant"),
                    "content": chat_data.get("content"),
                    "user_id": user_id,
                    "id": chat_data.get("id"),
                    "media_ids": media_ids,
                    "created_at": created_at,
                }

                # Create and append message
                try:
                    message = cls(**message_args)
                    # print(f"message: {message}")
                    messages.append(message)
                except Exception as e:
                    logger.error(f"Error creating message: {e}")
        return messages

    @classmethod
    def from_role_content_format(
        cls, current_user, user_id, chat_history: List[dict]
    ) -> List["ChatHistMessage"]:
        """
        Formats a list of role-content format messages as ChatHistMessage objects.

        Args:
            current_user: Current user reference
            user_id: ID of the user
            chat_history: List of messages in role-content format

        Returns:
            List[ChatHistMessage]: The created message objects
        """
        messages = []

        if not chat_history:
            return messages

        for chat in chat_history:
            if not isinstance(chat, dict):
                continue

            # Get media from the message
            media_ids = chat.get("media", [])

            # Create message with direct mapping
            try:
                message = cls(
                    current_user=current_user,
                    role=chat.get("role"),
                    content=chat.get("content", ""),
                    sender=chat.get("sender")
                    or ("unknown" if chat.get("role") == "user" else "assistant"),
                    created_at=chat.get("created_at") or datetime.now(),
                    id=chat.get("id"),
                    user_id=user_id,
                    media_ids=media_ids,
                )
                messages.append(message)
            except Exception as e:
                logger.error(f"Error creating message from role_content format: {e}")

        return messages

    @classmethod
    def system_message(cls, generated_summary, user_id, chat_ids, old_summary_id=None):
        """
        Creates a ChatHistMessage object for system messages.

        Args:
            generated_summary: The generated summary content
            user_id: ID of the user the message belongs to
            chat_ids: List of chat IDs
            old_summary_id: ID of previous summary if this is an update

        Returns:
            ChatHistMessage: A validated message object
        """
        # Ensure content is a string
        if not isinstance(generated_summary, str):
            try:
                generated_summary = generated_summary.content
            except AttributeError:
                generated_summary = str(generated_summary)

        return cls(
            id=None,
            role="system",
            content=generated_summary,
            sender="system",
            created_at=datetime.now(),
            chat_ids=[str(chat_id) for chat_id in chat_ids],
            user_id=user_id,
            summary_id=old_summary_id,
        )

    @classmethod
    def format_message(cls, role, content, media_ids, media_values, user_id, ai_enabled=True):
        """
        Formats the message for display or storage.

        Args:
            role: Role of the message sender
            content: Message content
            media_ids: List of media IDs
            media_values: Extracted text from media
            user_id: ID of the user
            ai_enabled: Whether AI was enabled for this message

        Returns:
            dict: Formatted message data
        """
        sender="user" if role=="user" else "assistant"
        return cls(
            id=None,
            role=role,
            content=content,
            media_ids=media_ids,
            media_values=media_values,
            user_id=user_id,
            sender=sender,
        )


    def model_dump(self):
        """
        Dumps the model data into a dictionary for serialization.

        Returns:
            dict: Serialized model data
        """
        dump_ = super().model_dump()

        # Rename id to _id for MongoDB
        dump_["_id"] = dump_.pop("id")

        # Process media IDs
        if dump_.get("media_ids"):
            dump_["media_ids"] = [
                get_base_url(url) for url in dump_["media_ids"] if isinstance(url, str)
            ]

        # Handle None values
        if not dump_.get("created_at"):
            dump_["created_at"] = datetime.now()

        return dump_
    def model_dump_promp(self):

        return {"role":self.role.__str__(),"content":self.content}

    def model_dict_format(self):
        """Remove any ObjectId or anything to insert into MongoDB as chat data."""
        data = self.model_dump()  # Use model_dump() instead of self.dict()
        if "_id" in data:
            data["_id"] = str(data["_id"])
            data.pop("current_user", None)
        return data


    def model_dump_mongo(self):
        """
        Prepares the model data for MongoDB storage.

        Returns:
            dict: MongoDB-compatible data
        """
        dump = self.model_dump()

        # Convert chat_ids to ObjectId
        if dump.get("chat_ids"):
            dump["chat_ids"] = [
                ObjectId(item) if isinstance(item, str) else item
                for item in dump["chat_ids"]
            ]

        # Ensure created_at is set
        if not dump.get("created_at"):
            dump["created_at"] = datetime.now()

        # Remove current_user reference to avoid serialization issues
        dump.pop("current_user", None)

        # Ensure _id is an ObjectId
        # if dump.get("_id"):
        #     dump["_id"] = ObjectId(dump["_id"])

        # Handle _id field properly to avoid null _id duplicate key errors
        if dump.get("_id") is not None:
            # Convert existing _id to ObjectId
            dump["_id"] = ObjectId(dump["_id"])
        else:
            # Remove _id field entirely to let MongoDB auto-generate it
            # This prevents the duplicate key error with _id: null
            dump.pop("_id", None)

        return dump

    def format_for_display(self):
        """
        Formats the chat message for display.

        Returns:
            str: The formatted chat message
        """
        role = self.role.capitalize()
        sender = self.sender.capitalize()
        content = self.content or ""

        # Add media indicator if present
        media_indicator = " [with media]" if self.media_ids else ""

        # Truncate very long content for display
        if len(content) > 100:
            content = content[:97] + "..."

        return f"{role} ({sender}){media_indicator}: `{content}`"


class ModelRunRequest(BaseModel):
    """
    Request model for running the chat model.

    Attributes:
        user_name: Name of the user
        user_id: ID of the user
        chat_data: Previous chat history
        chat_data_format: Format of the chat data (role_data or role_content)
        primary_product_code: Product code if applicable
        primary_product: Product name if applicable
        media_ids: List of media IDs (not used directly, kept for backward compatibility)
        media_values: Extracted text from media (not used directly, kept for backward compatibility)
        tags: List of tags for categorization
    """
 
    user_name: str = "Developer"
    user_id:str = "0"
    message: Optional[str] = ""
    chat_data: List[Dict[str, Any]] = []
    chat_data_format: ChatDataFormat = ChatDataFormat.ROLE_CONTENT
    # primary_product_code: Optional[str] = None
    # primary_product: Optional[str] = None
    media_ids: List[str] = []
    media_values: Optional[str] = None  # Kept for backward compatibility
    mode:Optional[Literal["simplified","detailed","elaborated"]]="elaborated"
    channel:Optional[Literal["Website","Whatsapp","Facebook","Playground","Instagram","Telegram","SMS"]]="Playground"
    message_topic:Optional[List[str]] = None
    profile_id:Optional[str] = None
    from_number:Optional[str] = None
    country_code:Optional[str] = None
    phone_number:Optional[str] = None
    email:Optional[str] = None

    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "user_name": "admin",
                    "user_id": "6704ac1803549df7f12c776e",
                    "chat_data": [
                        {
                            "role": "user",
                            "data": {
                                "content": "what are the risk",
                                "created_at": None,
                            },
                        }
                    ],
                    "channel":"Whatsapp",
                    "profile_id":"1",
                    "chat_data_format": "role_data",
                    "media_values": "string",
                    "tags": ["Facebook"],
                    "channel":"Playground",
                    
                }
            ]
        }
    }


    @field_validator("chat_data")
    @classmethod
    def validate_chat_data(cls, v):
        """Ensure chat_data is a list."""
        if v is None:
            return []
        if not isinstance(v, list):
            return []
        return v

    async def get_formatted_messages(self, current_user=None) -> List[ChatHistMessage]:
        """
        Convert request data into ChatHistMessage objects.

        Args:
            current_user: Optional current user object reference

        Returns:
            Tuple[List[ChatHistMessage], ChatHistMessage, dict]:
            (chat_data_messages, latest_message, image_process_metadata)
        """
        if not current_user:
            current_user = CurrentUserContext.get_current_user()

        # Convert all chat data to ChatHistMessage objects
        if self.chat_data_format == ChatDataFormat.ROLE_DATA:
            all_messages = ChatHistMessage.from_role_data_format(
                current_user, self.user_id, self.chat_data
            )
        else:
            all_messages = ChatHistMessage.from_role_content_format(
                current_user, self.user_id, self.chat_data
            )

        # Ensure we have messages
        # if not all_messages or len(all_messages) == 0:
        #     logger.error("No messages found in request")
        #     # Create a dummy message to avoid errors
        #     dummy_message = ChatHistMessage(
        #         role="user",
        #         content=self.message if self.message else "",  # Use message from request if available
        #         sender="user",
        #         created_at=datetime.now(),
        #         user_id=self.user_id
        #     )
        #     all_messages = [dummy_message]
        #     logger.info(f"Created dummy message with content: {dummy_message.content}")

        # Sort all messages by created_at timestamp (oldest first for chat_data)
        try:
            all_messages.sort(key=lambda x: x.created_at)
        except Exception as e:
            logger.error(f"Error sorting messages by created_at: {e}")

        # Find the latest user message (most recent created_at)
        user_messages = [msg for msg in all_messages if msg.role == "user"]
        latest_message = None

        if user_messages:
            try:
                # Sort user messages by created_at and get the latest one
                user_messages.sort(key=lambda x: x.created_at, reverse=True)
                latest_message = user_messages[0]
            except Exception as e:
                logger.error(f"Error finding latest user message: {e}")
                latest_message = user_messages[0]  # Fallback to first user message
        else:
            # If no user messages, create a fallback
            latest_message = ChatHistMessage(
                role="user",
                content=self.message if self.message else "",
                sender="user",
                created_at=datetime.now(),
                user_id=self.user_id
            )
            logger.warning("No user messages found, created fallback latest message")

        # Fetch chat history from db - limit to 5 most recent conversations
        messages: List[ChatHistMessage] = []
        db = current_user.async_db
        cursor = db.ai_response.find({"request.user_id": self.user_id}).sort("created_at", -1).limit(5)
        chat_history = await cursor.to_list(length=5)  # Convert to list properly

        # Process chat history in reverse order (oldest first)
        chat_history = chat_history[::-1]

        # Limit the total number of messages to avoid context length issues
        max_messages = 10
        message_count = 0

        if chat_history:
            for chat_doc in chat_history:
                # Stop if we've reached the maximum number of messages
                if message_count >= max_messages:
                    break

                chat_data = chat_doc.get("response", {}).get("chat_data", [])

                # Skip empty chat data
                if not chat_data:
                    continue

                # Convert to ChatHistMessage objects
                try:
                    chat_messages = [ChatHistMessage(**chat) for chat in chat_data]

                    # Add messages up to the maximum
                    remaining_slots = max_messages - message_count
                    if len(chat_messages) <= remaining_slots:
                        messages.extend(chat_messages)
                        message_count += len(chat_messages)
                    else:
                        # If we can't add all messages, prioritize the most recent ones
                        messages.extend(chat_messages[-remaining_slots:])
                        message_count = max_messages
                except Exception as e:
                    logger.error(f"Error processing chat data: {e}")

        
        if len(all_messages) <=1:
            all_messages = messages
        # Get image processing metadata if available
        image_process_metadata = latest_message.image_process_metadata if hasattr(latest_message, 'image_process_metadata') else {}

        # Log information about the messages being returned
 
        # print(f"all_messages: {all_messages}")
        # logger.info(f"Returning {len(all_messages)} current chat messages, {len(messages)} chat history messages")
        logger.info(f"Latest message content: {latest_message.content}")
        # logger.info(f"Latest message created_at: {latest_message.created_at}")

        # Return: (current chat data sorted by time, latest user message, image metadata)
        return all_messages, latest_message, image_process_metadata

    async def get_latest_message(self) -> Optional[ChatHistMessage]:
        """
        Get the latest user message in the request.

        Returns:
            Optional[ChatHistMessage]: The most recent user message or None if no user messages exist.
        """
        # Get formatted messages
        result = await self.get_formatted_messages()

        # Unpack the result (messages, latest_message, image_process_metadata)
        messages = result[0] if isinstance(result, tuple) and len(result) > 0 else []

        if not messages:
            return None

        # Normalize all datetime objects to ensure they're comparable
        for msg in messages:
            # Convert offset-aware to offset-naive datetime if needed
            if msg.created_at and msg.created_at.tzinfo is not None:
                msg.created_at = msg.created_at.replace(tzinfo=None)

        # Filter for user messages and sort by created_at
        user_messages = [msg for msg in messages if msg.role == "user"]

        if not user_messages:
            return None

        # Return the most recent user message
        return sorted(
            user_messages, key=lambda x: x.created_at or datetime.min, reverse=True
        )[0]

    def add_message(
        self, content: str, role: str = "user", media_ids: List[str] = None
    ) -> None:
        """
        Add a new message to the chat data.

        Args:
            content: Message content
            role: Message role (user, assistant, system)
            media_ids: Optional list of media URLs
        """
        new_message = {
            "role": role,
            "content": content,
            "created_at": datetime.now(),
        }

        if media_ids:
            new_message["media"] = media_ids

        self.chat_data.append(new_message)


class InformationGathering(BaseModel):
    function_name: str
    function_args: Dict[str, Any]
    result: Dict[str, Any]


    @model_validator(mode="before")
    @classmethod
    def modify_result_for_issues(cls, values):
        """
        Modify the result based on the function name.
        """
        function_name = values.get("function_name")
        function_args = values.get("function_args")
        result = values.get("result")

        if function_name == "create_issue_tickets":
            # Modify the result as needed
            result = {"Message": "Call to Action Added Sucessfully!"}
            # function_args.pop("issue_type")

        if function_name == "handle_booking":
            # Modify the result as needed
            function_args["issue_type"] = CTAType.BOOKING.value

        values["result"] = result
        values["function_args"] = function_args
        return values

class ModelRunResponse(BaseModel):
    request_time: datetime
    processing_time: float
    reply: str | None
    reply_urls: List[str] = []  # Enhanced to ensure all URLs are included as strings
    # identified_product: str | None
    information_gathering: List[InformationGathering] | None = []
    # products: str | None = "Breast Implant"
    # sentiment: str | None = "Neutral"
    language: str | None = "English"
    chat_ids: List[str] = []
    chat_data: List[Dict[str, Any]] = []
    metadata: List[Dict[str, Any]] = []
    call_to_action: List[Dict[str, Any]] = []
    image_process_cost: Optional[Dict[str, Any]] = {}
    ai_enabled: Optional[bool] = True
    has_credit: Optional[bool] = True
    # Additional fields for enhanced response
    source_nodes: List[Dict[str, Any]] = []
    background_processing: Optional[bool] = True
    background_processing_completed: Optional[bool] = False
    latest_message: Optional[str] = None
    usage: Optional[Dict[str, Any]] = {}

    @field_validator("call_to_action")
    def remove_none_values(cls, v):
        """Remove None values from the list."""
        if v is None:
            return []
        return [item for item in v if item is not None]

    @field_validator("reply_urls")
    def ensure_reply_urls_list(cls, v):
        """Ensure reply_urls is always a list of strings."""
        if v is None:
            return []
        if isinstance(v, str):
            return [v]
        if isinstance(v, list):
            # Filter out None values and ensure all items are strings
            return [str(url) for url in v if url is not None]
        return []


class EnhancedModelRunResponse(ModelRunResponse):
    """
    Enhanced ModelRunResponse with additional fields for comprehensive response data.
    This class extends the base ModelRunResponse to include all necessary information
    like identified product, sentiment, reply, and comprehensive reply_urls list.
    """

    # Override to ensure proper typing and validation
    reply_urls: List[str] = []  # All URLs including resource URLs and image URLs
    # identified_product: Optional[str] = None  # Product identified from the conversation
    # sentiment: Optional[str] = "Neutral" 
    reply: Optional[str] = None  # AI generated reply

    # Additional enhanced fields
    tool_calls_results: List[Dict[str, Any]] = []  # Results from tool calls
    extracted_urls: List[str] = []  # URLs extracted from source nodes
    image_urls: List[str] = []  # Image URLs from MinIO
    resource_urls: List[str] = []  # Resource URLs from metadata

    @field_validator("reply_urls", mode="after")
    def consolidate_all_urls(cls, v, info):
        """Consolidate all URLs from different sources into reply_urls."""
        all_urls = set(v) if v else set()

        # Add URLs from other fields if they exist
        data = info.data
        if data.get("extracted_urls"):
            all_urls.update(data["extracted_urls"])
        if data.get("image_urls"):
            all_urls.update(data["image_urls"])
        if data.get("resource_urls"):
            all_urls.update(data["resource_urls"])

        # Convert back to list and filter out empty strings
        return [url for url in all_urls if url and isinstance(url, str)]

    def model_dump(self, **kwargs):
        """Override model_dump to ensure proper serialization."""
        data = super().model_dump(**kwargs)

        # Ensure reply_urls contains all URLs
        all_urls = set(data.get("reply_urls", []))
        all_urls.update(data.get("extracted_urls", []))
        all_urls.update(data.get("image_urls", []))
        all_urls.update(data.get("resource_urls", []))

        data["reply_urls"] = [url for url in all_urls if url and isinstance(url, str)]

        return data


class ReplyResponse(BaseModel):
    request: ModelRunRequest
    response: ModelRunResponse

    def __init__(self, **data):
        super().__init__(**data)


    def model_dump_mongo(self):
        """
        Prepares the model data for MongoDB storage.

        Returns:
            dict: MongoDB-compatible data
        """
        dump = self.model_dump()

        # Ensure request_time is in ISO format
        if dump.get('request_time'):
            dump['request_time'] = dump['request_time'].isoformat()
        return dump
