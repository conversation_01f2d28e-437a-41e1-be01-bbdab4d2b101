from typing import Any, Dict, List, Literal, Optional
from pydantic import BaseModel, Field
from src.helper import logger

loggers=logger.setup_new_logging(__name__)
class Products(BaseModel):
    id: str = Field(alias="_id")
    course: str
    status: Optional[Literal["active", "inactive"]] = None  
    description: Optional[str] = None  
    documents: List[Any] = []
    document: Optional[Any] = None
    product_code: Optional[str] = None
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed=True
        
    @classmethod
    def return_products(cls, record: Dict[str,Any]) -> "Products":
        return Products(
            id=str(record.get("_id")),
            course=record.get("course"),
            status=record.get("status"),
            description=record.get("description")
        )   