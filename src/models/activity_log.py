"""
Pydantic models for activity logging.
"""
from typing import Dict, List, Optional, Any, Union, Annotated, Type
from pydantic import BaseModel, Field, GetCoreSchemaHandler, GetJsonSchemaHandler
from pydantic.json_schema import JsonSchemaValue
from pydantic_core import core_schema
from datetime import datetime
from bson import ObjectId


class PyObjectId(ObjectId):
    """Custom type for handling MongoDB ObjectId fields in Pydantic models."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_core_schema__(cls, _source_type: Type[Any], _handler: GetCoreSchemaHandler) -> core_schema.CoreSchema:
        return core_schema.union_schema([
            core_schema.is_instance_schema(ObjectId),
            core_schema.chain_schema([
                core_schema.str_schema(),
                core_schema.no_info_plain_validator_function(cls.validate),
            ])
        ])

    @classmethod
    def __get_pydantic_json_schema__(cls, _schema_generator: GetJsonSchemaHandler, _field_schema: JsonSchemaValue) -> JsonSchemaValue:
        return {"type": "string", "format": "objectid"}

class ActivityData(BaseModel):
    """
    Model for individual activity data entries.
    """
    path: str
    method: str
    query_params: Optional[Dict[str, Any]] = Field(default_factory=dict)
    request_body: Optional[Any] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)

    model_config = {
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str
        },
        "arbitrary_types_allowed": True
    }

class ActivityLog(BaseModel):
    """
    Main activity log model that stores user activity information.
    """
    id: Annotated[PyObjectId, Field(default_factory=PyObjectId, alias="_id")]
    user_id: str
    username: Optional[str] = None
    email: Optional[str] = None
    tenant_id: str
    created_at: datetime = Field(default_factory=datetime.now)
    last_active_at: datetime = Field(default_factory=datetime.now)
    data: List[ActivityData] = Field(default_factory=list)

    model_config = {
        "populate_by_name": True,
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str,
            datetime: lambda dt: dt.isoformat()
        },
        "arbitrary_types_allowed": True
    }

class ActivityLogCreate(BaseModel):
    """
    Model for creating a new activity log entry.
    """
    user_id: str
    username: Optional[str] = None
    email: Optional[str] = None
    tenant_id: str
    activity_data: ActivityData
    id: Annotated[PyObjectId, Field(default_factory=PyObjectId, alias="_id")]

    model_config = {
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str
        },
        "arbitrary_types_allowed": True
    }

class ActivityLogUpdate(BaseModel):
    """
    Model for updating an existing activity log entry.
    """
    last_active_at: Optional[datetime] = None
    new_activity: Optional[ActivityData] = None

    model_config = {
        "json_encoders": {
            ObjectId: str,
            PyObjectId: str
        },
        "arbitrary_types_allowed": True
    }