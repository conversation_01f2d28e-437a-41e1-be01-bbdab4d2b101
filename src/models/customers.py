from datetime import datetime

from pydantic import BaseModel, Field, root_validator
from typing import Optional, Dict, Union
from bson import ObjectId
from src.models.chat_history import ChatHistMessage
class CustomerModel(BaseModel):
    id: Union[str, ObjectId] = Field(..., alias="_id")
    customer_id: int
    name: str
    additional_information: Dict
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        populate_by_name = True
        arbitrary_types_allowed=True

    @classmethod
    def from_id(cls, id, db) -> "CustomerModel":
        customer = db.customers.find_one({"_id": ObjectId(id)})
        if customer:
            return cls(**customer)
        return None

    @root_validator(pre=True)
    def convert_objectid_to_string(cls, values):
        if "_id" in values and isinstance(values["_id"], ObjectId):
            values["_id"] = str(values["_id"])
        return values

    def update_in_db(self, db):
        db.customers.update_one({"_id": ObjectId(self.id)}, {"$set": self.dict(exclude={"id"})})

    def set_additional_info(self, key, value, db):
        self.additional_information[key] = value
        self.update_in_db(db)

    
    @classmethod
    def format_chat_history(cls, current_user, user_id, chat_history,format):
        # Initialize formatted_history as an empty list
        formatted_history = []

        # # Fetch the most recent chat history for the user
        # old_history = db.ai_response.find_one({"request.user_id": user_id}, sort=[("created_at", -1)])
        old_history=list(current_user.db.chat_messages.find({"user_id": user_id}, sort=[("created_at", -1)],limit=2))
        old_history=[]

        # If old_history exists, retrieve its 'incomming_chat' field
        if old_history:
            formatted_history = old_history
            if format=="role_content":
                formatted_history=ChatHistMessage.from_role_content_format(current_user,user_id,formatted_history)
            elif format=="role_data":
                formatted_history=ChatHistMessage.from_role_data_format(current_user,user_id,formatted_history)
            if formatted_history is None:
                formatted_history = []


        # Append the new chat history
        formatted_history.append(chat_history)

        return formatted_history
    @staticmethod
    def fetch_chat_hist(request,current_user):
        if request.chat_data_format == "role_data":
            user_chat_history=ChatHistMessage.from_role_data_format(current_user,request.user_id,request.chat_data)
        elif request.chat_data_format == "role_content":
            user_chat_history=ChatHistMessage.from_role_content_format(current_user=current_user,user_id=request.user_id, chat_history=request.chat_data)
        return user_chat_history
    
    def model_dump(self):
        dump_ = super().model_dump()
        dump_['_id'] =str( dump_.pop('id'))
        return dump_

class AllCustomersRequest(BaseModel):
    start_date: str
    end_date: str