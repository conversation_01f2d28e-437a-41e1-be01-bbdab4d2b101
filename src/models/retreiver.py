from typing import List,Optional
from pydantic import BaseModel,validator
class AddToIndexRequest(BaseModel):
    data_name: str
    documents: Optional[List[str]] = None
    vector_token: Optional[str] = "5a774894-c076-4117-ba20-b5cb53153d14"

    
class QueryEngineRequest(BaseModel):
    token: str|None="5a774894-c076-4117-ba20-b5cb53153d14"
    query_engine_id: str|None="662633f8-0adf-4720-8367-aa827d17cb78"
    query: str|None="price kati hola"



class MetadataFilter(BaseModel):
    key: str
    operator: str = "IN"
    value: str


class CreateQueryEngineRequest(BaseModel):
    token: str
    query_engine_name: str
    query: Optional[str] = None
    product: Optional[str] = None
    category: Optional[List[str]] = None
    metadata_filters: Optional[List[MetadataFilter]] = None

    def create_metadata_filters(self):
        """
        Dynamically create metadata filters for 'product' and 'category'
        if they are provided in the request.
        """
        filters = []

        if self.product:
            filters.append(MetadataFilter(key="product", value=self.product))

        if self.category:
            for cat in self.category:  # Iterate through the list of categories
                filters.append(MetadataFilter(key="category", value=cat))
        return filters

    # Override the `metadata_filters` to automatically include product/category filters
    def dict(self, *args, **kwargs):
        data = super().dict(*args, **kwargs)
        # Only set metadata_filters if it's not provided by the user
        if not data.get("metadata_filters"):
            data["metadata_filters"] = self.create_metadata_filters()
        return data

