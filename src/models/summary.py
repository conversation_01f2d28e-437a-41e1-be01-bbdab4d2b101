from pydantic import BaseModel
from typing import List, <PERSON><PERSON>, Optional
from bson import ObjectId
from collections import deque
from src.models.chat_history import ChatHistMessage
from src.helper import logger
from llama_index.core.memory import ChatSummaryMemoryBuffer
from llama_index.llms.openai import OpenAI
import tiktoken

# Set up logging
loggers = logger.setup_new_logging(__name__)

class SummaryModel(BaseModel):
    chatHistory: List[ChatHistMessage]

    @staticmethod
    def find_unsaved_chats_and_summary(
        db, chat_history: List[ChatHistMessage],user_id
    ) -> Tuple[List[ChatHistMessage], Optional[ChatHistMessage]]:
        """
        Finds unsaved chats and the existing summary from the database.

        Args:
            db: Database connection.
            chat_history: List of ChatHistMessage objects.

        Returns:
            A tuple of unsaved chats and the existing summary (if any).
        """
        unsaved_chats = []
        existing_summary = None
        chat_history=chat_history[0]

        #older messages
        from collections import deque

        # Retrieve the old messages and convert them to ChatHistMessage objects
        old_messages = list(db.chat_messages.find(
            {"user_id": user_id, "sender": {"$nin": ["System", "system"]}}
        ).sort("created_at", -1).limit(3))
        old_messages=old_messages[::-1]
        old_messages = [ChatHistMessage(id=str(message["_id"]), **message) for message in old_messages]
        # print("old_messages",old_messages)
        # chat_history = deque(chat_history)
        for message in old_messages:
            chat_history.append(message)


        for message in chat_history:  # Use 'message' instead of 'chat' to avoid conflict
            existing_chat = db["chat_messages"].find_one({"_id": ObjectId(message.id)})

            if existing_chat and existing_chat.get("summary_id"):
                summary_doc = db["chat_messages"].find_one({"_id": ObjectId(existing_chat["summary_id"])})
                if summary_doc:
                    existing_summary = ChatHistMessage(
                        role=summary_doc.get("role"),
                        sender=summary_doc.get("sender"),
                        content=summary_doc.get("content"),
                        created_at=summary_doc.get("created_at"),
                        user_id=summary_doc.get("user_id"),
                        id=str(summary_doc.get("_id")),
                        chat_ids=[str(id) for id in summary_doc.get("chat_ids", [])],
                    )
                    break
            else:
                unsaved_chats.append(message)

        return unsaved_chats, existing_summary

    @staticmethod
    def format_summary(
        db, unsaved_chats: List[ChatHistMessage], existing_summary: Optional[ChatHistMessage]
    ) -> str:
        """
        Formats the summary by combining unsaved chats with an existing summary if it exists.

        Args:
            db: Database connection.
            unsaved_chats: List of unsaved chat messages.
            existing_summary: An existing ChatHistMessage summary (if any).

        Returns:
            A formatted summary string.
        """
        if existing_summary:
            unsaved_chats = deque(unsaved_chats)
            unsaved_chats.appendleft(existing_summary)

        summary = SummaryModel.create_summary(db, list(unsaved_chats))
        return summary.content

    @staticmethod
    def create_summary(
        db, unsaved_chats: List[ChatHistMessage], old_summary_id: Optional[str] = None
    ) -> ChatHistMessage:
        """
        Creates a summary from the unsaved chats.

        Args:
            db: Database connection.
            unsaved_chats: List of ChatHistMessage objects.
            old_summary_id: ID of the old summary (if any).

        Returns:
            A ChatHistMessage object representing the summary.

        """
        
        chat_ids = [str(chat.id) for chat in unsaved_chats if chat.id]
        user_id = unsaved_chats[0].user_id if unsaved_chats else None
        summary_output = SummaryModel.summarize_chat(db, unsaved_chats,old_summary_id)
        summary = ChatHistMessage.system_message(
            generated_summary=summary_output,
            user_id=user_id,
            chat_ids=chat_ids,
            old_summary_id=old_summary_id,
        )
        # print(f'{summary=}')

        # raise Exception
        return summary

    @staticmethod
    def summarize_chat(db, chat_history: List[ChatHistMessage],old_summary_id: Optional[str] = None) -> str:
        """
        Summarizes the chat history using a language model.

        Args:
            db: Database connection.
            chat_history: List of ChatHistMessage objects.

        Returns:
            A string summary of the chat history.
        """
        try:

            # Retrieve model and tokeni zer
            model = db["prompt"].find_one({"name": "summary_prompt"})["model"]
            summarizer_llm = OpenAI(model_name=model, max_tokens=256, temperature=0.0)
            tokenizer_fn = tiktoken.encoding_for_model(model).encode

            # Convert chat history to ChatMessage objects
            from llama_index.core.llms import ChatMessage
            chat_history = [ChatMessage(role=message.role, content=message.content) for message in chat_history]


            # Create memory buffer
            if not chat_history:
                loggers.warning("Chat history is empty. Returning old summary only.")
                # return old_summary.get("summary", "")
            # Retrieve old summary
            if old_summary_id:
                old_summary = db["chat_messages"].find_one({"_id": ObjectId(old_summary_id)})
                # loggers.info(f"Old summary retrieved: {old_summary}")
                chat_history.append(ChatMessage(role=old_summary["role"], content=old_summary["content"]))

            # loggers.info(f"Summarizing chat history... {chat_history}")
            prompt=db["prompt"].find_one({"name":"summary_prompt"})["text"]
            memory = ChatSummaryMemoryBuffer.from_defaults(
                chat_history=chat_history if len(chat_history) > 1 else chat_history,
                llm=summarizer_llm,
                token_limit=1,  # Adjusted for meaningful summaries
                summarize_prompt=prompt,
                tokenizer_fn=tokenizer_fn,
            )

            # Log successful processing
            # loggers.info("Chat history summarized successfully.")
            try:
                # print(memory.get())
                summarized_content = memory.get()[0]
            except Exception as e:
                loggers.error(f"Error while summarizing chat history: {e}")
                summarized_content = ""


            # Return the summarized content
            loggers.info(f"\n\nFinal summarized content: \n{summarized_content}\n")
            return summarized_content

        except Exception as e:
            loggers.error(f"Error while summarizing chat history: {e}")
            raise

    @staticmethod
    def process_chat_history(
        db, chat_history: List[ChatHistMessage],user_id
    ) -> Tuple[str, ChatHistMessage]:
        """
        Processes the chat history to find unsaved chats, retrieve or generate a summary,
        and return the final output.

        Args:
            db: Database connection.
            chat_history: List of ChatHistMessage objects.

        Returns:
            Tuple containing:
                - A formatted summary string.
                - A ChatHistMessage object representing the summary.
        """
        # Step 1: Find unsaved chats and existing summary
        unsaved_chats, existing_summary = SummaryModel.find_unsaved_chats_and_summary(db, chat_history,user_id)

        # print("chat_history",chat_history)
        # Step 2: Generate a formatted summary string
        # print("unsaved_chats",unsaved_chats)
        formatted_summary = SummaryModel.format_summary(db, unsaved_chats, existing_summary)

        # print("creating chat summ")
        # Step 3: Create a ChatHistMessage summary object
        summary_message = SummaryModel.create_summary(
            db, unsaved_chats, old_summary_id=existing_summary.id if existing_summary else None
        )
        # print("created chat summ")
        return formatted_summary, summary_message

    def model_dump(self) -> dict:
        """
        Converts the model into a dictionary with '_id' replacing 'id'.

        Returns:
            A dictionary representation of the model.
        """
        dump_ = super().model_dump()
        dump_['_id'] = str(dump_.pop('id'))
        return dump_