from pydantic import BaseModel, Field, root_validator
from typing import Optional, Dict, Union, Literal
from bson import ObjectId
from pymongo.database import Database

class Category(BaseModel):
    id: str = Field(..., alias="_id")
    name: str = Field(..., description="Name of the category")
    requirements: Dict[str, bool] | None = Field(None, description="Requirements for the category. e.g: Information that is required to be available if AI is to provide answer related to this category. for eg: if phone_number is requirement, then the ai will not answer the question and will prompt user to provide his phone number ")
    personality: str | None = Field(None, description="Personality label of the category. e.g: If the personality is 'sales_personality', then the corresponding personality will be used to answer the question")
    language: str = Field(..., description="Language of the response if the reply_from is Document")
    properties: Dict | None = None
    reply_from: Literal["documents", "human","saved_reply","ai"] = Field(...,  description="The source of the reply. If the reply is from a document, then the document_id will be provided. If the reply is from a human, then the human_id will be provided. If the reply is from a saved reply, then the saved_reply_id will be provided")
    retriever_requirements: Dict[str, bool]| None=None
    
    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

    @classmethod
    def from_id(cls, id, db: Database) -> "Category":
        category = db.categories.find_one({"_id": ObjectId(id)})
        if category:
            return cls(**category)
        
        return None
    
    @classmethod
    def from_name(cls, name, db: Database) -> "Category":
        category = db.categories.find_one({"name": name})
        if category:
            return cls(**category)

    @root_validator(pre=True)
    def convert_objectid_to_string(cls, values):
        if "_id" in values and isinstance(values["_id"], ObjectId):
            values["_id"] = str(values["_id"])
        return values
        
    def personality_string(self, db: Database):
        return db.settings.find_one({"name": self.personality})
    
    def saved_reply( self,  db: Database,requirement:Optional[dict],product_name:Optional[str]):
        """ 
        Returns saved reply of category if available
        """
        if requirement:
            if "product" in requirement.keys():
                print("product",product_name)
                print("category",self.id)
                saved_reply_doc = db.saved_reply.find_one({"category":self.id, "product":product_name})
                if saved_reply_doc:
                    saved_reply = saved_reply_doc.get("description")
                    return saved_reply

        if saved_reply_doc := db.saved_reply.find_one({"category":self.name}):
            saved_reply = saved_reply_doc.get("description")
            return saved_reply
        else:
            return None
        
    def document_info(self, db: Database):
        """ 
        Returns document information such as document pdf used in reply, query engine used in reply, index used in reply etc.
        """
        pass

    