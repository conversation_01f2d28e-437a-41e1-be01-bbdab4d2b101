# from pydantic import BaseModel, Field
# from typing import List,ClassVar
# from src.parse_products.extract_names import find_prod_name, find_prod_document
# import asyncio
# import hashlib
# from llama_index.llms.openai import OpenAI

# class Parse_Product(BaseModel):
#     description: str = Field(..., description="Description of the product")
#     images: List[str] = Field([], description="List of image URLs or Paths for the product")
#     llm: ClassVar[OpenAI] = OpenAI(model="gpt-4o-mini")


#     async def _compute_name(self) -> str:
#         """Compute product name from the description using the find_prod_name function"""
#         print(self.description)
#         prompt = find_prod_name.format(description=self.description)
#         return await self.llm.acomplete(prompt)
    
#     async def _compute_document(self) -> str:
#         """Compute product price from the description"""
#         find_prod_document_=find_prod_document.format(description=self.description)
#         return await self.llm.acomplete(find_prod_document_)

    
#     async def dict(self, *args, **kwargs):
#         ddict = super().dict(*args, **kwargs)
#         ddict["name"] =await self._compute_name()  # Compute name from description here
#         ddict["document"] = await self._compute_document()
#         print(f'DDICT: {ddict}')
        
#         return ddict

#     class Config:
#         from_attributes = True
#         populate_by_name = True


# if __name__ == "__main__":
#     async def main():
#         prod = Parse_Product(
#             description="""Priced at $999, the iPhone 15 Pro features a stunning 6.1-inch Super
#             Retina XDR display, which offers vibrant colors and sharp contrast. 
#             Powered by the A17 Pro chip, this smartphone boasts impressive
#             performance and efficiency. It comes with an enhanced camera
#             system that includes advanced computational photography features,
#             making it perfect for capturing high-quality photos and videos.
#             The iPhone 15 Pro also introduces USB-C charging, improved battery life,
#             and is available in several sleek colors.""",
#             images=[]
#         )

#         result = await prod.dict() 
#         print(result["name"])  
#         print(result["price"])
    
#     asyncio.run(main())