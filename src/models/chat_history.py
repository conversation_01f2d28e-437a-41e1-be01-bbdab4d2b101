from pydantic import BaseModel,  field_validator, Field,model_validator
from typing import List, Optional, Literal, Any
from bson.objectid import ObjectId
from datetime import datetime
from hashlib import sha256
import asyncio

from src.v2.chat.handle_image.extract_text_from_image import extract_text
from src.helper import get_base_url,change_port
from src.helper.logger import setup_new_logging

logger = setup_new_logging(__name__)

class ChatHistMessage(BaseModel):
    """

    summary_id (str): ID of the summary the message belongs to if it does.

    chat_ids (list(str)): If this chat message is a summary (system role) than chat_ids mean the chat messages that are summarized.

    """
    role: Literal['user', 'system', 'agent', 'assistant']
    content: str|None=""
    sender: str
    created_at: datetime|None=datetime.now()
    user_id: Any|None=None
    media_ids: List|None = []
    media_values: str|None = None
    summary_id: Optional[str] = None
    id: Optional[str] = None
    chat_ids: Optional[List[str]] = None
    verified_by: Optional[str] = None
    verified_at: Optional[datetime] = None
    verified: Optional[bool] = True
    message_id: Optional[str] = None

    current_user:Optional[Any]=None






    @field_validator("id", mode="before")
    def convert_object_id_to_str(cls, v):
        if isinstance(v, ObjectId):
            return str(v)
        return v



    @field_validator("content")
    def validate_content_msg(cls, v):
        """
        Ensures the `content` field is a valid string or None.
        Converts input to string if possible, or raises a validation error.

        Args:
            v: The value of the `content` field.

        Returns:
            str: The validated or converted string.

        Raises:
            ValueError: If the value cannot be converted to a string.
        """
        # print("a",v)
        if v is None:
            return ""  # Return an empty string if content is None
        if isinstance(v, str):
            return v  # Content is already a valid string
        elif hasattr(v, "role") and hasattr(v, "content"):  # If it's a ChatMessage object
            return str(v.content)  # Extract and return the content
        else:
            raise ValueError(f"Invalid content type: expected string or ChatMessage-like object, got {type(v).__name__}")

    @model_validator(mode="before")
    @classmethod
    def process_media(cls, values):
        """
        Processes media_ids to validate and generate media_values before finalizing the model.
        """
        media_ids = values.get("media_ids", [])

        if not media_ids or not isinstance(media_ids, list) or values.get("media_values"):
            return values

        try:
            valid_media_ids = [
                get_base_url(url)
                for url in media_ids
                if isinstance(url, str) and (url.startswith("http://") or url.startswith("https://"))
            ]

            logger.debug(f"Valid media IDs: {valid_media_ids}")
            if not valid_media_ids:
                logger.warning(f"Invalid media IDs found: {media_ids}")
                values["media_ids"] = []
                return values

            # Use sync version or create a new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            text_from_image, job_images = loop.run_until_complete(
                extract_text(values.get("current_user"), valid_media_ids)
            )
            loop.close()

            logger.debug(f"Extracted text from image: {text_from_image[:5] if text_from_image else None}")

            valid_media_ids = [change_port(url, 8402) for url in valid_media_ids]
            values["media_ids"] = job_images or valid_media_ids
            values["media_values"] = text_from_image

        except Exception as e:
            logger.error(f"Error processing media IDs: {e}")
            values["media_values"] = None

        return values


    @classmethod
    def from_role_data_format(cls, current_user,user_id, chat_history: List[dict]) -> List['ChatHistMessage']:
        """
        Converts raw chat history into ChatHistMessage objects.

        Args:
            chat_history (list): List of raw chat data dictionaries.

        Returns:
            List[ChatHistMessage]: A list of ChatHistMessage objects.
        """
        messages = []
        # user_id = next((
        #     chat_data.get("user_id")
        #     for chat in chat_history
        #     if isinstance(chat, dict) and chat.get("role") == "user"
        #     for chat_data in chat.get("data", [])
        #     if isinstance(chat_data, dict)
        # ), None)
        if chat_history:
            print(f"chat_history: {chat_history}")

            for chat in chat_history:
                role = chat.get('role')
                chat_datas = chat.get('data')  # 'data' is a dictionary, not a list

                if isinstance(chat_datas, dict):
                    chat_datas = [chat_datas]

                if isinstance(chat_datas, list):
                    for chat_data in chat_datas:
                        # Only add created_at if it has a value
                        if chat_data.get("media"):
                            if not chat_data.get("content"):
                                chat_data["content"] = "Answer This"
                        created_at = chat_data.get('created_at')
                        message_args = {
                        'current_user': current_user,

                            'role': role,
                            'sender': chat_data.get('sender') or ("unknown" if role == "user" else "assistant"),
                            'content': chat_data.get('content'),
                            'user_id': user_id,
                            'id': chat_data.get("id"),
                        }
                        if created_at is not None:
                            message_args['created_at'] = created_at

                        message = cls(**message_args)
                        messages.append(message)


            print(f"messages: {messages}")
            return messages

    @classmethod
    def from_role_content_format(cls,current_user,user_id, chat_history: List[dict]) -> List['ChatHistMessage']:
        """
        Formats a list of role-based content as a ChatHistMessage objects.

        Args:
        [
         {  role (str): Role of the message sender.
            content (str): Message content.
            media (list(urls)): List of media URLs.     # Optional
        }
        ]
        Returns:
            List[ChatHistMessage]: The created message object.
        """
        messages=[]
        for chat in chat_history:

            text_from_image = None
            if chat.get("media"):
                print(f"Extracting text from image for chat ID: {chat}")
                if not chat.get("content"):
                    chat["content"] = "Answer from the Image"
                # logger.debug(f"Extracting text from image for chat ID: {chat.get('id')} {chat.get('content')}")
                # text_from_image = asyncio.run(extract_text(None, chat.get("media")))
                # logger.debug(f"{text_from_image[:5]}, {type(text_from_image)}")

            message=cls(
            current_user=current_user,
            role = chat.get('role'),
            content = chat.get("content"),
            sender = chat.get('sender') or ("unknown" if chat.get('role') == "user" else "assistant"),
            created_at = datetime.now(),
            id = chat.get("id", None),
            user_id = user_id,
            media_ids = chat.get("media", []),
            media_values = text_from_image
            )
            messages.append(message)
        return messages

    @classmethod
    def format_chat_msg(cls, user: dict) -> List['ChatHistMessage']:
        """
        Formats user chat data into ChatHistMessage objects.

        Args:
            user (dict): User data including chat history and current message.

        Returns:
            list: A list of ChatHistMessage objects.
        """
        message = user.get("request", {}).get("message")
        user_id = user.get("request", {}).get("user_id")
        sender = user.get("user_name", "unknown")

        # Format the current user message
        message_format = cls(
            role="user",
            content=message,
            sender=sender,
            created_at=datetime.now(),
            user_id=user_id,
            id=None,
        )

        # Process chat history
        chat_history = user.get("request", {}).get("chat_history", [])
        formatted_msg = cls.from_role_data_format(chat_history)

        return formatted_msg + [message_format]

    @classmethod
    def system_message(cls, generated_summary, user_id, chat_ids, old_summary_id):
        """
        Creates a ChatHistMessage object for system messages.

        Args:
            generated_summary (str): The generated summary content.
            user_id (int): ID of the user the message belongs to.
            chat_ids (list): List of chat IDs.

        Returns:
            ChatHistMessage: A validated message object.
        """
        if not isinstance(generated_summary, str):
            generated_summary=generated_summary.content


        return cls(
            id=None,
            role="system",
            content=generated_summary,  # Ensure this is passed as a string
            sender="system",
            created_at=datetime.utcnow(),
            chat_ids=chat_ids,
            user_id=user_id,
            summary_id=old_summary_id
        )
    @classmethod
    def from_additional_information(cls, data: dict) -> 'ChatHistMessage':
        """
        Creates a ChatHistMessage object from additional information.

        Args:
            data (dict): Additional information data.

        Returns:
            ChatHistMessage: A validated message object.
        """
        return cls(
            role="user",
            content=data.get("content"),
            sender="system",
            created_at=datetime.now(),
            user_id=data.get("user_id"),
            id=data.get("id"),
            chat_ids=data.get("chat_ids")
        )


    @field_validator("role")
    def validate_role(cls, v):
        if v == "agent":
            return "assistant"
        return v



    @field_validator("id")
    def hash_id_from_role_content(cls, v, info):
        """
        Validates or generates a unique ID for the ChatHistMessage.

        Args:
            v: Existing ID value.
            info (ValidationInfo): Validation info object containing model data.

        Returns:
            str: A unique or existing ID.
        """
        # Handle both Pydantic validation and manual object creation
        if isinstance(info, dict):  # When called manually (e.g., cls(**values))
            values = info
        else:  # During Pydantic validation
            values = info.data

        if not v:  # If `id` is not provided, generate one
            content_hash = f"{values.get('role')}:{values.get('content')}:{values.get('created_at',datetime.now())}:{values.get('sender', 'unknown')}"
            return sha256(content_hash.encode('utf-8')).hexdigest()[:24]
        if isinstance(v, ObjectId):  # Convert ObjectId to string if necessary
            return str(v)
        return v
    @field_validator("content")
    def validate_message_content(cls, v):
        """
        Ensures the `content` field is a valid string.
        Converts input to string if possible, or raises a validation error.

        Args:
            v: The value of the `content` field.

        Returns:
            str: The validated or converted string.

        Raises:
            ValueError: If the value cannot be converted to a string.
        """
        if isinstance(v, str):
            return v  # Content is already a valid string
        elif hasattr(v, "role") and hasattr(v, "content"):  # If it's a ChatMessage object
            return str(v.content)  # Extract and return the content
        else:
            raise ValueError(f"Invalid content type: expected string or ChatMessage-like object, got {type(v).__name__}")
    @field_validator("sender")
    def validate_sender(cls, v, info):
        if isinstance(v, str):
            return v
        else:
            if not v:
                if info.data.get("role") == "user":
                    return "unknown"
                else:
                    return "assistant"
            raise ValueError(f"Invalid sender type: expected string, got {type(v).__name__}")
    def model_dump(self):
        """
        Dumps the model data into a dictionary for serialization.

        Returns:
            dict: Serialized model data.
        """
        dump_ = super().model_dump()
        dump_['_id'] = dump_.pop('id')
        try:
            if dump_["media_ids"] is None:
                dump_["media_ids"] = []
            dump_["media_ids"] = [get_base_url(url) for url in dump_["media_ids"]]
        except TypeError:
            dump_["media_ids"] = []
        # # Handle the case when media_ids is None
        # if dump_["media_ids"] is not None:
        #     dump_["media_ids"] = [get_base_url(url) for url in dump_["media_ids"]]
        # else:
        #     dump_["media_ids"] = []

        return dump_

    def model_dump_mongo(self):
        """
        Prepares the model data for MongoDB storage.

        Returns:
            dict: MongoDB-compatible data.
        """
        dump = self.model_dump()
        if dump.get("chat_ids"):
            # print(dump["chat_ids"])
            dump["chat_ids"] = [ObjectId(item) for item in dump["chat_ids"]]
        if not dump["created_at"]:
            dump["created_at"] = datetime.now()
        # Remove current_user from the dump as it's not needed for MongoDB storage
        dump.pop("current_user", None)
        # print("dump",dump)
        dump["_id"] = ObjectId(dump["_id"])
        # logger.debug(f"dump: {dump}")
        return dump

    def format_chat_msg(self):
        """
        Formats the chat message for display.

        Returns:
            str: The formatted chat message.
        """
        return f"""{self.role.capitalize()} ({self.sender.capitalize()}): `{self.content}`"""