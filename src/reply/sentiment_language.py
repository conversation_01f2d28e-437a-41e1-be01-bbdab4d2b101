from pydantic import ValidationError
from llama_index.llms.openai import OpenAI

from src.v2.chat.categorize.categorise import Category
from src.helper import get_DB,logger
from llama_index.core import Response
from typing import Optional
from src.models.chat_history import ChatHistMessage
from src.v2.chat.categorize.categorise import Category,ReplyConfig,AIConfig
loggers = logger.setup_new_logging(__name__)

def fetch_saved_reply(current_user, categories: list[Category],products: Optional[list]):
    """
    Note to whoever wrote this: khai exception handling? reply_from none aayo bhane k hunxa?
    """
    try:
        # Fetch saved reply of this category if available
        saved_replies = []
        no_saved_reply_categories = []
        try:
            product = products[0].id if products and len(products) ==1 else None
        except Exception as e:
            loggers.error(f"Error while fetching product: {e}")
            product = []


        for category in categories: 
            product_id = products[0].id if products and len(products) ==1 else None
            query={
                "category": str(category.id),
                "reply_from": "saved_reply"
            }
            if category.requirements.get("product"):
                query["product"] = product_id
            print("query",query)
            if current_user.db.reply_setup.find_one(query):
                db_record = current_user.db.reply_setup.find_one(query)['reply_config']

                reply_config_data = ReplyConfig(
                    type=db_record.get("reply_from", "saved_reply"),
                    retriever_requirements=db_record.get("retriever_requirements", None),
                    documents=db_record.get("documents", None),
                    prompt_template=db_record.get("prompt_template", None),
                    ai_config=AIConfig(
                        model=db_record["ai_config"]["model"],
                        temperature=db_record["ai_config"]["temperature"],)
                ) if db_record.get("documents", None) else None
                try:
                    category.reply_from = reply_config_data
                except ValidationError as e:
                    raise ValueError(f"Invalid reply_config data: {e}")
                print("category.reply_from.type",category.reply_from.type)
                saved_replies.append(category.saved_reply)
            else:
                no_saved_reply_categories.append(category)
            # if not category.requirements:          
            #     saved_reply = category.saved_reply( current_user.db,None,None)  # Fixed line
            # else:
            #     saved_reply=category.saved_reply(current_user.db, category.requirements,product)
            # if saved_reply:
            #     saved_replies.append(saved_reply)
            # else:
            #     no_saved_reply_categories.append(category)
        
        return no_saved_reply_categories, Response(response=" ".join(saved_replies)) if saved_replies else None
    except Exception as e:
        loggers.error(f"Error fetching saved reply: {e}")
        raise e


def sentiment_classification(current_user, text):
    # loggers.info(f"Classifying sentiment for text")
    try:
        prompt_db = current_user.db["prompt"] # variable naming bhayena
        sentiment_classification_prompt = prompt_db.find_one({"name":"sentiment_classification"}) # k ho yo jepayetei naming?
        prompt_ = sentiment_classification_prompt["text"]
        model = sentiment_classification_prompt["model"]
        llm= OpenAI(model=model)
        sentiment_prompt=prompt_.format(question=text)
        response = llm.complete(prompt=sentiment_prompt,formatted=True)
        response_text = response.text.strip(" ").split(":")[-1]
        # loggers.info(f"\nSentiment classification result: {response_text}")
        return response_text
    except Exception as e:
        loggers.error(f"\nError classifying sentiment: {e}") # i dont see the point of this error log
        raise e

def detect_language(current_user, text):
    # loggers.info(f"Detecting language for text:")
    try:
        prompt_db = current_user.db["prompt"]
        language_prompt = prompt_db.find_one({"name":"language_detection"})
        prompt_ = language_prompt["text"]
        model = language_prompt["model"]
        llm= OpenAI(model=model)
        language_prompt=prompt_.format(question=text)
        response = llm.complete(prompt=language_prompt,formatted=True)
        response_text = response.text.strip(" ").split(":")[-1]
        # loggers.info(f"\nDetected language: {response_text}")
        return response_text
    except Exception as e:
        loggers.error(f"\nError detecting language: {e}") # same here fix this or remove this
        raise e
from typing import List
def refine_reply(answer,question,db,summary,call_to_action,language):

    prompt=db["prompt"].find_one({"name":"refine_reply"})["text"]
    loggers.info("Refining the reply")
    loggers.debug(f"Question: {question}")
    loggers.debug(f"Summary: {summary}")
    loggers.debug(f"Answer: {answer}")
    llm=OpenAI(model=db["prompt"].find_one({"name":"refine_reply"})["model"])
    # prompt=f"""
    #     """
    summary=[i.format_chat_msg() for i in summary][::-1]
    
        
        
    prompt_=prompt.format(answer=answer,
                        question=question,summary=summary,call_to_action=call_to_action,language=language)
    print(prompt_)
    response = llm.complete(prompt=prompt_,formatted=True)
    return response.text

if __name__=="__main__":
    result = sentiment_classification("I love this product")
    result2=detect_language("I love this product")
    print(result)
    print(result2)