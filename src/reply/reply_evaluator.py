from pydantic import BaseModel
from llama_index.core.evaluation import FaithfulnessEvaluator
from llama_index.core.evaluation.base import BaseEvaluator, EvaluationResult
from llama_index.core import Document
from llama_index.core.evaluation import CorrectnessEvaluator,AnswerRelevancyEvaluator


from llama_index.llms.openai import OpenAI
from llama_index.core import Settings
import os
from src.helper import(
    get_DB,
    logger)


loggers = logger.setup_new_logging(__name__)

class VerifyQueryResponse(BaseModel):
    is_relevant: bool
    explanation: str
    
# class VerifyReplyDetails(BaseModel):
#     query=str|None=None
#     response=str|None=None
#     contexts=str|None=None,
#     passing=bool|None=None,
#     score=1.0 if passing else 0.0,
#     feedback=str

llm = OpenAI(model="gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
Settings.llm = llm
    

def verify_reply_in_document(query_: str,context, reply) -> EvaluationResult | None:
    try:
        loggers.info("Verifying reply in document")
        evaluator_gpt4 = FaithfulnessEvaluator(eval_template=f"""
            Query:
            ****
            {query_}
            ****          
            Response:
            ****
            {reply}
            ****
            Task:
            - Evaluate if the response is accurate, faithful, and directly supported by the content of the retrieved document.
            - Identify whether the information in the response is present in the document and aligns with the query.
            - Provide an overall evaluation of whether the response appropriately reflects the relevant information from the document.

                                         

        """)
        faith_eval = evaluator_gpt4.evaluate_response(
            # query=f"Is the response accurate to the query and is the answer present in the retrieved files?\n\nQuery: {query_}\n\nResponse: {response}\n\nPlease evaluate whether the response accurately reflects the relevant information from the retrieved documents.",
            query=query_,
            response=context,
        )
        return faith_eval
    except Exception as e:
        # raise e
        loggers.error(f"Error verifying reply in document: {e}")
        return EvaluationResult


def verify_query_reply(query: str,context, reply):
    try:
        # prompt =f"""
        # "Does the reply **fully answer** the query, addressing all the details and nuances of what the query is asking for? Answer 'Yes' or 'No' and provide a brief explanation."
        #     Query: {query}\n"
        #     "Reply: {reply}\n"
        # """
        prompt =f"""

        Query: 
        *****
        {query}
        *****
        Reply:
        *****
        {reply}
        *****
        Given the query and the reply of the query, please evaluate the reply based on the query:
            1. Does the reply fully address all details and nuances of the query? (Answer 'Yes' or 'No')
            2. Provide a brief explanation to justify your answer.

            """
        loggers.info("Verifying query reply")
        response=AnswerRelevancyEvaluator(llm=OpenAI(model="gpt-4o-mini",temperature=0)).evaluate_response(query=query, response=context)
        return response
    except Exception as e:  
        loggers.error(f"Error verifying query reply: {e}")
        return {}
        
async def evaluate_reply(current_user, query, context, reply):
    relevance_response = verify_query_reply(query,context, reply)
    faithfulness_response = verify_reply_in_document(query,  context, reply)
    
    relevance_data = {}
    try:
        relevance_data = {
            "is_relevant": True if relevance_response.score >=1 else False,
            "invalid_reason": relevance_response.invalid_reason,
            "invalid_result": relevance_response.invalid_result,
            "explanation": relevance_response.feedback,
        }
    
    except Exception as e:
        loggers.error(f"Error processing relevance data: {e}")
    faith_data = {}
    try:
        faith_data = {
            "passing": faithfulness_response.passing,
            "response": faithfulness_response.response,
            "pairwise_source": faithfulness_response.pairwise_source,
            "invalid_reason": faithfulness_response.invalid_reason,
            "invalid_result": faithfulness_response.invalid_result,
            "contexts": faithfulness_response.contexts,

        }
    except Exception as e:
        loggers.error(f"Error processing faithfulness data: {e}")

 
    
    return relevance_data, faith_data
        