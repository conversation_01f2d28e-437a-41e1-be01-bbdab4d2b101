from llama_index.core import Vector<PERSON><PERSON>Index, Document, Response
from llama_index.llms.openai import OpenAI
from llama_index.core.memory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.helper import logger, get_DB
from src.models.chat_history import ChatHistMessage
import re
from src.helper.logger import apply_logging_to_all_functions
from src.v2.chat.categorize.categorise import Category,ReplyConfig
from typing import Optional, List,Any
loggers = logger.setup_new_logging(__name__)

apply_logging_to_all_functions(globals())


def convert_latex(latex_text):
    try:
        loggers.debug(f"LATEX BEFORE: {latex_text}")
        text = latex_text.encode('unicode_escape').decode()

        # Replace \( ... \) with $ ... $ for inline math
        text = re.sub(r'\\\((.*?)\\\)', r'$\1$', text)
        
        # Replace \[ ... \] with $$ ... $$ for block math
        text = re.sub(r'\\\[(.*?)\\\]', r'$\1$', text)
        text = text.replace(r"\$", "$")

        loggers.debug(f"LATEX AFTER: {text}")
        return text
    except Exception as e:
        loggers.error(f"Error converting latex: {e}")
        return latex_text


def custom_chat_engine(current_user, user_message, contextualize, chat_hist: list[ChatHistMessage],ai_categories:Any,summary:str,language:str):
    if ai_categories:
        for cat in ai_categories:
            prompt=cat.reply_from.prompt_template
            model=cat.reply_from.ai_config.model
            temperature=cat.reply_from.ai_config.temperature
            llm=OpenAI(model=model)
            prompt=prompt.format(query=user_message,summary=summary,language=language)
            print(prompt)
            resp=llm.complete(prompt)
            print(resp)
            return resp.text
    
    latest_chat = chat_hist[0]

    if latest_chat.media_values:
        # loggers.info(f"Media values found querying index ,{media_values}")
        # document = Document(text=media_values, doc_id=media_ids[0], metadata={"media_ids": media_ids})

        # index = VectorStoreIndex.from_documents([document])

        # memory = ChatMemoryBuffer.from_defaults(token_limit=1500)

        # chat_engine = index.as_chat_engine(
        #             memory=memory,
        #             system_prompt=get_DB("echo")["prompt"].find_one({"name": "qna_prompt"})["text"],
        #             llm=OpenAI(model=get_DB("echo")["prompt"].find_one({"name": "qna_prompt"})["model"])
        #             ,

        #         )
        # reply = chat_engine.chat(user_message)
        # return reply
        # from openai import OpenAI

        llm=OpenAI(model=current_user.db["prompt"].find_one({"name": "ai_reply_prompt"})["model"])
        prompt = current_user.db["prompt"].find_one(
            {"name": "qna_prompt"})["text"]
        prompt = f" user message :{user_message} \n Context :{latest_chat.media_values} {prompt}"
        reply = llm.complete(prompt)
        # reply = OpenAI().beta.chat.completions.parse(model=current_user.db["prompt"].find_one({"name": "qna_prompt"})["model"],
        #                                              messages=[{"role": "system", "content": prompt},
        #                                                        {"role": "user", "content": user_message}],
        #                                              response_format={
        #     "type": "text",
        #     "format": "markdown",
        # }
        # )
        return convert_latex(reply.text)
    else:
        loggers.info(f"Media values not found querying AI model")
        Subject = None

        llm = OpenAI(model=current_user.db["prompt"].find_one({"name": "ai_reply_prompt"})["model"])
        prompt = current_user.db["prompt"].find_one(
            {"name": "ai_reply_prompt"})["text"]
        prompt = prompt.format(user_message=user_message)
        # from llama_index.llms.gemini import Gemini
        # llm = Gemini(
        #     model="models/gemini-2.0-flash-exp",
        # )
        # reply = OpenAI().chat.completions.create(model=current_user.db["prompt"].find_one({"name": "qna_prompt"})["model"],
        #                                              messages=[{"role": "system", "content": prompt},
        #                                                        {"role": "user", "content": user_message}],
        #                                              #   response_format={
        #                                              #       "type": "text",
        #                                              #       "format": "markdown",
        #                                              #   }
        #                                              )
        # return convert_latex(reply.choices[0].message.content)
        reply = llm.complete(prompt)

        return convert_latex(reply.text)
