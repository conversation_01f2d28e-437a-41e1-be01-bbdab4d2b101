from fastapi import UploadF<PERSON>, <PERSON>, APIRouter, HTTPException
from fastapi.responses import JSONResponse
from src.helper import logger, file_store, get_DB
from dotenv import load_dotenv
import requests
import fitz  # For PDFs
from fastapi import HTTPException
from PIL import Image  # For image handling
from io import BytesIO

import os
from src.models.retreiver import AddToIndexRequest, CreateQueryEngineRequest
from src.helper import logger, file_store, get_DB
from dotenv import load_dotenv
load_dotenv()
loggers = logger.setup_new_logging(__name__)

# Helper Function: File Upload
async def handle_file_upload(file: UploadFile, index_name: str):
    try:
        metadata = {"key": "file_name", "value": index_name, "operator": "IN"}
        access_url_data = await file_store.upload_file(file.file, metadata)
        loggers.info("File uploaded and access URL retrieved successfully")
        return access_url_data
    except Exception as e:
        loggers.exception("Error during file upload")
        raise HTTPException(status_code=500, detail={"error": "Failed to upload file", "details": str(e)})

async def extract_data_from_url(url: str):
    try:
        # Fetch the file from the URL
        response = requests.get(url)
        loggers.critical(f"Response status code: {response.status_code}")
        response.raise_for_status()

        # Check content type
        content_type = response.headers.get('Content-Type')

        if content_type == 'application/pdf':
            return extract_text_from_pdf(response.content)
        # elif content_type in ['image/jpeg', 'image/png', 'image/tiff']:
        #     return extract_text_from_image(response.content)
        # elif content_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        #     return extract_text_from_docx(response.content)
        else:
            raise HTTPException(
                status_code=415,
                detail="Unsupported file type. Supported types: PDF, DOCX, and images (JPG, PNG, TIFF).",
            )
    except requests.exceptions.RequestException as e:
        loggers.error(f"Error downloading file: {e}")
        raise HTTPException(status_code=400, detail=f"Error downloading file: {e}")
    except Exception as e:
        loggers.error(f"Error processing file: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing file: {e}")


# Helper: Extract text from PDF
def extract_text_from_pdf(file_bytes: bytes) -> str:
    try:
        loggers.info("Processing PDF file")
        reader = fitz.open(stream=file_bytes, filetype="pdf")
        extracted_text = ""
        for page in reader:
            extracted_text += page.get_text()
        reader.close()
        return extracted_text
    except Exception as e:
        loggers.error(f"Error extracting text from PDF: {e}")
        raise HTTPException(status_code=500, detail="Failed to process PDF file")


# Helper: Extract text from images using OCR
# def extract_text_from_image(file_bytes: bytes) -> str:
#     try:
#         loggers.info("Processing image file with OCR")
#         image = Image.open(BytesIO(file_bytes))
#         extracted_text = pytesseract.image_to_string(image)
#         return extracted_text
#     except Exception as e:
#         loggers.error(f"Error extracting text from image: {e}")
#         raise HTTPException(status_code=500, detail="Failed to process image file")


# Helper Function: Add to Index
def add_to_index_logic(request: AddToIndexRequest, documents: str):
    try:
        metadata = {"product_name":request.data_name}
        payload = {
            "token": request.vector_token,
            "content": documents,
            "metadata": metadata,
        }
        headers = {
            'accept': 'application/json',
            'Content-Type': 'application/json',
        }
        loggers.debug(f"Payload: {payload}, Headers: {headers}")

        response = requests.post(f"{os.getenv('API_URL')}:5600/add_vector/", headers=headers, json=payload)
        loggers.critical(f"Response status code: {response.status_code}")
        response.raise_for_status()

        return response.json()
    except requests.exceptions.RequestException as req_err:
        loggers.critical("Error occurred while communicating with the indexing service")
        raise HTTPException(
            status_code=502,
            detail={"error": "Failed to communicate with the indexing service", "details": str(req_err)},
        )
    except Exception as e:
        loggers.critical("Unexpected error occurred during indexing")
        raise HTTPException(
            status_code=500,
            detail={"error": "Failed to add to index", "details": str(e)},
        )

