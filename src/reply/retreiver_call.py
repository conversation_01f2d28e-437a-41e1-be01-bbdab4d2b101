# from fastapi import HTTPException
# from pydantic import BaseModel, Field
# from dotenv import load_dotenv
# import requests
# from typing import List, Dict
# from llama_index.core.schema import NodeWithScore, TextNode
# from llama_index.core import Response
# from llama_index.core.response import Response as LlamaIndexResponse
# from src.v2.chat.categorize.categorise import Category
# from src.models.products import Products
# from src.helper import logger, get_DB
# from src.reply.res_help import convert_to_llama_index_response
# import json
# import pymongo
# import asyncio
# import aiohttp
# from langchain_core.runnables import RunnableMap
# from nest_asyncio import apply
# from src.qdrant.qdrant_client import Qdrant_Call,QdrantConfig
# apply()

# # Load environment variables
# load_dotenv()
# loggers = logger.setup_new_logging(__name__)

# class FaissCall:
#     class FaissPayload(BaseModel):
#         token: str = Field(..., description="Query token ID")
#         query_engine_id: str = Field(..., description="Query engine ID")
#         query: str = Field(..., description="Query string to process")
#         prompt: str | None = None

#     def __init__(self, query: str, product: list[Products], query_engine_name: str, additional_information: dict = None, categories: list[Category] = None, contextualize: str = None, sentiment: str = None, language: str = None):
#         self.query = query
#         try:
#             self.product = product[0].id if product else None
#             self.product_name = product[0].course if product else None
#         except Exception as e:
#             print(e)
#             self.product = None
#             self.product_name = None
#         self.query_engine_name = query_engine_name
#         self.additional_information = additional_information
#         self.categories = categories
#         self.contextualize = contextualize
#         self.sentiment = sentiment
#         self.language = language
#         self.db=None
#         self.images=[]

#     def fetch_response_sync(self, db_query_formatted: str, query_engine_id: str, query_token_id: str) -> Dict:

    
#         if not db_query_formatted:
#             db_query_formatted = None
#         payload = self.FaissPayload(
#             token=query_token_id,
#             query_engine_id=query_engine_id,
#             query=self.contextualize,
#             prompt=db_query_formatted           
#         ).dict()
#         print(f"{payload=}")

#         headers = {
#             "accept": "application/json",
#             "Content-Type": "application/json",
#         }

#         FAISS_API = self.db["settings"].find_one({"name": "env"})["config"].get("VECTOR_API_URL")
#         response = requests.post(f"{FAISS_API}/query_index/", headers=headers, json=payload)
#         response.raise_for_status()
#         return response.json()

#     async def gather_responses(self, db) -> Dict[str, Dict]:
#             qd_confidg=db["settings"].find_one({"name": "env"})["qdran_config"]
#             print(qd_confidg)
        
#             qd=Qdrant_Call(
#                 config=QdrantConfig(
#                     host=qd_confidg["host"],
#                     port=qd_confidg["port"],
#                     coll_name=qd_confidg.get("page_collection")
#                 )
                
#             )
#             tasks = {}
#             db_engine_data = db["engine_data"]
#             db_query_ = db["prompt"].find_one({"name": self.query_engine_name})["text"]
#             db_query_formatted = None
#             if not self.categories:
#                 tasks[str("all")] = lambda  _: qd.qdran_call(query=self.query,metadata=None)
                
#             for category in self.categories:

                
#                 personality = category.personality
#                 personality_ = db["settings"].find_one({"name": personality})
#                 if personality_:
#                     personality = personality_.get("description")
#                     # if personality_.get("name") == "sales":
#                     #     db_query_formatted = None
                    
#                 else:
#                     db_query_formatted = db_query_.format(
#                         query=self.query,
#                         contextualize=self.contextualize,
#                         identified_products=self.product_name,
#                         personality=personality,
#                         language="English"
#                     )

#                 query_engine_data = db_engine_data.find_one({
#                     "product": self.product,
#                     "category":str(category.id)
#                 }, sort=[("position", pymongo.DESCENDING)])

#                 print(f"query engine data {query_engine_data}")

#                 if query_engine_data:
#                     query_engine_id = query_engine_data.get("engine_id")
#                     query_token_id = query_engine_data.get("index_id")
#                     metadata=query_engine_data.get("metadata",{})
#                     # Correctly capture the session and other parameters in the lambda
#                     tasks[str(category.id)] = lambda  db_query_formatted=db_query_formatted, query_engine_id=query_engine_id, query_token_id=query_token_id: qdran_call(query=self.query,metadata=metadata)
#                     # tasks[str(category.id)] = lambda  db_query_formatted=db_query_formatted, query_engine_id=query_engine_id, query_token_id=query_token_id: 

#             # Use RunnableMap to execute the tasks
#             runnable_map = RunnableMap(tasks)
#             results = await runnable_map.ainvoke({})
#             print(f"retreiver call results {results}")
#             return results
#     def convert_responses_to_llama_index(self, responses: Dict[str, Dict]) -> LlamaIndexResponse:
#         reply = ""
#         source_nodes = []
#         node_ids = set()  # Use a set for faster lookups
#         print(f"converting to llama index response {len(responses)}")

#         for category_id, response in responses.items():
#             try:
#                 # response_data = json.loads(json.dumps(response))
#                 # llama_response = convert_to_llama_index_response(json.dumps(response_data))
#                 image = response.metadata
#                 for item in image:
#                     print(image[item])
#                     if image[item].get("images"):
#                         self.images.extend(image[item]["images"])
#                 llama_response =response
#                 reply += llama_response.response
#                 if llama_response.source_nodes:
#                     source_nodes_data = llama_response.source_nodes
#                     for node in source_nodes_data:
#                         if node.id_ not in node_ids:
#                             node_ids.add(node.id_)
#                             text_node = TextNode(
#                                 text=node.node.text,
#                                 metadata=node.metadata,
#                                 id_=node.id_
#                             )
#                             source_nodes.append(NodeWithScore(node=text_node, score=node.score))
#             except Exception as e:
#                 loggers.error(f"Error processing response for category {category_id}: {e}")

#         return LlamaIndexResponse(response=reply, source_nodes=source_nodes,metadata={"images":self.images})

#     async def execute(self, db):
#         try:
#             self.db = db
#             loggers.info(f"Initializing FAISS index call {self.query_engine_name}")
#             loggers.info(f"Query: {self.query}")

#             # Fetch appropriate prompt and personality
#             db_engine_data = db["engine_data"]
#             db_query_ = db["prompt"].find_one({"name": self.query_engine_name})["text"]

#             if self.query_engine_name == "product_query_engine":
#                 query_engine_data = db_engine_data.find_one({"engine_name": self.query_engine_name})
#                 db_query_formatted = None
#                 loggers.info(f"Using engine: {self.query_engine_name}")
#                 query_engine_id = query_engine_data.get("engine_id")
#                 query_token_id = query_engine_data.get("index_id")
#                 response = self.call_index(db, self.query, db_query_formatted, query_engine_id, query_token_id)
#                 return convert_to_llama_index_response(response.content.decode('utf-8'))

#             if self.categories:
#                 responses = await self.gather_responses(db)
#             if not self.categories:
#                 responses=await self.gather_responses(db)
#                 return self.convert_responses_to_llama_index(responses)

#         except requests.RequestException as req_err:
#             loggers.error(f"Request error: {req_err}")
#             raise HTTPException(status_code=502, detail={"error": "Failed to communicate with the indexing service", "details": str(req_err)})
#         except Exception as e:
#             import traceback
#             traceback.print_exc()
#             loggers.error(f"Unexpected error: {e}")
#             return Response(response=None, source_nodes=None)

#     def call_index(self, db, query, db_query_formatted, query_engine_id, query_token_id):
#         payload = self.FaissPayload(
#             token=query_token_id,
#             query_engine_id=query_engine_id,
#             query=query,
#             prompt=None
#         ).dict()
#         print(f'{payload=}')
#         headers = {
#             "accept": "application/json",
#             "Content-Type": "application/json",
#         }
#         print(f'{payload=}')

#         # Send request to FAISS indexing service
#         FAISS_API = db["settings"].find_one({"name": "env"})["config"].get("VECTOR_API_URL")
#         response = requests.post(f"{FAISS_API}/query_index/", headers=headers, json=payload)
#         response.raise_for_status()

#         loggers.info(f"Response received: {response.status_code}")
#         return response