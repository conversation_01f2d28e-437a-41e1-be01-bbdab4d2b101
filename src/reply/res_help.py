from llama_index.core.response import Response as LlamaIndexResponse
from llama_index.core.schema import NodeWithScore
from llama_index.core.schema import TextNode
import json
from src.helper.logger import setup_new_logging
loggers = setup_new_logging(__name__)
def convert_to_llama_index_response(response_content):
    try:
        # Parse the JSON content
        data = json.loads(response_content)
        loggers.info(f"Converting to LlamaIndex Response:")
        
        # Extract response and source nodes
        response_text = data.get('response')
        loggers.info(f"response_text: {response_text}")
        source_nodes_data = data.get('source_nodes', [])
        
        # Convert source nodes to NodeWithScore objects
        source_nodes = []
        for node_data in source_nodes_data:
            node = node_data.get('node', {})
            source_nodes.append(
                NodeWithScore(
                    node=TextNode(
                        text=node.get('text', ''),
                        metadata=node.get('metadata', {}),
                        id_=node.get('id_', '')
                    ),
                    score=node_data.get('score', 0.0)
                )
            )
        
        # Create LlamaIndex Response object
        llama_response = LlamaIndexResponse(
            response=response_text,  # Assuming first item is the main response
            source_nodes=source_nodes
        )
        loggers.info(f"Converted to LlamaIndex Response:")
        
        return llama_response
    
    except Exception as e:
        loggers.error(f"Er ror converting to LlamaIndex Response: {e}")
        raise e