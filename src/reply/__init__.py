from llama_index.core import (
    get_response_synthesizer,
)
from llama_index.llms.openai import OpenAI
from nest_asyncio import apply
from dotenv import load_dotenv
import os
import json
from src.helper import(
    get_DB,
    logger)

from src.v2.chat.categorize.additional_information import update_user_additional_information
loggers = logger.setup_new_logging(__name__)
load_dotenv()
apply()

async def check_requirements(current_user, categories):
    try:
        categories_db=current_user.db["categories"]
        all_requirements=set()
        if not categories:
            return {}
        for category in categories:
            category = categories_db.find_one({"name":category})
            if not category:
                loggers.error("No category found")
            requirements = category["requirements"]
            if requirements:
                all_requirements.update(
                    key for key, value in requirements.items() if value is True
                )
        loggers.info(f"Requirements for categories: {all_requirements}")
        if not all_requirements:
            return {}
        else:
            return all_requirements
    except Exception as e:
        raise e
    
async def extract_user_information(current_user, required_information, request, formatted_summary):
    try:
        llm=OpenAI(model="gpt-4o-mini")
        extract_info_prompt = current_user.db["prompt"].find_one({"name":"extract_additional_information"})["text"]

        formatted_prompt = extract_info_prompt.format(
                    required_information=" ,".join(required_information),
                    summary=formatted_summary,
                    query=request
                )
        output=llm.complete(formatted_prompt,formatted=True)
        loggers.critical(f"Output: {output.text}")
        cleaned_output = output.text.strip("```json").strip("```").strip()
        return json.loads(cleaned_output)
    except Exception as e:
        raise e
    

async def prompt_user_for_missing_information(current_user, query, formatted_summary, missing_information):
    try:
        llm=OpenAI(model="gpt-4o-mini")

        request_information_prompt = current_user.db["prompt"].find_one({"name":"request_information"})["text"]
        formatted_prompt = request_information_prompt.format(
                    query=query,
                    summary=formatted_summary,
                    missing_information = missing_information
                )
        output=llm.complete(formatted_prompt,formatted=True)
        return output.text
    except Exception as e:
        raise e
    


async def extract_additional_information(current_user, request, contextualize, formatted_summary, categories, additional_information):
    """
    Given user's chat summary, contextualized sentence, and latest message, extract useful information for the category.
    """
    try:
        all_requirements = await check_requirements(current_user, categories)
        if all_requirements:
            user_information = await extract_user_information(current_user, all_requirements, request, formatted_summary)
            loggers.info(f"Extracting information from user query: {user_information}")

            filtered_information = {key: value for key, value in user_information.items() if value is not None}
            if filtered_information:
                await update_user_additional_information(current_user, request, filtered_information)
            else:
                print("no new additional information")

            user = current_user.db["customers"].find_one({"customer_id": request.user_id})
            additional_information = user["additional_information"]
            required_info = {key:value for key, value in additional_information.items() if key in all_requirements}
            missing_info = set(key for key,value in required_info.items() if value is None)
            return missing_info
        return set()
    except Exception as e:
        raise e    

