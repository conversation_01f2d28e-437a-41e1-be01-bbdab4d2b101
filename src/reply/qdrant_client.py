from typing import List, Dict, Optional
from pydantic import BaseModel
import re
from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex, QueryBundle

from llama_index.core.schema import NodeWithScore
from llama_index.core.vector_stores import (
    MetadataFilter,
    MetadataFilters,
    FilterOperator,
)
from llama_index.core.retrievers import BaseRetriever, VectorIndexRetriever, KeywordTableSimpleRetriever
from llama_index.core.query_engine import RetrieverQueryEngine

class QdrantConfig(BaseModel):
    coll_name: str = "test"
    host: str = "*************"
    port: int = 6333

class Qdrant_Call:
    def __init__(self, config: QdrantConfig):
        self.config = config
        self.client = QdrantClient(host=self.config.host, port=self.config.port)
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=self.config.coll_name)
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        self.index = VectorStoreIndex.from_vector_store(self.vector_store)

    def delete_collection(self, collection_name: str) -> bool:
        self.client.delete_collection(collection_name=collection_name)
        return True
    
    def create_collection(self, group: str, collection_name: str, vectors_config: dict) -> bool:
        self.client.create_collection(collection_name=collection_name, vectors_config=vectors_config,metadata={"group": group})
        return True

    def add_document_to_index(self, Nodes: List, collection_name: str) -> bool:
        self.client.delete_collection(collection_name=collection_name)
        vectors_config = {"size": 1536, "distance": "Cosine"}
        self.client.create_collection(collection_name=collection_name, vectors_config=vectors_config)
        self.vector_store = QdrantVectorStore(client=self.client, collection_name=collection_name)
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        self.index = VectorStoreIndex.from_documents(Nodes, storage_context=self.storage_context)
        self.index.insert_nodes(Nodes)
        return True

    class KeywordRetriever(KeywordTableSimpleRetriever):
        """Custom keyword retriever with regex matching"""
        def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
            keywords = self._get_keywords(query_bundle.query_str)
            retriever_ = self._index.as_retriever(similarity_top_k=100)
            nodes = []
            
            for keyword in keywords:
                nodes.extend(retriever_.retrieve(keyword))

            # Deduplicate while preserving order
            seen = set()
            nodes = [node for node in nodes if not (node.node.node_id in seen or seen.add(node.node.node_id))]
            
            # Regex match nodes
            matched_nodes = []
            for keyword in keywords:
                pattern = re.compile(rf'\b{re.escape(keyword)}\b', re.IGNORECASE)
                for node_with_score in nodes:
                    if pattern.search(node_with_score.node.text):
                        matched_nodes.append(node_with_score)
            return matched_nodes

    class LostInTheMiddleRetriever(BaseRetriever):
        """Hybrid retriever combining vector and keyword results"""
        def __init__(
            self,
            vector_retriever: VectorIndexRetriever,
            keyword_retriever: 'Qdrant_Call.KeywordRetriever',
            mode: str = "AND"
        ) -> None:
            self._vector_retriever = vector_retriever
            self._keyword_retriever = keyword_retriever
            self._mode = mode
            super().__init__()

        def _retrieve(self, query_bundle: QueryBundle) -> List[NodeWithScore]:
            vector_nodes = self._vector_retriever.retrieve(query_bundle)
            keyword_nodes = self._keyword_retriever._retrieve(query_bundle)

            vector_ids = {n.node.node_id for n in vector_nodes}
            keyword_ids = {n.node.node_id for n in keyword_nodes}

            combined_dict = {n.node.node_id: n for n in vector_nodes + keyword_nodes}

            if self._mode == "AND":
                retrieve_ids = vector_ids & keyword_ids
            else:
                retrieve_ids = vector_ids | keyword_ids

            retrieve_nodes = [combined_dict[rid] for rid in retrieve_ids]
            return sorted(retrieve_nodes, key=lambda x: x.score, reverse=True)

    def qdran_call(self, query: str, metadata: Optional[List[Dict]] = None, mode: str = "AND") -> str:
        """
        Execute a hybrid search query with metadata filtering
        
        Args:
            query: Search query string
            metadata: Optional list of metadata filters
            mode: Combination mode ("AND" or "OR")
        """
        try:
            # Handle metadata filters
            metadata_filter = None
            if metadata:
                filters = [
                    MetadataFilter(
                        key=m["key"], 
                        value=m["value"], 
                        operator=FilterOperator(m["operator"].upper())
                    ) for m in metadata
                ]
                metadata_filter = MetadataFilters(filters=filters)

            # Initialize retrievers
            vector_ret = VectorIndexRetriever(
                index=self.index,
                filters=metadata_filter,
                similarity_top_k=100
            )
            keyword_ret = self.KeywordRetriever(self.index)
            
            # Create hybrid retriever
            hybrid_retriever = self.LostInTheMiddleRetriever(
                vector_retriever=vector_ret,
                keyword_retriever=keyword_ret,
                mode=mode
            )
            
            # Execute query
            query_engine = RetrieverQueryEngine.from_args(hybrid_retriever)
            response = query_engine.query(query)
            return response
        
        except Exception as e:
            raise RuntimeError(f"Query execution failed: {str(e)}") from e