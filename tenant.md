

# 📘 Chatbot Tenant Onboarding & Setup

---

## Page 1: Step 1 – Tenant Creation & Environment Setup

---

### 🔹 Overview

When a new tenant registers, the system must set up the necessary infrastructure. This includes:

* Creating a unique tenant slug
* Setting up dedicated services for data and messaging

---

### 🔸 Flow

#### 1. Tenant Registration

* Triggered when a business fills out the signup form
* A unique **slug** is generated based on the business name
* Used to namespace tenant-specific resources

#### 2. Provision Resources for Tenant

Once registration is successful:

* ✅ **Database**: A new logical schema or prefixed collections are created for tenant data
* ✅ **Qdrant**: Initialize a vector index to store tenant-specific embeddings
* ✅ **MinIO**: Create a bucket or folder for storing documents or media
* ✅ **Social Messaging Setup**: Prepare configurations for WhatsApp, Facebook, etc., depending on the tenant’s requirements

---

### 🧩 Frontend Hints

* Validate business name before submission
* Show loading/processing status during tenant environment setup
* Display clear success or error messages after setup

---

## Page 2: Step 2 – Chatbot Business Info Collection

---

### 🔹 Overview

After registration, guide the tenant through a conversation-style interface to collect key business details. This ensures the chatbot can behave in a customized way.

---

### 🔸 Information to Collect

1. **Business Type**
   Example: Ecommerce, SaaS, Agency, Healthcare

2. **Target Users**
   Options: B2B, B2C, Internal Staff

3. **Main Business Goals**
   Examples: Lead generation, Customer support, Appointment booking

4. **Integration Needs**
   Social or messaging platforms: WhatsApp, Facebook, etc.

5. **Response Style**

   * Simplified: Short and sweet, friendly tone
   * Elaborated: More structured, informative
   * Detailed: In-depth, includes technical specifics if needed

6. **Business-Specific Information**
   Free-form description of products, services, customer types, tone preferences, etc.
   **Example**: "We are a software company that provides tools for small businesses. Our main goal is to help users manage their finances better. We offer a free trial and have a support team available 24/7."

---

### 💬 Frontend UX Notes

* Use a step-by-step guided chat interface (like a wizard)
* Use buttons/dropdowns for known options
* Show progress bar or step count
* Final step: Show a summary for confirmation before submitting

---

## In Background (Backend)

---

### 🔹 Overview

Based on the collected business data, generate a **system prompt** to guide the chatbot’s behavior.

---

### 🔸 Prompt Template Includes

* Role of the chatbot (e.g., support assistant)
* Company and industry context
* Key business details and tone
* Integration references

---

### 🔸 Retrieval Answer Mode

The system must tailor the chatbot's responses based on the chosen **Response Style**:

| Style      | Audience        | Description                                      |
| ---------- | --------------- | ------------------------------------------------ |
| Simplified | General users   | Easy-to-understand, friendly tone                |
| Elaborated | Staff or agents | More detailed, structured, informative           |
| Detailed   | Devs or experts | In-depth, includes technical specifics if needed |

---

## Page 3: Step 3 – Dynamic Answer Generation

---

### 🔹 Overview

Based on user queries, the chatbot responds in one of three modes depending on the selected retrieval style: **Simplified**, **Elaborated**, or **Detailed**.

---

### 🧠 Example – User Query:

> **"How do I book an appointment?"**

---

1. **Simplified**

> "To book an appointment, simply call our customer service line at 1-800-123-4567 during our business hours. Our friendly staff will be happy to assist you."

---

2. **Elaborated**

> "To book an appointment, you can either call our customer service line at 1-800-123-4567 during our business hours, or you can visit our website and navigate to the 'Book Appointment' section. Our staff will guide you through the process step-by-step."

---

3. **Detailed**

> "To book an appointment, you have two options. First, you can call our customer service line at 1-800-123-4567 during our business hours. Our staff is available 24/7 and will be happy to assist you. Alternatively, you can visit our website and navigate to the 'Book Appointment' section. There, you will find a detailed guide on how to book an appointment, including information on available appointment times, required documents, and any fees associated with the appointment. If you have any technical questions or need further assistance, please don't hesitate to contact our support team."

