# Eko Developer Onboarding 


[![Python Version](https://img.shields.io/badge/python-3.11%2B-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115.6-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/docker-ready-blue.svg)](Dockerfile)

> **Welcome to the Eko development team! This guide will help you understand our AI-powered customer service platform architecture, navigate the codebase, and start contributing effectively to the project.**

---

## 📚 Table of Contents

### Developer Onboarding
- [🚀 Project Overview for Developers](#-project-overview-for-developers)
- [�️ Codebase Navigation Guide](#️-codebase-navigation-guide)
- [🏗️ API Architecture Deep Dive](#️-api-architecture-deep-dive)
- [🏢 Understanding Multi-Tenant Architecture](#-understanding-multi-tenant-architecture)
- [💻 Local Development Setup](#-local-development-setup)
- [🔄 Development Workflow & Contribution Guidelines](#-development-workflow--contribution-guidelines)

### Technical Architecture
- [📁 Project Structure & Key Components](#-project-structure--key-components)
  - [Core Infrastructure (src/core)](#core-infrastructure-srccore)
  - [Production API (src/v2)](#production-api-srcv2)
  - [Experimental API (src/v3)](#experimental-api-srcv3)
  - [Supporting Systems](#supporting-systems)
- [� Tenant Isolation Implementation](#-tenant-isolation-implementation)
- [🛠 Technology Stack & Dependencies](#-technology-stack--dependencies)

### Development Practices
- [🧪 Testing & Quality Assurance](#-testing--quality-assurance)
- [📖 Key Files Every Developer Should Know](#-key-files-every-developer-should-know)
- [� Developer Troubleshooting Guide](#-developer-troubleshooting-guide)
- [📋 Code Standards & Best Practices](#-code-standards--best-practices)

### Quick Reference
- [⚡ Quick Start for New Developers](#-quick-start-for-new-developers)
- [� API Reference Summary](#-api-reference-summary)
- [📄 Additional Resources](#-additional-resources)

---

## 🚀 Project Overview for Developers

**Eko** is an enterprise-grade AI-powered customer service platform built with modern Python technologies and a sophisticated multi-tenant architecture. As a developer joining this project, you'll be working with cutting-edge AI integration, scalable microservices, and complex data processing systems.

### What You'll Be Working On

As an Eko developer, you'll contribute to a comprehensive customer service automation platform that:

- **AI Integration Layer**: Implement and optimize connections to multiple AI models (OpenAI GPT, Google Gemini) with intelligent fallback mechanisms and response processing
- **Multi-Channel Communication Engine**: Build and maintain integrations with WhatsApp Business API, email systems, web widgets, and custom API endpoints
- **Real-Time Analytics Pipeline**: Develop data processing systems for customer interaction analysis, sentiment detection, and business intelligence
- **Knowledge Base Management**: Create document processing pipelines, vector embeddings, and semantic search capabilities
- **Multi-Tenant Infrastructure**: Implement tenant isolation, security boundaries, and scalable resource management

### Development Environment Overview

The Eko platform is structured around several key technical domains:

| Domain | Technologies | Your Focus Areas |
|--------|-------------|------------------|
| **Backend API** | FastAPI, Python 3.11+, Pydantic | RESTful endpoints, data validation, business logic |
| **AI Processing** | OpenAI API, Google AI, LangChain, LlamaIndex | Model integration, prompt engineering, response optimization |
| **Data Layer** | MongoDB, Qdrant Vector DB, MinIO | Database design, query optimization, data modeling |
| **Communication** | Twilio, WebSocket, SMTP | Message routing, real-time features, external integrations |
| **Infrastructure** | Docker, FastAPI middleware, JWT | Containerization, authentication, tenant isolation |

### Architecture Principles for Developers

Understanding these core principles will help you navigate and contribute to the codebase effectively:

- **Multi-Tenant by Design**: Every feature must consider tenant isolation and data separation
- **API-First Development**: All functionality exposed through well-documented RESTful APIs
- **Modular Architecture**: Clear separation between v2 (production), v3 (experimental), and core systems
- **Async Processing**: Heavy operations handled asynchronously to maintain responsiveness
- **Comprehensive Logging**: Detailed logging and monitoring for debugging and performance analysis

---

## ✨ Key Features

### 🤖 AI-Powered Intelligence
- **Advanced Language Models**: Integration with OpenAI GPT-4, Google Gemini, and other leading AI models
- **Context-Aware Responses**: Maintains conversation context and customer history for personalized interactions
- **Sentiment Analysis**: Real-time emotion detection to route sensitive issues appropriately
- **Language Detection**: Automatic language identification for multilingual customer support
- **Product Identification**: AI-powered product recognition from customer queries

### 📱 Multi-Channel Communication
- **WhatsApp Integration**: Native WhatsApp Business API support via Twilio and Sociar
- **Email Management**: Automated email responses and notification systems
- **Web Widgets**: Embeddable chat widgets for websites with customizable styling
- **API Endpoints**: RESTful APIs for custom integrations and third-party applications

### 📊 Business Analytics & Intelligence
- **Real-Time Dashboards**: Comprehensive overview of customer service metrics
- **Performance Analytics**: AI response effectiveness, resolution times, customer satisfaction
- **Topic Analysis**: Automatic categorization and trending of customer inquiry topics
- **Customer Insights**: Detailed customer interaction history and behavior patterns
- **Agent Performance**: Metrics for human agents and AI automation effectiveness

### 📚 Knowledge Management
- **Document Processing**: Automatic ingestion and indexing of business documents (PDF, DOCX, TXT)
- **Vector Search**: Semantic search capabilities using Qdrant vector database
- **Content Generation**: AI-powered FAQ generation and content suggestions
- **Version Control**: Track changes and updates to knowledge base content

### 🏢 Enterprise-Ready Architecture
- **Multi-Tenant Support**: Isolated environments for multiple client businesses
- **Role-Based Access Control**: Granular permissions for different user types
- **Scalable Infrastructure**: Docker and Kubernetes deployment support
- **Security Features**: JWT authentication, data encryption, and audit logging
- **API Versioning**: Multiple API versions (v2, v3) for backward compatibility

---

## 🗺️ Codebase Navigation Guide

Understanding the Eko codebase structure is essential for effective development. This section provides a comprehensive guide to help you navigate and understand the organization of our code.

### High-Level Directory Structure

The Eko project follows a modular architecture with clear separation of concerns:

```
echo_bot/
├── 📄 main.py                    # FastAPI application entry point
├── 📄 main_routes.py             # Centralized router management and configuration
├── 📁 src/                      # Main source code directory
│   ├── 📁 core/                 # Foundation layer (config, database, security)
│   ├── 📁 models/               # Pydantic data models and validation
│   ├── 📁 helper/               # Shared utilities and helper functions
│   ├── 📁 routes/               # Core API route definitions
│   ├── 📁 v2/                   # Production API implementation
│   ├── 📁 v3/                   # Experimental/next-generation API
│   ├── 📁 tenant/               # Multi-tenant management system
│   └── 📁 background_tasks/     # Async processing and background jobs
├── 📁 tests/                    # Test files and testing utilities
└── 📁 deployment/               # Docker, Kubernetes, and deployment configs
```

### Understanding the src/ Directory

The `src/` directory contains all the core application logic, organized into logical modules:

#### Core Infrastructure (`src/core/`)

The foundation layer that provides essential services across the entire application:

<augment_code_snippet path="src/core/config.py" mode="EXCERPT">
````python
# Database settings
MONGO_URI = os.getenv("MONGO_URI")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
````
</augment_code_snippet>

| File | Purpose | Key Responsibilities |
|------|---------|---------------------|
| **`config.py`** | Environment configuration | Database connections, API keys, feature flags |
| **`database.py`** | Database operations | MongoDB client management, connection pooling |
| **`security.py`** | Authentication & authorization | JWT handling, tenant validation, RBAC |
| **`exceptions.py`** | Error handling | Custom exceptions, standardized error responses |
| **`activity_log.py`** | Audit logging | User action tracking, middleware integration |

#### Data Models (`src/models/`)

Pydantic models that define data structures, validation rules, and API contracts:

**User & Security Models:**
- `user.py` - User accounts, authentication, tenant associations
- `security.py` - JWT tokens, permissions, access control
- `profile.py` - User profiles and preferences

**Business Logic Models:**
- `customers.py` - Customer data, contact information, metadata
- `products.py` - Product catalog, descriptions, categorization
- `chat_history.py` - Conversation logs, message threading
- `category.py` - Message routing rules, AI configurations

**AI & Analytics Models:**
- `ReplyHandler.py` - AI response processing and evaluation
- `model_run.py` - AI model execution requests and responses
- `qdrant_model.py` - Vector database interaction models

#### Helper Utilities (`src/helper/`)

Shared functionality used across the application:

<augment_code_snippet path="src/helper/logger.py" mode="EXCERPT">
````python
def setup_new_logging(__name__):
    # Centralized logging configuration
    # Multiple output formats and levels
````
</augment_code_snippet>

| Utility | Functionality | When to Use |
|---------|---------------|-------------|
| **`logger.py`** | Centralized logging | All modules for consistent log formatting |
| **`file_processing.py`** | Document handling | File uploads, format conversion, processing |
| **`qdrant.py`** | Vector operations | Semantic search, embedding storage |
| **`resolve_llm.py`** | AI model management | Model selection, fallback handling |
| **`websocketmanager.py`** | Real-time communication | Live chat, streaming responses |

### Navigation Tips for New Developers

#### 1. Start with Entry Points

Begin your exploration with these key files:

- **`main.py`** - Application startup and configuration
- **`main_routes.py`** - Router registration and API structure
- **`src/core/config.py`** - Environment and configuration management

#### 2. Follow the Request Flow

Understand how requests flow through the system:

1. **Router** (`src/routes/` or `src/v2/`) - Endpoint definition
2. **Security** (`src/core/security.py`) - Authentication and tenant validation
3. **Business Logic** (various modules) - Core processing
4. **Data Layer** (`src/core/database.py`) - Database operations
5. **Response** - Formatted output back to client

#### 3. Understand the Module Dependencies

Key dependency patterns to understand:

- **Core modules** are imported by all other modules
- **Helper utilities** provide shared functionality
- **Models** define data contracts between layers
- **v2 modules** represent stable, production code
- **v3 modules** contain experimental features

---

## 🛠 Technology Stack & Dependencies

### Backend Framework
| Technology | Version | Purpose |
|------------|---------|---------|
| **FastAPI** | 0.115.6 | High-performance web framework for building APIs |
| **Python** | 3.11+ | Core programming language |
| **Uvicorn** | 0.34.0 | ASGI server for production deployment |
| **Gunicorn** | 23.0.0+ | Process manager for scaling applications |

### AI & Machine Learning
| Technology | Purpose | Integration |
|------------|---------|-------------|
| **OpenAI GPT** | Primary language model for chat responses | Direct API integration |
| **Google Gemini** | Alternative AI model for diverse capabilities | Google AI SDK |
| **LlamaIndex** | Document indexing and retrieval framework | Knowledge base management |
| **LangChain** | AI application development framework | Chain-of-thought processing |
| **NLTK** | Natural language processing toolkit | Text preprocessing and analysis |

### Databases & Storage
| Technology | Purpose | Use Case |
|------------|---------|----------|
| **MongoDB** | Primary database | User data, conversations, configurations |
| **Qdrant** | Vector database | Semantic search and document embeddings |
| **MinIO** | Object storage | File uploads, media storage, document archives |

### Communication & Integration
| Service | Purpose | Implementation |
|---------|---------|---------------|
| **Twilio** | WhatsApp Business API | Message sending and webhook handling |
| **Sociar** | Alternative WhatsApp provider | Multi-provider redundancy |
| **Email Services** | SMTP integration | Automated notifications and responses |
| **WebSocket** | Real-time communication | Live chat and streaming responses |

### Deployment & Infrastructure
| Technology | Purpose | Configuration |
|------------|---------|---------------|
| **Docker** | Containerization | Multi-stage builds for optimization |
| **Docker Compose** | Local development | Service orchestration and networking |
| **Kubernetes** | Production orchestration | Scalable, resilient deployments |
| **Nginx** | Load balancing | Request distribution and SSL termination |

### Development & Monitoring
| Tool | Purpose | Usage |
|------|---------|-------|
| **Pydantic** | Data validation | Request/response models and configuration |
| **Streamlit** | Demo applications | Interactive prototypes and testing |
| **Logging** | Application monitoring | Structured logging with multiple levels |
| **Testing** | Quality assurance | Unit tests and integration testing |

---

## ⚡ Quick Start Guide

### Prerequisites Check

Before starting, ensure you have:

- **Python 3.11 or higher** installed
- **Docker and Docker Compose** for containerized deployment
- **MongoDB instance** (local or cloud)
- **Qdrant vector database** access
- **API keys** for AI services (OpenAI, Google)

### 1. Clone the Repository

```bash
git clone <repository-url>
cd echo_bot
```

### 2. Environment Setup

Create a `.env` file in the root directory:

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Quick Docker Deployment

```bash
# Build and start all services
docker-compose up -d

# Check service status
docker-compose ps
```

### 4. Access the Application

- **API Documentation**: http://localhost:8000/docs
- **Admin Dashboard**: http://localhost:8000/dashboard
- **Health Check**: http://localhost:8000/health

### 5. Initial Configuration

1. Access the admin dashboard
2. Create your first tenant
3. Configure AI models and API keys
4. Set up communication channels
5. Upload initial knowledge base documents

---

## 🏗️ API Architecture Deep Dive

Understanding the API architecture is crucial for effective development in Eko. Our platform uses a versioned API approach with clear separation between production-ready and experimental features.

### API Versioning Strategy

Eko implements a multi-version API architecture to support both stability and innovation:

| Version | Status | Purpose | Development Focus |
|---------|--------|---------|-------------------|
| **v2** | Production | Stable, battle-tested features | Bug fixes, performance optimization, minor enhancements |
| **v3** | Experimental | Next-generation features | New capabilities, architectural improvements, breaking changes |
| **Core Routes** | Legacy | Basic functionality | Maintenance mode, gradual migration to v2/v3 |

### v2 API (Production) - `src/v2/`

The v2 API represents our current production implementation with proven stability and comprehensive features:

#### Key Characteristics:
- **Stability**: Thoroughly tested, production-ready endpoints
- **Comprehensive**: Full feature set for customer service automation
- **Backward Compatible**: Maintains API contracts for existing integrations
- **Performance Optimized**: Optimized for high-throughput production workloads

#### Major Modules:

**Knowledge Base Management (`src/v2/KB/`)**
<augment_code_snippet path="src/v2/KB/kb_setup/handle_documents.py" mode="EXCERPT">
````python
# Document upload, parsing, and indexing
# Text segmentation and node creation
# Intelligent text splitting for optimal chunks
````
</augment_code_snippet>

- **Document Processing**: Upload, parse, and index business documents
- **Vector Operations**: Semantic search using Qdrant vector database
- **Content Management**: Version control and document lifecycle

**Advanced Chat System (`src/v2/chat/`)**
- **Message Processing**: Classification, routing, and contextualization
- **Image Handling**: OCR and image content extraction
- **Product Identification**: AI-powered product recognition from queries

**Business Dashboard (`src/v2/dashboard/`)**
- **Analytics Engine**: Real-time metrics and business intelligence
- **Customer Management**: Interaction history and profile management
- **Performance Evaluation**: AI response quality assessment

**External Integrations (`src/v2/external_hooks/`)**
- **WhatsApp Integration**: Twilio and Sociar provider support
- **Email Systems**: SMTP integration and notification management
- **Webhook Management**: External service integration points

#### Core v2 Endpoints Overview:

**Tenant Management & Authentication:**
- `GET /get_tenant_id` - Resolve tenant ID from slug for multi-tenant routing
- `POST /login` - User authentication with tenant-scoped JWT tokens
- `GET /verify_token` - Validate JWT tokens and return user details
- `POST /extended_token` - Generate long-lived access tokens (1-365 days)

**User & Agent Management:**
- `POST /agents/invite` - Invite new agents with registration tokens (admin/supervisor only)
- `POST /agents/register` - Complete agent registration using invitation tokens
- `GET /users/roles/count` - Get user count statistics by role for dashboard analytics
- `GET /users/subordinates` - List agents with pagination and filtering capabilities

### v3 API (Experimental) - `src/v3/`

The v3 API contains next-generation features and architectural improvements:

#### Key Characteristics:
- **Innovation**: Cutting-edge features and improved architectures
- **Tool Calling**: Advanced AI capabilities with function calling
- **Enhanced Performance**: Optimized response generation and processing
- **Breaking Changes**: May introduce incompatible changes for better design

#### Major Modules:

**Advanced Chat (`src/v3/chat/`)**
<augment_code_snippet path="src/v3/chat/response.py" mode="EXCERPT">
````python
# Enhanced response generation with tool calling
# AI tool definitions and execution handlers
# Advanced detection algorithms and processing
````
</augment_code_snippet>

- **Enhanced Response Generation**: Improved AI model integration with tool calling
- **Advanced Detection**: Better language, sentiment, and intent detection
- **Improved Client Management**: More efficient API client implementations

#### Core v3 Endpoints Overview:

**Next-Generation AI Processing:**
- Enhanced model integration with tool calling capabilities
- Improved response generation algorithms
- Advanced sentiment and language detection
- Optimized client management for better performance

**Experimental Features:**
- Breaking changes for improved architecture
- New AI capabilities and model integrations
- Performance optimizations and enhanced processing
- Future-ready API design patterns

*Note: v3 endpoints are currently in development and may introduce breaking changes. Use v2 endpoints for production applications.*

### When to Use Each Version

#### Choose v2 API When:
- Building production features that need stability
- Integrating with existing systems
- Implementing well-established customer service workflows
- Working on features that require proven reliability

#### Choose v3 API When:
- Experimenting with new AI capabilities
- Implementing cutting-edge features
- Prototyping next-generation functionality
- Working on features that can tolerate breaking changes

### API Request Flow Architecture

Understanding how requests flow through the system helps in debugging and optimization:

```
1. Request Entry
   ├── main.py (FastAPI app)
   └── main_routes.py (Router registration)

2. Authentication & Tenant Resolution
   ├── src/core/security.py (JWT validation)
   └── Tenant context establishment

3. Route Processing
   ├── src/v2/* (Production endpoints)
   ├── src/v3/* (Experimental endpoints)
   └── src/routes/* (Legacy endpoints)

4. Business Logic
   ├── AI Processing (OpenAI, Gemini integration)
   ├── Data Operations (MongoDB, Qdrant)
   └── External Services (Twilio, Email)

5. Response Formation
   ├── Data serialization (Pydantic models)
   └── HTTP response with proper status codes
```

### Development Guidelines for API Work

#### For v2 Development:
1. **Maintain Backward Compatibility**: Ensure existing integrations continue working
2. **Comprehensive Testing**: All changes must have thorough test coverage
3. **Performance Monitoring**: Monitor impact on production metrics
4. **Documentation Updates**: Keep API documentation current

#### For v3 Development:
1. **Innovation Focus**: Prioritize new capabilities over backward compatibility
2. **Experimental Approach**: Rapid prototyping and iteration encouraged
3. **Migration Planning**: Consider how features will eventually move to production
4. **Breaking Change Documentation**: Clearly document any breaking changes

---

## 💻 Local Development Setup

This section provides a comprehensive guide for setting up your local development environment to work effectively with the Eko platform.

### Prerequisites for Developers

Before starting development, ensure you have the following tools and accounts:

| Requirement | Purpose | Installation Notes |
|-------------|---------|-------------------|
| **Python 3.11+** | Core runtime | Use pyenv for version management |
| **Git** | Version control | Configure with your GitHub credentials |
| **Docker & Docker Compose** | Containerization | Required for database services |
| **VS Code or PyCharm** | IDE | Recommended for Python development |
| **MongoDB Compass** | Database GUI | Optional but helpful for data inspection |

### Step-by-Step Development Setup

#### 1. Repository Setup

```bash
# Clone the repository
git clone <repository-url>
cd echo_bot

# Create and activate virtual environment
python -m venv venv

# Activate environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Upgrade pip and install dependencies
pip install --upgrade pip
pip install -r requirements.txt
```

#### 2. Environment Configuration

Create your development environment file:

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your development configuration
# Use a text editor or IDE to modify the file
```

**Essential Environment Variables for Development:**

```bash
# Development Database
MONGO_URI=mongodb://localhost:27017
DATABASE_NAME=eko_development

# Development Qdrant
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Development AI Keys (get from respective platforms)
OPENAI_API_KEY=your_development_openai_key
GOOGLE_AI_API_KEY=your_development_google_key

# Development Security
SECRET_KEY=your_development_secret_key_here
ALGORITHM=HS256

# Development Features (enable for testing)
ENABLE_AI_RESPONSES=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_LANGUAGE_DETECTION=true
```

#### 3. Database Services Setup

Use Docker Compose to run required services locally:

```bash
# Start MongoDB and Qdrant services
docker-compose up -d mongodb qdrant

# Verify services are running
docker-compose ps

# Check service logs if needed
docker-compose logs mongodb
docker-compose logs qdrant
```

#### 4. Development Database Initialization

Set up your development database with initial data:

```bash
# Connect to MongoDB and create development database
mongo mongodb://localhost:27017

# In MongoDB shell:
use eko_development

# Create a development tenant
db.tenants.insertOne({
  "tenant_id": "dev_tenant",
  "business_name": "Development Tenant",
  "database_name": "eko_development",
  "status": "active",
  "created_at": new Date()
})

# Create development user
db.users.insertOne({
  "email": "<EMAIL>",
  "password_hash": "$2b$12$...", // Use proper bcrypt hash
  "role": "admin",
  "tenant_id": "dev_tenant",
  "permissions": ["all"],
  "created_at": new Date(),
  "status": "active"
})
```

#### 5. External Service Setup for Development

**OpenAI API Setup:**
1. Visit https://platform.openai.com
2. Create an account and generate an API key
3. Add the key to your `.env` file
4. Set usage limits to avoid unexpected charges

**Google AI Setup:**
1. Go to Google Cloud Console
2. Enable the Generative AI API
3. Create credentials and download the key
4. Add to your `.env` file

**Optional: Twilio Setup (for WhatsApp testing)**
1. Create a Twilio account
2. Get a trial phone number
3. Configure webhook URLs for local testing (use ngrok)

#### 6. Running the Development Server

Start the FastAPI development server:

```bash
# Run with auto-reload for development
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or use the development script if available
python main.py
```

**Verify your setup:**
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Admin Dashboard: http://localhost:8000/dashboard

### Development Workflow Best Practices

#### Daily Development Routine

1. **Start Services**: `docker-compose up -d mongodb qdrant`
2. **Activate Environment**: `source venv/bin/activate`
3. **Pull Latest Changes**: `git pull origin main`
4. **Install New Dependencies**: `pip install -r requirements.txt`
5. **Run Tests**: `pytest tests/`
6. **Start Development Server**: `uvicorn main:app --reload`

#### Code Quality Tools

Set up these tools for consistent code quality:

```bash
# Install development tools
pip install black isort flake8 mypy pytest

# Format code
black src/
isort src/

# Check code quality
flake8 src/
mypy src/

# Run tests
pytest tests/ -v
```

#### Debugging Setup

**VS Code Configuration:**
Create `.vscode/launch.json`:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "FastAPI Debug",
            "type": "python",
            "request": "launch",
            "program": "main.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            }
        }
    ]
}
```

**Logging Configuration:**
Enable detailed logging for development:

```python
# In your .env file
LOG_LEVEL=DEBUG
ENABLE_DEBUG_LOGGING=true
```

---

## ⚙️ Configuration

### Environment Variables

Create a `.env` file with the following configuration:

#### Database Configuration
```bash
# MongoDB Settings
MONGO_URI=mongodb://localhost:27017
DATABASE_NAME=eko_production

# Qdrant Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=your_qdrant_api_key
```

#### AI Service Configuration
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Google AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_key
GEMINI_MODEL=gemini-pro
```

#### Security Settings
```bash
# JWT Configuration
SECRET_KEY=your_super_secret_jwt_key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]
```

#### Communication Services
```bash
# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_WHATSAPP_NUMBER=whatsapp:+**********

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

#### File Storage
```bash
# MinIO Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=eko-documents
```

### Feature Toggles

Control application features through environment variables:

```bash
# AI Features
ENABLE_AI_RESPONSES=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_LANGUAGE_DETECTION=true

# Communication Channels
ENABLE_WHATSAPP=true
ENABLE_EMAIL=true
ENABLE_WEB_WIDGET=true

# Analytics Features
ENABLE_TOPIC_ANALYSIS=true
ENABLE_PERFORMANCE_METRICS=true
ENABLE_CUSTOMER_INSIGHTS=true
```

### Multi-Tenant Configuration

```bash
# Tenant Management
DEFAULT_TENANT_ID=default
ENABLE_TENANT_ISOLATION=true
TENANT_DATABASE_PREFIX=tenant_
MAX_TENANTS_PER_INSTANCE=100
```

---

## 🏢 Multi-Tenant Architecture

Eko is built with a comprehensive multi-tenant architecture that allows service providers to manage multiple client businesses from a single platform instance. Each tenant operates in complete isolation with dedicated data storage, configurations, and customizations.

### Architecture Overview

The multi-tenant system provides:

- **Data Isolation**: Each tenant has separate database collections and storage spaces
- **Configuration Isolation**: Independent settings, AI models, and feature configurations per tenant
- **Security Isolation**: JWT tokens are tenant-scoped with role-based access control
- **Resource Isolation**: Separate rate limits, quotas, and performance metrics per tenant
- **Customization Support**: Tenant-specific branding, workflows, and integrations

### Tenant Data Structure

Each tenant maintains the following isolated data collections:

| Collection | Purpose | Schema Requirements |
|------------|---------|-------------------|
| **users** | Tenant user accounts and permissions | `user_id`, `email`, `role`, `tenant_id`, `permissions` |
| **customers** | Customer profiles and contact information | `customer_id`, `name`, `email`, `phone`, `metadata` |
| **chat_history** | Conversation logs and message history | `chat_id`, `customer_id`, `messages`, `timestamp`, `channel` |
| **ai_response** | AI-generated responses and analytics | `response_id`, `query`, `response`, `model_used`, `metrics` |
| **products** | Product catalog and information | `product_id`, `name`, `description`, `category`, `metadata` |
| **categories** | Message categorization and routing rules | `category_id`, `name`, `rules`, `ai_config`, `routing` |
| **settings** | Tenant-specific configuration | `setting_name`, `value`, `type`, `updated_by`, `updated_at` |
| **knowledge_base** | Document storage and vector embeddings | `doc_id`, `content`, `embeddings`, `metadata`, `version` |
| **activity_logs** | User activity and audit trails | `log_id`, `user_id`, `action`, `timestamp`, `details` |

### New Tenant Creation Process

#### 1. Administrative Prerequisites

Before creating a new tenant, ensure:

- **Database Access**: Administrative access to MongoDB instance
- **API Keys**: Collected AI service API keys for the new tenant
- **Configuration Data**: Business information, branding assets, initial settings
- **User Information**: Primary administrator account details

#### 2. Database Setup Procedure

**Step 1: Create Tenant Database Collections**

```bash
# Connect to MongoDB
mongo mongodb://localhost:27017

# Switch to tenant database (replace {tenant_id} with actual ID)
use eko_tenant_{tenant_id}

# Create required collections with proper indexing
db.createCollection("users")
db.createCollection("customers")
db.createCollection("chat_history")
db.createCollection("ai_response")
db.createCollection("products")
db.createCollection("categories")
db.createCollection("settings")
db.createCollection("knowledge_base")
db.createCollection("activity_logs")

# Create essential indexes for performance
db.users.createIndex({"email": 1}, {"unique": true})
db.customers.createIndex({"email": 1, "phone": 1})
db.chat_history.createIndex({"customer_id": 1, "timestamp": -1})
db.ai_response.createIndex({"timestamp": -1})
db.activity_logs.createIndex({"user_id": 1, "timestamp": -1})
```

**Step 2: Initialize Default Settings**

```javascript
// Insert default tenant settings
db.settings.insertMany([
  {
    "name": "tenant_info",
    "value": {
      "tenant_id": "{tenant_id}",
      "business_name": "{business_name}",
      "contact_email": "{contact_email}",
      "created_at": new Date(),
      "status": "active"
    }
  },
  {
    "name": "ai_config",
    "value": {
      "default_model": "gpt-4",
      "max_tokens": 2000,
      "temperature": 0.7,
      "enable_ai_responses": true
    }
  },
  {
    "name": "communication_channels",
    "value": {
      "whatsapp_enabled": false,
      "email_enabled": true,
      "web_widget_enabled": true
    }
  },
  {
    "name": "email_notifications",
    "value": {
      "enabled": false,
      "emails": [],
      "cta_types": {
        "ticket": {"enable": false},
        "booking": {"enable": false}
      }
    }
  }
])
```

**Step 3: Create Administrative User**

```javascript
// Insert primary administrator account
db.users.insertOne({
  "email": "{admin_email}",
  "password_hash": "{hashed_password}",
  "role": "admin",
  "tenant_id": "{tenant_id}",
  "permissions": ["all"],
  "created_at": new Date(),
  "status": "active",
  "profile": {
    "name": "{admin_name}",
    "phone": "{admin_phone}"
  }
})
```

#### 3. Backend Configuration Steps

**Step 1: Environment Variables Setup**

Create tenant-specific environment configuration:

```bash
# Add to .env or tenant-specific config file
TENANT_{TENANT_ID}_DATABASE_NAME=eko_tenant_{tenant_id}
TENANT_{TENANT_ID}_OPENAI_API_KEY={tenant_openai_key}
TENANT_{TENANT_ID}_TWILIO_SID={tenant_twilio_sid}
TENANT_{TENANT_ID}_TWILIO_TOKEN={tenant_twilio_token}
TENANT_{TENANT_ID}_WHATSAPP_NUMBER={tenant_whatsapp_number}
```

**Step 2: Qdrant Vector Database Setup**

```bash
# Create tenant-specific collections in Qdrant
curl -X PUT "http://localhost:6333/collections/{tenant_id}_knowledge_base" \
  -H "Content-Type: application/json" \
  -d '{
    "vectors": {
      "size": 1536,
      "distance": "Cosine"
    }
  }'

curl -X PUT "http://localhost:6333/collections/{tenant_id}_embeddings" \
  -H "Content-Type: application/json" \
  -d '{
    "vectors": {
      "size": 1536,
      "distance": "Cosine"
    }
  }'
```

**Step 3: MinIO Storage Setup**

```bash
# Create tenant-specific storage buckets
mc mb minio/tenant-{tenant_id}-documents
mc mb minio/tenant-{tenant_id}-media
mc mb minio/tenant-{tenant_id}-exports

# Set bucket policies for tenant isolation
mc policy set private minio/tenant-{tenant_id}-documents
mc policy set private minio/tenant-{tenant_id}-media
mc policy set private minio/tenant-{tenant_id}-exports
```

#### 4. Application Configuration

**Step 1: Register Tenant in System**

```python
# Add tenant registration to main database
main_db.tenants.insert_one({
    "tenant_id": "{tenant_id}",
    "business_name": "{business_name}",
    "database_name": "eko_tenant_{tenant_id}",
    "status": "active",
    "created_at": datetime.now(),
    "subscription_plan": "standard",
    "features_enabled": {
        "ai_responses": True,
        "multi_channel": True,
        "analytics": True,
        "api_access": True
    }
})
```

**Step 2: Initialize Default Categories**

```javascript
// Create default message categories
db.categories.insertMany([
  {
    "name": "general_inquiry",
    "description": "General customer questions",
    "ai_config": {
      "model": "gpt-4",
      "temperature": 0.7,
      "prompt_template": "You are a helpful customer service assistant..."
    },
    "routing": {
      "auto_respond": true,
      "escalate_keywords": ["urgent", "complaint", "manager"]
    }
  },
  {
    "name": "technical_support",
    "description": "Technical issues and troubleshooting",
    "ai_config": {
      "model": "gpt-4",
      "temperature": 0.5,
      "prompt_template": "You are a technical support specialist..."
    },
    "routing": {
      "auto_respond": false,
      "require_human": true
    }
  }
])
```

### Tenant Management Operations

#### Tenant Status Management

| Operation | Purpose | Implementation |
|-----------|---------|----------------|
| **Activate** | Enable tenant services | Update status to "active" in tenant registry |
| **Suspend** | Temporarily disable access | Set status to "suspended", block API access |
| **Archive** | Long-term storage mode | Move to archive status, reduce resource allocation |
| **Delete** | Complete removal | Remove all data, collections, and configurations |

#### Resource Monitoring

Track tenant resource usage:

- **API Call Limits**: Monitor and enforce rate limits per tenant
- **Storage Usage**: Track document and media storage consumption
- **Database Size**: Monitor collection sizes and query performance
- **AI Token Usage**: Track AI model usage and associated costs

#### Backup and Recovery

Implement tenant-specific backup procedures:

```bash
# Backup tenant database
mongodump --db eko_tenant_{tenant_id} --out /backups/tenant_{tenant_id}/$(date +%Y%m%d)

# Backup tenant files from MinIO
mc mirror minio/tenant-{tenant_id}-documents /backups/tenant_{tenant_id}/documents/

# Backup Qdrant collections
curl -X POST "http://localhost:6333/collections/{tenant_id}_knowledge_base/snapshots"
```

### Security Considerations

#### Data Isolation

- **Database Level**: Separate databases or collections per tenant
- **Application Level**: Tenant ID validation in all queries
- **API Level**: JWT tokens include tenant scope
- **Storage Level**: Isolated file storage with access controls

#### Access Control

- **Authentication**: Tenant-scoped user authentication
- **Authorization**: Role-based permissions within tenant context
- **API Security**: Rate limiting and request validation per tenant
- **Audit Logging**: Complete activity tracking for compliance

#### Compliance

- **Data Residency**: Configure data storage location per tenant requirements
- **Encryption**: At-rest and in-transit encryption for all tenant data
- **Retention Policies**: Configurable data retention per tenant
- **Export Capabilities**: Data export functionality for tenant migration

---

## 🔄 Development Workflow & Contribution Guidelines

This section outlines our development practices, contribution workflow, and coding standards to ensure consistent, high-quality code across the Eko platform.

### Git Workflow & Branching Strategy

We follow a structured Git workflow to maintain code quality and enable collaborative development:

#### Branch Structure

| Branch Type | Purpose | Naming Convention | Merge Target |
|-------------|---------|-------------------|--------------|
| **main** | Production-ready code | `main` | Protected, no direct commits |
| **develop** | Integration branch | `develop` | Merges to main via release |
| **feature** | New features | `feature/EKO-123-feature-name` | Merges to develop |
| **bugfix** | Bug fixes | `bugfix/EKO-456-fix-description` | Merges to develop |
| **hotfix** | Critical production fixes | `hotfix/EKO-789-critical-fix` | Merges to main and develop |
| **release** | Release preparation | `release/v1.2.0` | Merges to main |

#### Development Workflow Steps

1. **Create Feature Branch**
   ```bash
   # Start from develop branch
   git checkout develop
   git pull origin develop

   # Create feature branch
   git checkout -b feature/EKO-123-add-new-ai-model
   ```

2. **Development Process**
   ```bash
   # Make your changes
   # Write tests for new functionality
   # Update documentation as needed

   # Commit changes with descriptive messages
   git add .
   git commit -m "feat: add support for Claude AI model integration

   - Implement Claude API client in src/v3/chat/
   - Add model configuration options
   - Include fallback mechanism for API failures
   - Add comprehensive test coverage

   Closes EKO-123"
   ```

3. **Code Quality Checks**
   ```bash
   # Run code formatting
   black src/
   isort src/

   # Run linting
   flake8 src/
   mypy src/

   # Run tests
   pytest tests/ -v --cov=src/
   ```

4. **Push and Create Pull Request**
   ```bash
   # Push feature branch
   git push origin feature/EKO-123-add-new-ai-model

   # Create pull request via GitHub/GitLab interface
   # Include detailed description and testing notes
   ```

### Code Standards & Best Practices

#### Python Code Style

We follow PEP 8 with some specific conventions:

**Import Organization:**
```python
# Standard library imports
import os
import json
from datetime import datetime
from typing import Dict, List, Optional

# Third-party imports
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field
import openai

# Local imports
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB
from src.helper.logger import setup_new_logging
```

**Function Documentation:**
```python
def process_ai_response(
    message: str,
    tenant_id: str,
    model_config: Dict[str, Any],
    current_user: UserTenantDB
) -> Dict[str, Any]:
    """
    Process AI response for customer message.

    Args:
        message: Customer message to process
        tenant_id: Tenant identifier for isolation
        model_config: AI model configuration parameters
        current_user: Authenticated user context

    Returns:
        Dict containing AI response and metadata

    Raises:
        HTTPException: If AI processing fails
        ValidationError: If input parameters are invalid
    """
    logger = setup_new_logging(__name__)
    logger.info(f"Processing AI response for tenant {tenant_id}")

    # Implementation here
    pass
```

**Error Handling:**
```python
from src.core.exceptions import EkoException, TenantNotFoundError

try:
    result = process_tenant_data(tenant_id)
except TenantNotFoundError:
    logger.error(f"Tenant {tenant_id} not found")
    raise HTTPException(status_code=404, detail="Tenant not found")
except Exception as e:
    logger.error(f"Unexpected error: {str(e)}")
    raise EkoException("Internal processing error")
```

#### API Development Standards

**Endpoint Structure:**
```python
from fastapi import APIRouter, Depends, HTTPException
from src.core.security import get_tenant_info
from src.models.user import UserTenantDB

router = APIRouter(prefix="/v2/feature", tags=["Feature Management"])

@router.post("/create")
async def create_feature(
    request: FeatureCreateRequest,
    current_user: UserTenantDB = Depends(get_tenant_info)
) -> FeatureResponse:
    """
    Create a new feature for the tenant.

    - **request**: Feature creation parameters
    - **current_user**: Authenticated user with tenant context

    Returns the created feature with metadata.
    """
    # Implementation
    pass
```

**Response Models:**
```python
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class FeatureResponse(BaseModel):
    """Standard response model for feature operations."""

    id: str = Field(..., description="Unique feature identifier")
    name: str = Field(..., description="Feature name")
    status: str = Field(..., description="Feature status")
    created_at: datetime = Field(..., description="Creation timestamp")
    tenant_id: str = Field(..., description="Associated tenant ID")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

### Testing Standards

#### Test Structure

Organize tests to mirror the source code structure:

```
tests/
├── unit/
│   ├── core/
│   ├── models/
│   ├── v2/
│   └── v3/
├── integration/
│   ├── api/
│   └── database/
└── fixtures/
    ├── test_data.py
    └── mock_responses.py
```

#### Test Examples

**Unit Test Example:**
```python
import pytest
from unittest.mock import Mock, patch
from src.v2.chat.categorize.categorise import categorize_message

class TestMessageCategorization:
    """Test suite for message categorization functionality."""

    @pytest.fixture
    def mock_tenant_user(self):
        """Mock tenant user for testing."""
        user = Mock()
        user.tenant_id = "test_tenant"
        user.db = Mock()
        return user

    @patch('src.v2.chat.categorize.categorise.openai_client')
    def test_categorize_general_inquiry(self, mock_openai, mock_tenant_user):
        """Test categorization of general customer inquiry."""
        # Arrange
        mock_openai.chat.completions.create.return_value = Mock(
            choices=[Mock(message=Mock(content="general_inquiry"))]
        )

        # Act
        result = categorize_message(
            message="What are your business hours?",
            current_user=mock_tenant_user
        )

        # Assert
        assert result.category == "general_inquiry"
        assert result.confidence > 0.8
        mock_openai.chat.completions.create.assert_called_once()
```

**Integration Test Example:**
```python
import pytest
from fastapi.testclient import TestClient
from main import app

class TestFeatureAPI:
    """Integration tests for feature management API."""

    @pytest.fixture
    def client(self):
        """Test client for API calls."""
        return TestClient(app)

    @pytest.fixture
    def auth_headers(self):
        """Authentication headers for test requests."""
        # Get test JWT token
        token = get_test_jwt_token()
        return {"Authorization": f"Bearer {token}"}

    def test_create_feature_success(self, client, auth_headers):
        """Test successful feature creation."""
        # Arrange
        feature_data = {
            "name": "Test Feature",
            "description": "Test feature description",
            "enabled": True
        }

        # Act
        response = client.post(
            "/v2/feature/create",
            json=feature_data,
            headers=auth_headers
        )

        # Assert
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Test Feature"
        assert "id" in data
```

### Code Review Process

#### Pull Request Requirements

Before submitting a pull request, ensure:

1. **Code Quality**
   - [ ] Code follows style guidelines (black, isort, flake8)
   - [ ] Type hints are present and accurate
   - [ ] Docstrings are comprehensive and up-to-date
   - [ ] No commented-out code or debug statements

2. **Testing**
   - [ ] Unit tests cover new functionality
   - [ ] Integration tests verify API endpoints
   - [ ] All tests pass locally
   - [ ] Test coverage meets minimum threshold (80%)

3. **Documentation**
   - [ ] README updated if needed
   - [ ] API documentation reflects changes
   - [ ] Inline comments explain complex logic
   - [ ] Migration notes for breaking changes

4. **Security & Performance**
   - [ ] Tenant isolation maintained
   - [ ] No sensitive data in logs
   - [ ] Database queries optimized
   - [ ] Error handling comprehensive

#### Review Checklist for Reviewers

**Functionality Review:**
- Does the code solve the intended problem?
- Are edge cases handled appropriately?
- Is the solution scalable and maintainable?

**Code Quality Review:**
- Is the code readable and well-structured?
- Are naming conventions consistent?
- Is there appropriate separation of concerns?

**Security Review:**
- Are tenant boundaries respected?
- Is input validation comprehensive?
- Are authentication/authorization checks present?

**Performance Review:**
- Are database queries efficient?
- Is caching used appropriately?
- Are there any potential bottlenecks?

### Release Process

#### Version Management

We use semantic versioning (SemVer):
- **Major** (1.0.0): Breaking changes
- **Minor** (1.1.0): New features, backward compatible
- **Patch** (1.1.1): Bug fixes, backward compatible

#### Release Steps

1. **Prepare Release Branch**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.2.0
   ```

2. **Update Version Numbers**
   - Update `pyproject.toml`
   - Update API version in documentation
   - Create changelog entry

3. **Final Testing**
   ```bash
   # Run full test suite
   pytest tests/ -v --cov=src/

   # Run integration tests
   pytest tests/integration/ -v

   # Performance testing
   pytest tests/performance/ -v
   ```

4. **Merge and Tag**
   ```bash
   # Merge to main
   git checkout main
   git merge release/v1.2.0

   # Create tag
   git tag -a v1.2.0 -m "Release version 1.2.0"
   git push origin main --tags

   # Merge back to develop
   git checkout develop
   git merge main
   git push origin develop
   ```

---

## 📁 Project Structure & Key Components

The Eko project follows a modular architecture with clear separation of concerns across different API versions and functional domains.

### Root Directory Overview

```
echo_bot/
├── 📄 main.py                    # FastAPI application entry point
├── 📄 main_routes.py             # Centralized router management
├── 📄 pyproject.toml             # Project configuration and dependencies
├── 📄 requirements.txt           # Legacy dependency specification
├── 🐳 Dockerfile                # Container definition
├── 🐳 docker-compose.yml        # Multi-service orchestration
├── ⚙️ nginx.conf                # Load balancer configuration
├── ☸️ deployment.yaml           # Kubernetes deployment config
├── 📁 src/                      # Main source code directory
├── 📁 tests/                    # Test files and utilities
├── 📁 images/                   # Documentation assets
└── 📄 README.md                 # Project documentation
```

### Core Infrastructure (`src/core/`)

The foundation layer providing essential services across the application:

| Component | Purpose | Key Responsibilities |
|-----------|---------|---------------------|
| **`config.py`** | Configuration Management | Environment variables, database settings, API keys |
| **`database.py`** | Database Operations | MongoDB connections, query utilities, connection pooling |
| **`security.py`** | Authentication & Authorization | JWT handling, tenant validation, role-based access |
| **`exceptions.py`** | Error Handling | Custom exceptions, error responses, logging integration |
| **`activity_log.py`** | Activity Tracking | User action logging, audit trails, middleware integration |
| **`object_id.py`** | Data Utilities | MongoDB ObjectId validation and conversion utilities |

### Data Models (`src/models/`)

Pydantic models defining data structures and validation rules:

#### User & Security Models
- **`user.py`**: User accounts, profiles, authentication data
- **`security.py`**: Security tokens, permissions, access control
- **`profile.py`**: User profile information and preferences

#### Business Logic Models
- **`customers.py`**: Customer profiles, contact information, metadata
- **`products.py`**: Product catalog, descriptions, categorization
- **`category.py`**: Message categories, routing rules, AI configurations

#### Communication Models
- **`chat_history.py`**: Conversation logs, message threading
- **`chat_hist.py`**: Legacy chat history compatibility
- **`model_run.py`**: AI model execution requests and responses

#### Analytics & AI Models
- **`ReplyHandler.py`**: AI response processing and evaluation
- **`resolve.py`**: Query resolution and response generation
- **`evaluate.py`**: Performance metrics and quality assessment
- **`summary.py`**: Conversation summarization data
- **`qdrant_model.py`**: Vector database interaction models

#### Setup Models (`src/models/setup/`)
- **`business.py`**: Business configuration and settings
- **`category.py`**: Category setup and management
- **`product.py`**: Product configuration and catalog setup

### Helper Utilities (`src/helper/`)

Shared utility functions and helper classes:

| Utility | Functionality | Usage |
|---------|---------------|-------|
| **`logger.py`** | Logging Infrastructure | Centralized logging, multiple output formats |
| **`file_processing.py`** | Document Handling | File upload, processing, format conversion |
| **`qdrant.py`** | Vector Operations | Embedding storage, semantic search, indexing |
| **`resolve_llm.py`** | AI Model Management | Model selection, configuration, fallback handling |
| **`websocketmanager.py`** | Real-time Communication | WebSocket connections, message broadcasting |
| **`QuestionGenerator.py`** | Content Generation | AI-powered question generation from documents |
| **`TOCGenerator.py`** | Document Structure | Table of contents generation and navigation |
| **`chat_topic_identifier.py`** | Topic Analysis | Conversation topic detection and categorization |

### API Routes (`src/routes/`)

Core API endpoint definitions:

| Route File | Endpoints | Purpose |
|------------|-----------|---------|
| **`ask_guru.py`** | `/ask_guru/*` | AI assistant interactions, context analysis |
| **`respond.py`** | `/respond/*` | Main chat response processing pipeline |
| **`reply.py`** | `/reply/*` | Reply generation and management |
| **`send_reply.py`** | `/send_reply/*` | Message sending across channels |
| **`activity_logs.py`** | `/activity/*` | User activity tracking and audit logs |

### Modern API Implementation (`src/v2/`)

The current production API with enhanced features:

#### Knowledge Base Management (`src/v2/KB/`)

**Document Processing (`kb_setup/`)**:
- **`handle_documents.py`**: Document upload, parsing, and indexing
- **`sentence_node_process.py`**: Text segmentation and node creation
- **`sentence_splitter.py`**: Intelligent text splitting for optimal chunks
- **`handle_url.py`**: Web content extraction and processing
- **`gemini_toc.py`**: AI-powered table of contents generation

**Vector Operations (`qdrant/`)**:
- **`qdrant_client.py`**: Vector database client and connection management
- **`qd_search.py`**: Semantic search and similarity matching
- **`handle_nodes.py`**: Vector node management and operations

#### Advanced Chat System (`src/v2/chat/`)

**Message Processing**:
- **`categorize/`**: Message classification and routing
- **`contextualize/`**: Conversation context management
- **`handle_image/`**: Image processing and text extraction
- **`identify_product/`**: Product recognition from queries

#### Business Dashboard (`src/v2/dashboard/`)

**Analytics & Insights**:
- **`overview/`**: Business metrics, AI performance, customer analytics
- **`customers/`**: Customer management and interaction history
- **`products/`**: Product catalog and performance tracking
- **`topic/`**: Conversation topic analysis and trending

**Management Features**:
- **`cta/`**: Call-to-action management and email notifications
- **`evaluate/`**: AI response quality evaluation and metrics
- **`channels/`**: Multi-channel conversation management
- **`users/`**: User management and access control

#### External Integrations (`src/v2/external_hooks/`)

**Communication Channels (`whatsapp_webhook/`)**:
- **`twilio_hooks.py`**: Twilio WhatsApp integration
- **`sociar_hooks.py`**: Sociar WhatsApp provider integration
- **`send_message.py`**: Message sending across providers
- **`email_sender/`**: Email notification system

#### Widget System (`src/v2/widget/`)

**Embeddable Chat Widgets**:
- **`chat/`**: Widget chat engine with handlers, processors, streaming
- **`model/`**: Widget data models and configuration
- **`route/`**: Widget-specific API endpoints

#### Settings & Configuration (`src/v2/settings/`)

**System Configuration**:
- **`ai_enable_disable/`**: AI feature toggles per tenant
- **`user_access/`**: User permission and access management
- **`credits.py`**: Usage tracking and billing integration

### Next-Generation API (`src/v3/`)

Experimental features and next-generation implementations:

#### Advanced Chat (`src/v3/chat/`)
- **`response.py`**: Enhanced response generation with tool calling
- **`tools.py`**: AI tool definitions and execution handlers
- **`detection.py`**: Advanced detection algorithms and processing
- **`client.py`**: Improved API client implementations
- **`models.py`**: Next-generation data models

### Supporting Systems

#### Background Processing (`src/background_tasks/`)
- **`assign_agent.py`**: Automatic agent assignment logic
- **`reply_processing.py`**: Asynchronous reply processing

#### Tenant Management (`src/tenant/`)
- **`tenant_api.py`**: Multi-tenant architecture support and APIs

#### Legacy Systems
- **`src/reply/`**: Legacy chat engine and sentiment analysis
- **`src/qdrant/`**: Legacy vector search implementations
- **`src/widget/`**: Legacy widget system (being migrated to v2)

#### Demo Applications
- **`src/streamlitdemo/`**: Interactive Streamlit demonstrations
- **Root demo files**: Various demo implementations for testing

### Key Architectural Principles

#### Modular Design
- **Separation of Concerns**: Clear boundaries between different functional areas
- **Version Isolation**: Multiple API versions can coexist without conflicts
- **Plugin Architecture**: Extensible design for adding new features

#### Scalability Considerations
- **Horizontal Scaling**: Stateless design enables easy horizontal scaling
- **Database Optimization**: Proper indexing and query optimization
- **Caching Strategy**: Multiple levels of caching for performance

#### Maintainability Features
- **Code Organization**: Logical grouping of related functionality
- **Documentation**: Comprehensive inline documentation and type hints
- **Testing Structure**: Organized test files matching source structure

---

## 🔌 API Documentation

Eko provides comprehensive RESTful APIs for all platform functionality. The API is organized into logical groups with consistent authentication and response patterns.

### API Base Information

| Property | Value | Description |
|----------|-------|-------------|
| **Base URL** | `http://localhost:8000` | Default development server |
| **API Title** | Eko Backend | Eko AI API |
| **API Version** | 0.1.0 | Current API version |
| **Authentication** | OAuth2 Bearer Token | JWT-based authentication |
| **Content Type** | `application/json` | Standard JSON request/response |
| **Documentation** | `/docs` | Interactive Swagger UI |

### Authentication & Tenant Isolation

All protected endpoints require OAuth2 Bearer token authentication with tenant-scoped access. The authentication system ensures complete tenant isolation and role-based access control.

#### Authentication Flow

1. **Login**: Submit credentials to `/login` endpoint to obtain access token
2. **Token Usage**: Include token in Authorization header: `Authorization: Bearer <token>`
3. **Token Verification**: Use `/verify_token` to validate token status
4. **Extended Tokens**: Request extended validity tokens via `/extended_token`

#### Tenant Isolation Mechanisms

**JWT Token Structure**: Each JWT token contains tenant-specific claims that ensure data isolation:
- `tenant_id`: Unique tenant identifier for database routing
- `user_role`: Role-based permissions within tenant context
- `permissions`: Granular access control for specific operations
- `database_name`: Tenant-specific database name for data isolation

**Database-Level Isolation**:
- Separate database collections per tenant (e.g., `eko_tenant_{tenant_id}`)
- All queries automatically filtered by tenant context
- Cross-tenant data access prevented at application level

**API-Level Security**:
- Tenant validation on every authenticated request
- Role-based endpoint access control
- Resource-level permissions enforcement

### User Management & Authentication APIs

#### Authentication Endpoints

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/get_tenant_id` | GET | Get tenant ID from slug | Query: `slug` (string, required) | JSON: tenant information | 200, 404, 422 |
| `/login` | POST | User authentication | Form: `username`, `password` | JSON: access token, user details | 200, 401, 422 |
| `/verify_token` | GET | Validate JWT token | Header: `Authorization: Bearer <token>` | JSON: user details, token status | 200, 401 |
| `/extended_token` | POST | Generate extended validity token | JSON: `username`, `password`, `client_id`, `days` (1-365) | JSON: extended access token | 200, 401, 422 |

**Authentication Details:**
- **Login**: Returns JWT access token with tenant context and user permissions
- **Token Verification**: Validates token and returns current user information
- **Extended Tokens**: Generate tokens with custom validity period for long-running integrations
- **Tenant Resolution**: Maps tenant slug to internal tenant ID for multi-tenant routing

#### Agent Management

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/agents/invite` | POST | Invite new agent | JSON: `AgentInvitation` object | JSON: invitation token, registration link | 200, 403, 422 |
| `/agents/register` | POST | Register agent with invitation token | JSON: `AgentRegistration` object | JSON: user account details | 200, 400, 422 |
| `/users/roles/count` | GET | Get user count by role | None (authenticated request) | JSON: role-based user statistics | 200, 401 |

**Agent Management Details:**
- **Invitation**: Only admins and supervisors can invite new agents
- **Registration**: Uses secure invitation tokens with expiration
- **Role Counting**: Provides dashboard analytics for user distribution

### Customer Management APIs

#### Customer Data & Interactions

| Endpoint | Method | Purpose | Key Parameters | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/customers/ai_responses` | GET | Fetch AI responses with filtering | `search`, `tags`, `cta`, `status`, `topic`, `sentiment`, `language`, `page`, `page_size` | JSON: paginated AI response data | 200, 401, 422 |
| `/customers/{customer_id}/latest-messages` | GET | Get latest customer messages | Path: `customer_id` (integer) | JSON: latest message object | 200, 401, 404, 422 |
| `/customers/chat-messages/` | POST | Retrieve chat messages | JSON array: `chat_data_ids` (strings) | JSON: array of chat message objects | 200, 401, 422 |
| `/customers/responses_summary_flow` | GET | Get response summary flow | `customer_id`, `start_date`, `end_date`, `page`, `page_size` | JSON: paginated summary data | 200, 401, 422 |

**Customer Data Features:**
- **Advanced Filtering**: Multi-parameter filtering for AI responses with search, tags, sentiment, and language
- **Pagination Support**: Configurable page size (1-100) with page-based navigation
- **Date Range Filtering**: Flexible date filtering with ISO 8601 datetime format
- **Tenant Isolation**: All customer data automatically filtered by tenant context

#### Customer Utilities

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/customers/get_tags` | GET | Fetch distinct tags | None (authenticated) | JSON: array of tag strings | 200, 401 |
| `/customers/get_call_to_action` | GET | Get call-to-action options | None (authenticated) | JSON: array of CTA options | 200, 401 |
| `/customers/saved_replies` | GET | Get saved reply templates | None (authenticated) | JSON: array of `SavedReply` objects | 200, 401 |
| `/customers/save_reply` | POST | Save new reply template | JSON: `SavedReply` object | JSON: created reply object | 200, 401, 422 |
| `/customers/get_message_media` | GET | Get message media content | Query: `message_id` (string, required) | JSON: `MessageMedia` object | 200, 401, 404, 422 |

**Utility Features:**
- **Tag Management**: Dynamic tag system for categorizing customer interactions
- **Template System**: Reusable reply templates for common customer inquiries
- **Media Handling**: Secure media retrieval with tenant-scoped access control

### Reporting & Analytics APIs

#### Usage Metrics

| Endpoint | Method | Purpose | Key Parameters | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/reporting/usage-metrics` | GET | Detailed cost breakdown | `start_date`, `end_date`, `response_id` (optional) | JSON: token counts, USD/NPR costs, AI/Twilio breakdown | 200, 401, 422 |
| `/reporting/metrics` | GET | AI performance metrics | `start_date`, `end_date`, `product_id`, `category_id` (optional) | JSON: performance analytics with filtering | 200, 401, 422 |
| `/reporting/trending_categories` | GET | Category trend analysis | `start_date`, `end_date`, `category_id`, `total_documents`, `page` | JSON: paginated category trends | 200, 401, 422 |
| `/reporting/trending_products` | GET | Product trend analysis | `start_date`, `end_date`, `product_id`, `total_documents`, `page` | JSON: paginated product trends | 200, 401, 422 |

**Usage Metrics Features:**
- **Cost Analysis**: Detailed breakdown of AI token usage and communication costs in multiple currencies
- **Performance Tracking**: AI model performance metrics with date range and category filtering
- **Trend Analysis**: Identify trending topics and products with pagination support
- **Multi-Currency Support**: Cost reporting in both USD and NPR for international deployments

#### Operational Metrics

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/reporting/average_processing_time` | POST | Processing time analytics | JSON: `metricDate` object | JSON: average processing time data | 200, 401, 422 |
| `/reporting/channel_msg_count` | POST | Message count by channel | JSON: `metricDate` object | JSON: channel-wise message statistics | 200, 401, 422 |
| `/reporting/cta_count` | POST | Call-to-action metrics | JSON: `metricDate` object | JSON: CTA generation and resolution stats | 200, 401, 422 |
| `/reporting/evaluation_count` | POST | Evaluation statistics | JSON: `metricDate` object | JSON: response evaluation metrics | 200, 401, 422 |
| `/reporting/message_count` | POST | Total message analytics | JSON: `metricDate` object | JSON: comprehensive message statistics | 200, 401, 422 |
| `/reporting/unique_users_per_day` | POST | Daily unique user metrics | JSON: `metricDate` object | JSON: daily active user analytics | 200, 401, 422 |
| `/reporting/language_count` | POST | Language usage statistics | JSON: `metricDate` object | JSON: language distribution data | 200, 401, 422 |
| `/reporting/human-intervention` | POST | Human intervention metrics | JSON: `metricDate` object | JSON: escalation and handoff statistics | 200, 401, 422 |
| `/reporting/topic_count` | POST | Topic generation analytics | JSON: `metricDate` object | JSON: topic analysis and categorization data | 200, 401, 422 |

**Operational Metrics Features:**
- **Time-Based Analytics**: All metrics support flexible date range filtering with `start_date` and `end_date`
- **Channel Analysis**: Track message volume and performance across WhatsApp, email, and web channels
- **Quality Metrics**: Monitor AI response quality through evaluation and human intervention tracking
- **Multilingual Support**: Language usage analytics for international customer bases

### Customer Profile & Communication APIs

#### Profile Management

| Endpoint | Method | Purpose | Response Schema |
|----------|--------|---------|-----------------|
| `/Profile/chat-message/profile/{chat_data_id}` | GET | Get chat message profile | Path: `chat_data_id` (string) |
| `/Profile/{customer_id}/profile` | GET | Get customer profile | CustomerProfileResponse with trending data, sentiment, language |

#### User Account Management

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/users/reset_password` | POST | Reset user password | JSON: ResetPasswordRequest |
| `/users/change_password` | POST | Change user password | JSON: `old_password`, `new_password`, `confirm_password` |
| `/user/delete` | DELETE | Delete user account | Query: `user_id` (string) |
| `/users/subordinates` | GET | Get subordinate users | Pagination, search, role filtering |
| `/user/update` | PUT | Update user role | Query: `user_id`, `role_update_request` |

### Channel & Communication APIs

#### Conversation Management

| Endpoint | Method | Purpose | Key Parameters |
|----------|--------|---------|----------------|
| `/fetch_conversation` | POST | Fetch conversation history | `user_id`, `limit`, `page_number` |
| `/delete_conversation` | POST | Delete conversation | `user_id` (string) |
| `/additional_information` | GET | Get additional response info | `response_id` (string) |

#### Data Retrieval

| Endpoint | Method | Purpose | Response Format |
|----------|--------|---------|-----------------|
| `/get_products` | GET | Get available products | Array of product strings |
| `/get_languages` | GET | Get supported languages | Array of language strings |
| `/get_sentiment` | GET | Get sentiment options | Array of sentiment strings |

#### Customer Operations

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/new_customer` | POST | Create new customer | JSON: `name`, `phone_number`, `email`, `message`, `channel`, `template_id` |
| `/change_msg_flag` | PUT | Update message flag | Query: `flag` (boolean) |
| `/get_msg_flag` | GET | Get current message flag | Returns current flag status |

### Content Management APIs

#### Prompt Management

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/prompts/all` | GET | Get all prompts | Returns array of prompt objects |
| `/prompts/` | POST | Create new prompt | JSON: `name`, `text`, `model` |
| `/prompts/{prompt_id}` | GET | Get specific prompt | Path: `prompt_id` |
| `/prompts/{prompt_id}` | PUT | Update prompt | JSON: PromptUpdate object |
| `/prompts/{prompt_id}` | DELETE | Delete prompt | Path: `prompt_id` |

### AI & Processing APIs

#### Core AI Model Operations

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/respond/final` | POST | Main AI response processing | JSON: `ModelRunRequest` with chat data, user info, media | JSON: `ModelResponse` with AI reply, metadata | 200, 401, 422 |
| `/ask_guru/async_context` | POST | AI assistant with context | JSON: Chat context and message data | JSON: contextual AI response | 200, 401, 422 |

**AI Processing Features:**
- **Multi-Model Support**: Integration with OpenAI GPT-4, Google Gemini, and other AI models
- **Context Awareness**: Maintains conversation history and customer context for personalized responses
- **Media Processing**: Handles image uploads with OCR and content extraction capabilities
- **Tenant-Specific Models**: AI model configurations isolated per tenant for customized behavior

**ModelRunRequest Schema:**
- `user_name`: User identifier (string, default: "test_eko")
- `user_id`: Numeric user ID (integer, default: 0)
- `message`: Current message text (string, required)
- `chat_data`: Previous conversation history (array, default: [])
- `chat_data_format`: Format specification ("role_data" or "role_content")
- `primary_product_code`: Product context (string, optional)
- `media_ids`: Attached media identifiers (array, optional)
- `tags`: Message categorization tags (array, default: ["Facebook"])

#### Evaluation & Quality Assurance

| Endpoint | Method | Purpose | Request Schema | Response | Status Codes |
|----------|--------|---------|----------------|----------|--------------|
| `/evaluation/submit` | POST | Submit response evaluation | JSON: `Eval_Request` object | JSON: evaluation confirmation | 200, 401, 422 |
| `/evaluation/fetch` | POST | Fetch evaluations with filtering | JSON: `Fetch_Evaluations` object | JSON: paginated evaluation data | 200, 401, 422 |
| `/evaluation/flag` | POST | Flag evaluation for review | JSON: `Flag_Evaluations` object | JSON: flag status update | 200, 401, 422 |
| `/evaluation/update` | POST | Update evaluation status | JSON: `Update_Evaluation` object | JSON: update confirmation | 200, 401, 422 |
| `/evaluation/filters` | GET | Get available evaluation filters | None (authenticated) | JSON: `Eval_Filters` object | 200, 401 |

**Quality Assurance Features:**
- **Response Evaluation**: Like/dislike rating system with detailed feedback categories
- **Review Workflow**: Flag problematic responses for human review and resolution
- **Filter System**: Advanced filtering by categories, products, sentiment, and reviewers
- **Analytics Integration**: Evaluation data feeds into performance analytics and reporting

### Knowledge Base & Search APIs

#### Document Management

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/qdrant/search` | POST | Vector similarity search | JSON: `query`, `metadata`, `level`, `prompt` |
| `/qdrant/search_all` | POST | Search all collections | JSON: `collection_name`, `limit`, `offset`, `filter` |
| `/qdrant/image/upload` | POST | Upload images | Multipart: `files` (binary array) |
| `/qdrant/add_resource` | POST | Add resource URLs | JSON: `url` (array), `id` |
| `/qdrant/handle_update` | POST | Update document | JSON: `document_id`, `collection_name`, `title` |
| `/qdrant/update_image` | POST | Update document images | JSON: `document_id`, `collection_name`, `images` |
| `/qdrant/update_node` | POST | Update knowledge node | JSON: UpdateNode object |

### CTA & Task Management APIs

#### Call-to-Action Management

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/cta/create` | POST | Create new CTA | JSON: CTACreate with name, description, type, channel |
| `/cta/list` | GET | List CTAs with filtering | Query parameters for status, type, date filtering |
| `/cta/{cta_id}` | GET | Get specific CTA | Path: `cta_id` |
| `/cta/{cta_id}` | PUT | Update CTA | JSON: CTAUpdate object |
| `/cta/{cta_id}` | DELETE | Delete CTA | Path: `cta_id` |
| `/cta/resolve` | POST | Resolve CTA | JSON: `cta_type`, `id`, `resolved_by`, `remarks` |

### Communication Integration APIs

#### WhatsApp Integration

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/send_reply` | POST | Send reply message | JSON: `user_id`, `message`, `ai_response_id`, `media_url`, `channel` |
| `/whatsapp/template/create` | POST | Create WhatsApp template | JSON: WhatsAppTemplate object |
| `/whatsapp/template/list` | GET | List WhatsApp templates | Returns array of templates |

#### Email Notifications

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/email/status` | GET | Get email notification status | Returns EmailStatus object |
| `/email/status` | PUT | Update email status | JSON: EmailStatus object |
| `/email/recipients` | GET | Get email recipients | Returns array of EmailRecipient objects |
| `/email/recipients` | POST | Add email recipient | JSON: EmailRecipient object |
| `/email/recipients/{recipient_id}` | PUT | Update recipient | JSON: EmailRecipient object |
| `/email/recipients/{recipient_id}` | DELETE | Delete recipient | Path: `recipient_id` |

### AI Configuration APIs

#### AI Settings

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/ai/schedule` | GET | Get AI schedule | Returns current AI schedule configuration |
| `/ai/schedule` | PUT | Update AI schedule | JSON: `schedule`, `start_time`, `end_time` |
| `/ai/channel/{channel}/schedule` | GET | Get channel-specific schedule | Path: `channel` |
| `/ai/channel/{channel}/schedule` | PUT | Update channel schedule | JSON: `schedule`, `start_time`, `end_time` |

### Credit & Billing APIs

#### Credit Management

| Endpoint | Method | Purpose | Response Schema |
|----------|--------|---------|-----------------|
| `/credits/balance` | GET | Get credit balance | CreditBalance with total, remaining, cost division |
| `/credits/transactions` | GET | Get transaction history | TransactionResponse with pagination |
| `/credits/add` | POST | Add credits | JSON: amount, description |

### Widget & External APIs

#### Widget Integration

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/widget/google/user` | POST | Google user integration | JSON: GoogleUser object |
| `/widget/follow_up` | POST | Send follow-up message | JSON: `message_id`, `follow_up_msg` |

#### Message Verification

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/verify_message` | POST | Verify AI response | JSON: MessageVerificationRequest |

### Topic Management APIs

#### Topic Operations

| Endpoint | Method | Purpose | Request Schema |
|----------|--------|---------|----------------|
| `/topics/` | POST | Create topic | JSON: TopicItem with name, type, parent_id |
| `/topics/` | GET | List topics | Returns array of TopicResponse objects |
| `/topics/{topic_id}` | GET | Get specific topic | Path: `topic_id` |
| `/topics/{topic_id}` | PUT | Update topic | JSON: TopicItem object |
| `/topics/{topic_id}` | DELETE | Delete topic | Path: `topic_id` |

### API Response Standards

#### Standard Response Format

All API endpoints return responses in a consistent JSON format with appropriate HTTP status codes. Successful responses typically include relevant data objects, while error responses include detailed error information for debugging.

#### HTTP Status Codes

| Status Code | Meaning | Usage | Example Scenarios |
|-------------|---------|-------|-------------------|
| **200** | OK | Successful request | Data retrieved, operation completed successfully |
| **201** | Created | Resource created successfully | New user registered, CTA created |
| **400** | Bad Request | Invalid request data | Missing required fields, invalid data format |
| **401** | Unauthorized | Authentication required/failed | Missing token, expired token, invalid credentials |
| **403** | Forbidden | Insufficient permissions | User lacks required role, tenant access denied |
| **404** | Not Found | Resource not found | Customer ID doesn't exist, endpoint not available |
| **422** | Unprocessable Entity | Validation error | Invalid parameter values, schema validation failed |
| **500** | Internal Server Error | Server-side error | Database connection failed, AI service unavailable |

#### Error Response Format

```json
{
  "detail": [
    {
      "loc": ["body", "field_name"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

#### Authentication Requirements

Most endpoints require OAuth2 Bearer token authentication. Public endpoints include basic information retrieval and some widget-related functionality. Authentication tokens are tenant-scoped and include role-based access control.

**Token Usage:**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Tenant Isolation:**
- All authenticated requests automatically filtered by tenant context
- Cross-tenant data access prevented at application and database levels
- Role-based permissions enforced within tenant boundaries

---

## 💡 Usage Examples

This section provides practical examples of common Eko platform use cases and implementation patterns.

### Basic Chat Interaction

#### Scenario: Customer Service Automation

**Use Case**: Automatically respond to customer inquiries with AI-powered responses while maintaining conversation context.

**Implementation Flow**:
1. Customer sends message via WhatsApp or web widget
2. Eko processes the message and generates contextual response
3. Response is sent back through the same channel
4. Conversation history is maintained for future context

**Key Configuration**:
- Set up AI model preferences (GPT-4, Gemini)
- Configure response templates and tone
- Define escalation rules for complex queries
- Enable sentiment analysis for priority routing

### WhatsApp Business Integration

#### Scenario: E-commerce Customer Support

**Use Case**: Integrate WhatsApp Business API to handle product inquiries, order status, and customer support.

**Setup Requirements**:
- Twilio or Sociar WhatsApp Business account
- Webhook endpoint configuration
- Product catalog integration
- Order management system connection

**Implementation Steps**:
1. Configure WhatsApp webhook endpoints in Eko
2. Set up product identification AI models
3. Create response templates for common inquiries
4. Configure escalation rules for complex issues
5. Enable order tracking integration

**Benefits**:
- 24/7 automated customer support
- Instant product information delivery
- Reduced response time for common queries
- Seamless escalation to human agents

### Knowledge Base Management

#### Scenario: Technical Documentation Support

**Use Case**: Upload technical documentation and enable AI to answer customer questions based on the knowledge base.

**Implementation Process**:
1. **Document Upload**: Upload PDF, DOCX, or TXT files containing product documentation
2. **AI Processing**: Eko automatically processes and indexes documents using vector embeddings
3. **Semantic Search**: AI searches relevant content to answer customer questions
4. **Response Generation**: Contextual responses generated from knowledge base content

**Document Types Supported**:
- Product manuals and specifications
- FAQ documents and help guides
- Policy documents and terms of service
- Technical documentation and API guides

**AI Enhancement Features**:
- Automatic table of contents generation
- Question generation from documents
- Content summarization and key point extraction
- Multi-language document support

### Dashboard Analytics Implementation

#### Scenario: Business Performance Monitoring

**Use Case**: Monitor customer service performance, AI effectiveness, and business metrics through comprehensive dashboards.

**Key Metrics Tracked**:
- **Customer Metrics**: Total customers, new acquisitions, engagement rates
- **AI Performance**: Response accuracy, resolution rates, escalation frequency
- **Channel Analytics**: Message volume across WhatsApp, email, web widget
- **Topic Analysis**: Trending customer inquiry topics and categories

**Dashboard Configuration**:
1. Set up tenant-specific analytics
2. Configure metric collection intervals
3. Define custom KPIs and alerts
4. Create automated reporting schedules

**Business Intelligence Features**:
- Real-time performance monitoring
- Trend analysis and forecasting
- Customer behavior insights
- AI optimization recommendations

### Multi-Tenant Deployment

#### Scenario: Service Provider Platform

**Use Case**: Deploy Eko as a service provider platform managing multiple client businesses.

**Architecture Benefits**:
- **Data Isolation**: Each client's data completely separated
- **Custom Branding**: Tenant-specific styling and branding
- **Independent Configuration**: Separate AI models and settings per tenant
- **Scalable Billing**: Usage-based billing per tenant

**Deployment Strategy**:
1. **Infrastructure Setup**: Configure multi-tenant database architecture
2. **Tenant Onboarding**: Streamlined process for adding new clients
3. **Resource Management**: Monitor and allocate resources per tenant
4. **Billing Integration**: Track usage and generate billing reports

### Web Widget Integration

#### Scenario: Website Customer Support

**Use Case**: Embed intelligent chat widgets on websites to provide instant customer support.

**Widget Features**:
- **Customizable Styling**: Match website branding and design
- **Real-time Streaming**: Live conversation updates and typing indicators
- **File Upload Support**: Allow customers to share images and documents
- **Conversation History**: Maintain context across multiple sessions

**Integration Steps**:
1. Generate widget configuration for tenant
2. Customize styling and behavior settings
3. Embed widget code in website
4. Configure AI response models
5. Set up escalation rules and human handoff

**Advanced Features**:
- **Proactive Engagement**: Trigger conversations based on user behavior
- **Multi-language Support**: Automatic language detection and response
- **Mobile Optimization**: Responsive design for mobile devices
- **Analytics Integration**: Track widget performance and user engagement

### Email Notification System

#### Scenario: Automated Customer Communication

**Use Case**: Set up automated email notifications for important customer service events.

**Notification Types**:
- **Ticket Creation**: Notify agents when new support tickets are created
- **Escalation Alerts**: Alert supervisors when issues require attention
- **Resolution Updates**: Inform customers when issues are resolved
- **Performance Reports**: Send periodic performance summaries

**Configuration Process**:
1. **Email Setup**: Configure SMTP settings and sender authentication
2. **Template Creation**: Design email templates for different notification types
3. **Trigger Rules**: Define when notifications should be sent
4. **Recipient Management**: Manage email lists and preferences

### API Integration Patterns

#### Scenario: Custom Application Integration

**Use Case**: Integrate Eko APIs with existing business applications and workflows.

**Common Integration Patterns**:
- **CRM Integration**: Sync customer data and interaction history
- **Helpdesk Integration**: Create tickets and track resolution status
- **E-commerce Integration**: Product catalog sync and order management
- **Analytics Integration**: Export data for business intelligence tools

**Implementation Considerations**:
- **Authentication**: Implement JWT token management
- **Rate Limiting**: Handle API rate limits and quotas
- **Error Handling**: Implement robust error handling and retry logic
- **Data Synchronization**: Maintain data consistency across systems

### Performance Optimization

#### Scenario: High-Volume Customer Service

**Use Case**: Optimize Eko deployment for high-volume customer service operations.

**Optimization Strategies**:
- **Horizontal Scaling**: Deploy multiple application instances
- **Database Optimization**: Implement proper indexing and query optimization
- **Caching Strategy**: Use Redis for session and response caching
- **Load Balancing**: Distribute traffic across multiple servers

**Monitoring and Maintenance**:
- **Performance Metrics**: Monitor response times and throughput
- **Resource Usage**: Track CPU, memory, and database performance
- **Error Monitoring**: Implement comprehensive error tracking
- **Capacity Planning**: Plan for growth and scaling requirements

### Compliance and Security

#### Scenario: Enterprise Security Requirements

**Use Case**: Implement Eko in environments with strict security and compliance requirements.

**Security Features**:
- **Data Encryption**: End-to-end encryption for all data transmission
- **Access Control**: Role-based permissions and multi-factor authentication
- **Audit Logging**: Comprehensive activity tracking and audit trails
- **Data Retention**: Configurable data retention policies

**Compliance Considerations**:
- **GDPR Compliance**: Data privacy and right to deletion
- **HIPAA Compliance**: Healthcare data protection requirements
- **SOC 2 Compliance**: Security and availability controls
- **Industry Standards**: Adherence to relevant industry regulations

---

---

## � New Developer Guide

Welcome to the Eko development team! This guide will help you understand the codebase, set up your development environment, and make your first contribution.

### Quick Development Setup

#### Prerequisites
- **Python 3.11+**: Core programming language
- **Docker & Docker Compose**: For local services
- **Git**: Version control
- **IDE**: VS Code or PyCharm recommended

#### 1. Repository Setup
```bash
# Clone and setup
git clone <repository-url>
cd echo_bot
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
```

#### 2. Environment Configuration
Create `.env` file:
```bash
ENVIRONMENT=development
DEBUG=true
MONGO_URI=mongodb://localhost:27017
DATABASE_NAME=eko_development
OPENAI_API_KEY=your_development_key
```

#### 3. Start Development Environment
```bash
# Start services
docker-compose up -d

# Run application
uvicorn main:app --reload --port 8000
```

#### 4. Verify Setup
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

### Testing Framework

#### Test Structure and Organization

**Test Directory Structure**:
```
tests/
├── unit/                    # Unit tests for individual components
│   ├── test_models.py      # Data model validation tests
│   ├── test_helpers.py     # Helper function tests
│   └── test_core.py        # Core functionality tests
├── integration/            # Integration tests for API endpoints
│   ├── test_chat_api.py    # Chat API integration tests
│   ├── test_auth.py        # Authentication flow tests
│   └── test_webhooks.py    # Webhook integration tests
├── e2e/                    # End-to-end tests
│   ├── test_user_flows.py  # Complete user journey tests
│   └── test_tenant_setup.py # Tenant creation workflows
└── fixtures/               # Test data and fixtures
    ├── sample_data.json    # Sample test data
    └── mock_responses.py   # Mock API responses
```

#### Testing Best Practices

**Unit Testing Guidelines**:
- **Test Isolation**: Each test should be independent and not rely on other tests
- **Mock External Dependencies**: Use mocks for database calls, API requests, and external services
- **Test Edge Cases**: Include tests for error conditions and boundary cases
- **Descriptive Test Names**: Use clear, descriptive names that explain what is being tested

**Integration Testing Approach**:
- **API Endpoint Testing**: Test complete request/response cycles
- **Database Integration**: Test actual database operations with test data
- **Authentication Testing**: Verify JWT token generation and validation
- **Multi-Tenant Testing**: Ensure proper tenant isolation in tests

#### Running Tests

**Test Execution Commands**:

```bash
# Run all tests
pytest

# Run specific test categories
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# Run tests with coverage reporting
pytest --cov=src --cov-report=html tests/

# Run tests in parallel (faster execution)
pytest -n auto tests/

# Run tests with verbose output
pytest -v tests/

# Run specific test file
pytest tests/unit/test_models.py

# Run specific test function
pytest tests/unit/test_models.py::test_user_model_validation
```

**Continuous Testing During Development**:

```bash
# Watch for file changes and auto-run tests
pytest-watch tests/

# Run tests on specific file changes
ptw --runner "pytest tests/unit/" src/models/
```

### Code Quality and Standards

#### Code Style and Formatting

**Automated Code Formatting**:
```bash
# Format code with Black
black src/ tests/

# Sort imports with isort
isort src/ tests/

# Check code style with flake8
flake8 src/ tests/

# Type checking with mypy
mypy src/
```

**Pre-commit Hooks Setup**:
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files
```

#### Documentation Standards

**Code Documentation Requirements**:
- **Function Docstrings**: All functions must have comprehensive docstrings
- **Class Documentation**: Classes should include purpose and usage examples
- **Type Hints**: All function parameters and return values must be type-hinted
- **Inline Comments**: Complex logic should include explanatory comments

**Documentation Example**:
```python
def process_chat_message(
    message: str,
    customer_id: str,
    tenant_id: str,
    context: Optional[Dict[str, Any]] = None
) -> ChatResponse:
    """
    Process incoming chat message and generate AI response.

    Args:
        message: The customer's message text
        customer_id: Unique identifier for the customer
        tenant_id: Tenant identifier for multi-tenant isolation
        context: Optional conversation context and metadata

    Returns:
        ChatResponse: Generated response with metadata

    Raises:
        ValidationError: If message or IDs are invalid
        AIServiceError: If AI service is unavailable

    Example:
        >>> response = process_chat_message(
        ...     "Hello, I need help",
        ...     "customer_123",
        ...     "tenant_abc"
        ... )
        >>> print(response.message)
        "Hello! I'm here to help you. What can I assist you with today?"
    """
```

### Debugging Techniques

#### Application Debugging

**Local Debugging Setup**:
```bash
# Run application with debug mode
ENVIRONMENT=development DEBUG=true uvicorn main:app --reload

# Enable detailed logging
LOG_LEVEL=DEBUG python -m uvicorn main:app --reload

# Run with Python debugger
python -m pdb main.py
```

**IDE Debugging Configuration**:
- **VS Code**: Configure launch.json for FastAPI debugging
- **PyCharm**: Set up run configuration with environment variables
- **Breakpoint Debugging**: Use IDE breakpoints for step-through debugging

#### Database Debugging

**MongoDB Query Debugging**:
```bash
# Connect to development database
mongo mongodb://localhost:27017/eko_development

# Enable query profiling
db.setProfilingLevel(2)

# View slow queries
db.system.profile.find().sort({ts: -1}).limit(5)

# Explain query execution
db.collection.find({query}).explain("executionStats")
```

**Qdrant Vector Database Debugging**:
```bash
# Check collection status
curl http://localhost:6333/collections

# View collection info
curl http://localhost:6333/collections/{collection_name}

# Search debugging
curl -X POST http://localhost:6333/collections/{collection_name}/points/search \
  -H "Content-Type: application/json" \
  -d '{"vector": [...], "limit": 5, "with_payload": true}'
```

#### API Debugging

**Request/Response Debugging**:
- **FastAPI Automatic Docs**: Use `/docs` endpoint for interactive API testing
- **Logging Middleware**: Enable request/response logging for debugging
- **Postman Collections**: Create and maintain Postman collections for API testing

**Performance Debugging**:
```bash
# Profile application performance
python -m cProfile -o profile_output.prof main.py

# Analyze profile results
python -m pstats profile_output.prof

# Memory profiling
pip install memory-profiler
python -m memory_profiler main.py
```

### Development Tools and Utilities

#### Database Management

**MongoDB Development Tools**:
- **MongoDB Compass**: Visual database management
- **Studio 3T**: Advanced MongoDB IDE
- **Command Line Tools**: mongo shell for direct database access

**Database Seeding and Migration**:
```bash
# Seed development database with sample data
python scripts/seed_development_data.py

# Run database migrations
python scripts/migrate_database.py

# Reset development database
python scripts/reset_dev_database.py
```

#### API Development Tools

**API Testing and Documentation**:
- **Swagger UI**: Available at `/docs` endpoint
- **ReDoc**: Alternative documentation at `/redoc`
- **Postman**: API testing and collection management
- **HTTPie**: Command-line HTTP client for quick testing

**Example API Testing Commands**:
```bash
# Test authentication endpoint
http POST localhost:8000/auth/login email=<EMAIL> password=testpass

# Test chat endpoint with authentication
http POST localhost:8000/respond/final \
  Authorization:"Bearer <token>" \
  message="Hello" customer_id="123" tenant_id="abc"

# Test file upload
http --form POST localhost:8000/kb/upload \
  Authorization:"Bearer <token>" \
  <EMAIL>
```

### Performance Optimization

#### Development Performance Tips

**Code Optimization Strategies**:
- **Database Query Optimization**: Use proper indexing and efficient queries
- **Caching Implementation**: Implement Redis caching for frequently accessed data
- **Async/Await Usage**: Use asynchronous programming for I/O operations
- **Connection Pooling**: Implement proper database connection pooling

**Memory Management**:
- **Object Lifecycle**: Properly manage object creation and destruction
- **Memory Profiling**: Regular memory usage analysis during development
- **Garbage Collection**: Understanding Python garbage collection behavior

#### Load Testing

**Local Load Testing Setup**:
```bash
# Install load testing tools
pip install locust

# Run load tests
locust -f tests/load/locustfile.py --host=http://localhost:8000

# Artillery load testing
npm install -g artillery
artillery run tests/load/artillery-config.yml
```

---

## 🏗️ Architecture Overview

This section provides a comprehensive overview of Eko's system architecture, data flow patterns, and design principles that enable scalable, maintainable, and secure customer service automation.

### System Architecture

#### High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   Web Widgets   │   Mobile Apps   │   WhatsApp/Email/SMS       │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Load Balancer (Nginx)                      │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                           │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   FastAPI v2    │   FastAPI v3    │   Legacy APIs               │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Chat Engine    │  AI Processing  │  Analytics Engine           │
│  - Contextualize│  - OpenAI/Gemini│  - Performance Metrics      │
│  - Categorize   │  - LlamaIndex   │  - Customer Insights        │
│  - Route        │  - LangChain    │  - Topic Analysis           │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Data Layer                                 │
├─────────────────┬─────────────────┬─────────────────────────────┤
│    MongoDB      │     Qdrant      │      MinIO                  │
│  - User Data    │  - Embeddings   │  - Documents                │
│  - Chat History │  - Vector Search│  - Media Files              │
│  - Config       │  - Semantic KB  │  - Backups                  │
└─────────────────┴─────────────────┴─────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                   External Services                            │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   AI Services   │  Communication  │   Monitoring                │
│  - OpenAI API   │  - Twilio       │  - Logging                  │
│  - Google AI    │  - Sociar       │  - Metrics                  │
│  - Embeddings   │  - SMTP         │  - Alerting                 │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

#### Core Components

**API Gateway Layer**:
- **FastAPI Application**: High-performance async web framework
- **Authentication Middleware**: JWT-based tenant-scoped authentication
- **Rate Limiting**: Request throttling and quota management
- **Request Validation**: Pydantic model validation for all inputs
- **Error Handling**: Centralized exception handling and logging

**Business Logic Layer**:
- **Chat Processing Engine**: Message analysis, context management, response generation
- **AI Integration Layer**: Multiple AI model support with fallback mechanisms
- **Analytics Engine**: Real-time metrics collection and business intelligence
- **Tenant Management**: Multi-tenant isolation and configuration management

**Data Persistence Layer**:
- **Primary Database (MongoDB)**: User data, conversations, configurations
- **Vector Database (Qdrant)**: Semantic search and knowledge base embeddings
- **Object Storage (MinIO)**: Document storage, media files, backups
- **Caching Layer (Redis)**: Session management, response caching

### Data Flow Architecture

#### Request Processing Flow

```
1. Client Request
   ↓
2. Load Balancer (Nginx)
   ↓
3. API Gateway (FastAPI)
   ├── Authentication Validation
   ├── Rate Limiting Check
   └── Request Validation
   ↓
4. Business Logic Processing
   ├── Tenant Context Resolution
   ├── Message Analysis
   ├── AI Model Selection
   └── Response Generation
   ↓
5. Data Layer Operations
   ├── Database Queries
   ├── Vector Search
   └── File Operations
   ↓
6. Response Assembly
   ├── Response Formatting
   ├── Metadata Addition
   └── Logging
   ↓
7. Client Response
```

#### Chat Message Processing Pipeline

**Phase 1: Message Ingestion**
1. **Channel Reception**: Receive message from WhatsApp, web widget, or API
2. **Authentication**: Validate tenant and user permissions
3. **Message Validation**: Ensure message format and content validity
4. **Context Loading**: Retrieve conversation history and customer data

**Phase 2: AI Processing**
1. **Contextualization**: Analyze message within conversation context
2. **Intent Classification**: Determine message category and intent
3. **Product Identification**: Identify relevant products or services
4. **Knowledge Base Search**: Semantic search for relevant information

**Phase 3: Response Generation**
1. **Model Selection**: Choose appropriate AI model based on context
2. **Prompt Engineering**: Construct optimized prompts for AI models
3. **Response Generation**: Generate contextual response using AI
4. **Quality Validation**: Validate response quality and appropriateness

**Phase 4: Response Delivery**
1. **Response Formatting**: Format response for target channel
2. **Delivery**: Send response through appropriate communication channel
3. **Logging**: Record interaction for analytics and audit
4. **Feedback Loop**: Update models based on interaction outcomes

### Multi-Tenant Architecture Design

#### Tenant Isolation Strategy

**Data Isolation Levels**:

| Level | Implementation | Security | Performance |
|-------|---------------|----------|-------------|
| **Database Level** | Separate databases per tenant | Highest | Good |
| **Collection Level** | Tenant-prefixed collections | High | Better |
| **Document Level** | Tenant ID in all documents | Medium | Best |
| **Application Level** | Runtime tenant filtering | Low | Excellent |

**Current Implementation**: Hybrid approach using database-level and collection-level isolation

#### Tenant Context Management

**Request-Level Tenant Resolution**:
```python
# Tenant context flow
1. JWT Token Validation
   ├── Extract tenant_id from token
   ├── Validate tenant status (active/suspended)
   └── Load tenant configuration

2. Database Connection Resolution
   ├── Select tenant-specific database
   ├── Apply tenant-specific connection settings
   └── Initialize tenant context

3. Request Processing
   ├── All queries scoped to tenant
   ├── All responses filtered by tenant
   └── All logging tagged with tenant_id
```

### Security Architecture

**Multi-Layer Security Model**:
- **Network Security**: VPC isolation, security groups, DDoS protection
- **Application Security**: JWT authentication, role-based access control, API rate limiting
- **Data Security**: Encryption at rest and in transit, database access controls, audit logging
- **Infrastructure Security**: Container security, secrets management, vulnerability scanning

**Data Protection**:
- **Encryption**: AES-256 for data at rest, TLS 1.3 for data in transit
- **Access Control**: Role-based permissions with tenant isolation
- **Compliance**: GDPR, HIPAA, SOC 2 compliance features
- **Audit Logging**: Comprehensive activity tracking for compliance

### Integration Architecture

**AI Service Integration**:
- **Multi-Provider Support**: OpenAI, Google AI with fallback mechanisms
- **Service Abstraction**: Unified interface for different AI providers
- **Error Handling**: Robust retry logic and circuit breakers
- **Performance Optimization**: Response caching and connection pooling

**Communication Channels**:
- **Webhook Architecture**: Standardized webhook handling for all channels
- **Message Processing**: Asynchronous message queue integration
- **Multi-Channel Support**: WhatsApp, email, web widgets with unified processing
- **Reliability**: Retry mechanisms and failure recovery

---

## 🤝 Contributing Guidelines

We welcome contributions to the Eko platform! This section outlines the process for contributing code, reporting issues, and participating in the development community.

### Getting Started with Contributions

#### Prerequisites for Contributors

**Technical Requirements**:
- **Python 3.11+** with experience in FastAPI development
- **Git** knowledge and GitHub workflow familiarity
- **Docker** for local development and testing
- **Understanding** of AI/ML concepts and APIs

**Recommended Knowledge**:
- **MongoDB** database operations and optimization
- **Vector databases** (Qdrant) and semantic search
- **Multi-tenant architecture** patterns
- **RESTful API** design and development

#### Setting Up Development Environment

**Step 1: Fork and Clone**
```bash
# Fork the repository on GitHub
# Clone your fork locally
git clone https://github.com/your-username/echo_bot.git
cd echo_bot

# Add upstream remote
git remote add upstream https://github.com/original-repo/echo_bot.git
```

**Step 2: Development Setup**
```bash
# Create development branch
git checkout -b feature/your-feature-name

# Set up development environment
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Start development services
docker-compose -f docker-compose.dev.yml up -d
```

### Contribution Process

#### Types of Contributions

**Code Contributions**:
- **Bug Fixes**: Resolve existing issues and improve stability
- **Feature Development**: Add new functionality and capabilities
- **Performance Improvements**: Optimize existing code and algorithms
- **Documentation**: Improve code documentation and user guides

**Non-Code Contributions**:
- **Issue Reporting**: Identify and report bugs or enhancement requests
- **Documentation**: Improve README, API docs, and user guides
- **Testing**: Add test cases and improve test coverage
- **Community Support**: Help other users and contributors

#### Development Workflow

**Step 1: Issue Creation/Assignment**
1. **Check Existing Issues**: Search for existing issues before creating new ones
2. **Create Detailed Issues**: Provide clear descriptions, steps to reproduce, expected behavior
3. **Issue Assignment**: Comment on issues you'd like to work on for assignment
4. **Discussion**: Participate in issue discussions for clarification

**Step 2: Development Process**
1. **Branch Creation**: Create feature branches from the main branch
2. **Code Development**: Follow coding standards and best practices
3. **Testing**: Write and run tests for all new functionality
4. **Documentation**: Update relevant documentation for changes

**Step 3: Pull Request Submission**
1. **Pre-submission Checklist**: Ensure all tests pass and code is formatted
2. **Pull Request Creation**: Provide detailed description of changes
3. **Code Review**: Respond to feedback and make requested changes
4. **Merge Process**: Maintainers will merge approved pull requests

### Code Standards and Guidelines

#### Code Style Requirements

**Python Code Standards**:
- **PEP 8 Compliance**: Follow Python style guidelines
- **Type Hints**: All functions must include type annotations
- **Docstrings**: Comprehensive documentation for all public functions
- **Import Organization**: Use isort for consistent import ordering

**Code Formatting Tools**:
```bash
# Format code before committing
black src/ tests/
isort src/ tests/
flake8 src/ tests/
mypy src/
```

#### Testing Requirements

**Test Coverage Standards**:
- **Minimum Coverage**: 80% test coverage for new code
- **Unit Tests**: All new functions must have unit tests
- **Integration Tests**: API endpoints require integration tests
- **Documentation Tests**: Docstring examples should be testable

**Test Execution**:
```bash
# Run all tests before submitting PR
pytest tests/ --cov=src --cov-report=html

# Ensure no test failures
pytest tests/ -v

# Check test coverage
coverage report --show-missing
```

#### Documentation Standards

**Code Documentation**:
- **Function Docstrings**: Include purpose, parameters, returns, and examples
- **Class Documentation**: Describe class purpose and usage patterns
- **Inline Comments**: Explain complex logic and business rules
- **API Documentation**: Update OpenAPI specs for new endpoints

**Documentation Example**:
```python
def create_tenant(
    tenant_data: TenantCreateRequest,
    admin_user: UserCreateRequest
) -> TenantResponse:
    """
    Create a new tenant with initial configuration and admin user.

    This function sets up a complete tenant environment including:
    - Database collections and indexes
    - Default configuration settings
    - Administrative user account
    - Vector database collections

    Args:
        tenant_data: Tenant configuration and business information
        admin_user: Initial administrator user details

    Returns:
        TenantResponse: Created tenant information with status

    Raises:
        TenantExistsError: If tenant ID already exists
        ValidationError: If input data is invalid
        DatabaseError: If database operations fail

    Example:
        >>> tenant_data = TenantCreateRequest(
        ...     tenant_id="acme_corp",
        ...     business_name="ACME Corporation"
        ... )
        >>> admin_user = UserCreateRequest(
        ...     email="<EMAIL>",
        ...     name="Admin User"
        ... )
        >>> result = create_tenant(tenant_data, admin_user)
        >>> print(result.tenant_id)
        "acme_corp"
    """
```

### Issue Reporting and Bug Reports

#### Bug Report Template

**Required Information**:
- **Environment Details**: OS, Python version, deployment method
- **Steps to Reproduce**: Clear, numbered steps to reproduce the issue
- **Expected Behavior**: What should happen
- **Actual Behavior**: What actually happens
- **Error Messages**: Complete error messages and stack traces
- **Additional Context**: Screenshots, logs, configuration details

**Bug Report Example**:
```markdown
## Bug Report

### Environment
- OS: Ubuntu 20.04
- Python: 3.11.2
- Deployment: Docker Compose
- Version: v2.1.0

### Steps to Reproduce
1. Create new tenant via API
2. Upload document to knowledge base
3. Attempt to search uploaded document
4. Observe error response

### Expected Behavior
Document should be searchable immediately after upload

### Actual Behavior
Search returns empty results, logs show vector indexing error

### Error Messages
```
ERROR: Failed to index document in Qdrant
VectorIndexError: Collection not found: tenant_123_knowledge_base
```

### Additional Context
- Issue occurs only with new tenants
- Existing tenants work correctly
- Manual collection creation resolves the issue
```

#### Feature Request Template

**Feature Request Information**:
- **Feature Description**: Clear description of the requested feature
- **Use Case**: Business justification and user scenarios
- **Proposed Implementation**: Technical approach suggestions
- **Alternatives Considered**: Other solutions evaluated
- **Additional Context**: Related issues, external references

### Code Review Process

#### Review Criteria

**Code Quality Checklist**:
- **Functionality**: Code works as intended and meets requirements
- **Testing**: Adequate test coverage and passing tests
- **Documentation**: Clear documentation and comments
- **Performance**: No obvious performance issues
- **Security**: No security vulnerabilities or data exposure
- **Style**: Follows project coding standards

#### Review Timeline

**Review Process**:
- **Initial Review**: Within 2-3 business days
- **Follow-up Reviews**: Within 1-2 business days after updates
- **Final Approval**: Maintainer approval required for merge
- **Merge Timeline**: Approved PRs merged within 1 business day

### Community Guidelines

#### Communication Standards

**Professional Conduct**:
- **Respectful Communication**: Treat all community members with respect
- **Constructive Feedback**: Provide helpful, actionable feedback
- **Inclusive Environment**: Welcome contributors of all backgrounds and skill levels
- **Problem-Solving Focus**: Focus on solutions rather than blame

#### Getting Help

**Support Channels**:
- **GitHub Issues**: Technical questions and bug reports
- **Discussions**: General questions and community interaction
- **Documentation**: Comprehensive guides and API reference
- **Code Examples**: Sample implementations and use cases

### Recognition and Attribution

#### Contributor Recognition

**Contribution Tracking**:
- **Contributor List**: All contributors recognized in project documentation
- **Release Notes**: Significant contributions highlighted in release notes
- **Community Highlights**: Regular recognition of outstanding contributions
- **Mentorship Opportunities**: Experienced contributors can mentor newcomers

---

## 🔧 Troubleshooting

This section provides solutions to common issues encountered when setting up, configuring, and running the Eko platform.

### Installation and Setup Issues

#### Python Environment Problems

**Issue: Python Version Compatibility**
```
Error: Python 3.11+ required, found Python 3.9
```

**Solution**:
```bash
# Install Python 3.11 using pyenv
curl https://pyenv.run | bash
pyenv install 3.11.7
pyenv global 3.11.7

# Or use conda
conda create -n eko python=3.11
conda activate eko
```

**Issue: Dependency Installation Failures**
```
Error: Failed building wheel for some-package
```

**Solutions**:
```bash
# Update pip and setuptools
pip install --upgrade pip setuptools wheel

# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install build-essential python3-dev

# Install system dependencies (macOS)
xcode-select --install
brew install python@3.11

# Clear pip cache and retry
pip cache purge
pip install -r requirements.txt
```

#### Docker and Container Issues

**Issue: Docker Services Not Starting**
```
Error: Cannot connect to the Docker daemon
```

**Solutions**:
```bash
# Start Docker service (Linux)
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Verify Docker installation
docker --version
docker-compose --version
```

**Issue: Port Conflicts**
```
Error: Port 8000 is already in use
```

**Solutions**:
```bash
# Find process using port
lsof -i :8000
netstat -tulpn | grep :8000

# Kill process or change port
kill -9 <process_id>

# Or modify docker-compose.yml
ports:
  - "8001:8000"  # Use different host port
```

### Database Connection Issues

#### MongoDB Connection Problems

**Issue: MongoDB Connection Refused**
```
Error: pymongo.errors.ServerSelectionTimeoutError
```

**Solutions**:
```bash
# Check MongoDB service status
sudo systemctl status mongod

# Start MongoDB service
sudo systemctl start mongod

# Check MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Test connection
mongo --eval "db.adminCommand('ismaster')"
```

**Issue: Authentication Failures**
```
Error: Authentication failed
```

**Solutions**:
```bash
# Create MongoDB user
mongo admin
> db.createUser({
    user: "eko_user",
    pwd: "secure_password",
    roles: ["readWriteAnyDatabase"]
  })

# Update connection string
MONGO_URI=*********************************************************
```

#### Qdrant Vector Database Issues

**Issue: Qdrant Service Unavailable**
```
Error: Connection refused to Qdrant server
```

**Solutions**:
```bash
# Start Qdrant with Docker
docker run -p 6333:6333 qdrant/qdrant

# Check Qdrant health
curl http://localhost:6333/health

# View Qdrant logs
docker logs qdrant-container
```

**Issue: Collection Creation Failures**
```
Error: Failed to create vector collection
```

**Solutions**:
```bash
# Check collection exists
curl http://localhost:6333/collections

# Create collection manually
curl -X PUT "http://localhost:6333/collections/test_collection" \
  -H "Content-Type: application/json" \
  -d '{
    "vectors": {
      "size": 1536,
      "distance": "Cosine"
    }
  }'
```

### API and Application Issues

#### Authentication and Authorization Problems

**Issue: JWT Token Validation Failures**
```
Error: Invalid token or token expired
```

**Solutions**:
```bash
# Check token expiration
python -c "
import jwt
token = 'your_jwt_token'
decoded = jwt.decode(token, options={'verify_signature': False})
print(decoded)
"

# Generate new token
curl -X POST http://localhost:8000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'
```

**Issue: Tenant Access Denied**
```
Error: Tenant not found or access denied
```

**Solutions**:
```bash
# Verify tenant exists in database
mongo eko_db
> db.tenants.find({"tenant_id": "your_tenant_id"})

# Check tenant status
> db.tenants.updateOne(
    {"tenant_id": "your_tenant_id"},
    {"$set": {"status": "active"}}
  )
```

#### AI Service Integration Issues

**Issue: OpenAI API Failures**
```
Error: OpenAI API rate limit exceeded
```

**Solutions**:
```bash
# Check API key validity
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $OPENAI_API_KEY"

# Implement rate limiting
# Add to .env file
OPENAI_RATE_LIMIT=60  # requests per minute
OPENAI_RETRY_ATTEMPTS=3
```

**Issue: AI Response Generation Failures**
```
Error: Failed to generate AI response
```

**Solutions**:
```bash
# Check AI service configuration
python -c "
from src.helper.resolve_llm import get_ai_client
client = get_ai_client('openai')
print(client.test_connection())
"

# Enable fallback models
# In tenant configuration
{
  "ai_config": {
    "primary_model": "gpt-4",
    "fallback_models": ["gpt-3.5-turbo", "gemini-pro"]
  }
}
```

### Performance and Scaling Issues

#### High Memory Usage

**Issue: Application Memory Leaks**
```
Warning: Memory usage exceeding limits
```

**Solutions**:
```bash
# Monitor memory usage
docker stats eko-backend

# Profile memory usage
pip install memory-profiler
python -m memory_profiler main.py

# Optimize database connections
# In configuration
MONGO_MAX_POOL_SIZE=10
MONGO_MIN_POOL_SIZE=5
```

#### Slow API Response Times

**Issue: High Response Latency**
```
Warning: API response times > 5 seconds
```

**Solutions**:
```bash
# Enable query profiling
mongo eko_db
> db.setProfilingLevel(2, {slowms: 1000})
> db.system.profile.find().sort({ts: -1}).limit(5)

# Add database indexes
> db.chat_history.createIndex({"customer_id": 1, "timestamp": -1})
> db.ai_response.createIndex({"tenant_id": 1, "timestamp": -1})

# Enable Redis caching
REDIS_URL=redis://localhost:6379
CACHE_TTL=300  # 5 minutes
```

### Deployment and Production Issues

#### Container Orchestration Problems

**Issue: Kubernetes Pod Failures**
```
Error: Pod stuck in CrashLoopBackOff
```

**Solutions**:
```bash
# Check pod logs
kubectl logs -f deployment/eko-backend

# Describe pod for events
kubectl describe pod <pod-name>

# Check resource limits
kubectl top pods
kubectl describe node <node-name>
```

**Issue: Load Balancer Configuration**
```
Error: 502 Bad Gateway from Nginx
```

**Solutions**:
```bash
# Check Nginx configuration
nginx -t

# View Nginx logs
tail -f /var/log/nginx/error.log

# Test upstream servers
curl -I http://backend-server:8000/health
```

### Monitoring and Debugging

#### Log Analysis

**Common Log Patterns**:
```bash
# Find error patterns
grep -i "error" /var/log/eko/app.log | tail -20

# Monitor real-time logs
tail -f /var/log/eko/app.log | grep -i "tenant_id"

# Analyze performance logs
awk '/response_time/ {sum+=$NF; count++} END {print "Avg:", sum/count}' app.log
```

#### Health Check Failures

**Issue: Health Endpoint Returning Errors**
```
Error: Health check failed - database connection
```

**Solutions**:
```bash
# Test individual components
curl http://localhost:8000/health/database
curl http://localhost:8000/health/qdrant
curl http://localhost:8000/health/ai-services

# Check service dependencies
docker-compose ps
kubectl get services
```

## 📋 API Reference Summary

This section provides a quick reference for developers working with the Eko API, organized by functional areas and highlighting key endpoints for common development tasks.

### Core API Endpoints by Functional Area

#### 🔐 Authentication & Tenant Management
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `POST /login` | POST | User authentication | JWT tokens with tenant context |
| `GET /verify_token` | GET | Token validation | Real-time token status verification |
| `GET /get_tenant_id` | GET | Tenant resolution | Multi-tenant routing support |
| `POST /extended_token` | POST | Long-lived tokens | 1-365 day validity for integrations |

#### 👥 User & Agent Management
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `POST /agents/invite` | POST | Agent invitation | Role-based invitation system |
| `POST /agents/register` | POST | Agent registration | Secure token-based registration |
| `GET /users/subordinates` | GET | User management | Pagination and role filtering |
| `GET /users/roles/count` | GET | User analytics | Dashboard statistics |

#### 💬 Customer Service & AI Processing
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `POST /respond/final` | POST | Main AI processing | Multi-model AI integration |
| `GET /customers/ai_responses` | GET | Response analytics | Advanced filtering and pagination |
| `POST /customers/chat-messages/` | POST | Message retrieval | Bulk message operations |
| `GET /customers/{customer_id}/latest-messages` | GET | Customer history | Real-time message tracking |

#### 📊 Analytics & Reporting
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `GET /reporting/usage-metrics` | GET | Cost analysis | Multi-currency cost breakdown |
| `GET /reporting/metrics` | GET | Performance analytics | AI model performance tracking |
| `GET /reporting/trending_categories` | GET | Trend analysis | Category-based insights |
| `POST /reporting/message_count` | POST | Volume analytics | Channel-wise message statistics |

#### 📚 Knowledge Base & Search
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `POST /qdrant/search` | POST | Vector search | Semantic similarity search |
| `POST /qdrant/image/upload` | POST | Media upload | Multi-file image processing |
| `POST /qdrant/add_resource` | POST | Resource management | URL-based content ingestion |

#### 🎯 Task & CTA Management
| Endpoint | Method | Purpose | Key Features |
|----------|--------|---------|--------------|
| `POST /cta/create` | POST | Create call-to-action | Ticket and booking management |
| `GET /cta/list` | GET | CTA listing | Status and type filtering |
| `POST /cta/resolve` | POST | CTA resolution | Workflow completion tracking |

### Quick Development Reference

#### Authentication Headers
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

#### Common Query Parameters
- **Pagination**: `page` (1-based), `page_size` (1-100)
- **Date Filtering**: `start_date`, `end_date` (ISO 8601 format)
- **Search**: `search` (string), `tags` (array), `categories` (array)

#### Standard HTTP Status Codes
- **200**: Success - Request completed successfully
- **401**: Unauthorized - Authentication required or failed
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource doesn't exist
- **422**: Validation Error - Invalid request parameters

#### Tenant Isolation Features
- **Automatic Filtering**: All data automatically scoped to tenant
- **Database Isolation**: Separate collections per tenant
- **Role-Based Access**: Granular permissions within tenant context
- **Resource Limits**: Per-tenant quotas and rate limiting

### Development Best Practices

#### API Integration Guidelines
1. **Always include tenant context** in authenticated requests
2. **Implement proper error handling** for all status codes
3. **Use pagination** for large data sets (page_size ≤ 100)
4. **Cache authentication tokens** and refresh before expiration
5. **Follow rate limiting** guidelines to avoid throttling

#### Security Considerations
- **JWT Token Management**: Secure storage and automatic refresh
- **Tenant Validation**: Verify tenant access for all operations
- **Input Validation**: Validate all request parameters
- **Error Handling**: Don't expose sensitive information in errors

#### Performance Optimization
- **Batch Operations**: Use bulk endpoints when available
- **Efficient Filtering**: Apply filters to reduce response size
- **Caching Strategy**: Cache frequently accessed data
- **Async Processing**: Use async endpoints for heavy operations

### Getting Additional Help

#### Support Resources

**Documentation and Guides**:
- **API Documentation**: Available at `/docs` endpoint
- **Architecture Guide**: See Architecture Overview section
- **Configuration Reference**: See Configuration section
- **Deployment Guides**: See Deployment section

**Community Support**:
- **GitHub Issues**: Report bugs and request features
- **GitHub Discussions**: Ask questions and share experiences
- **Stack Overflow**: Tag questions with `eko-platform`

**Professional Support**:
- **Enterprise Support**: Available for production deployments
- **Consulting Services**: Architecture and implementation guidance
- **Training Programs**: Team training and onboarding

---

## 📄 License & Credits

### License Information

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for complete details.

#### MIT License Summary

```
MIT License

Copyright (c) 2024 Eko Platform Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

### Third-Party Acknowledgments

#### Core Dependencies

**Web Framework and API**:
- **[FastAPI](https://fastapi.tiangolo.com/)** - Modern, fast web framework for building APIs
- **[Uvicorn](https://www.uvicorn.org/)** - Lightning-fast ASGI server implementation
- **[Pydantic](https://pydantic-docs.helpmanual.io/)** - Data validation using Python type annotations
- **[Starlette](https://www.starlette.io/)** - Lightweight ASGI framework/toolkit

**AI and Machine Learning**:
- **[OpenAI](https://openai.com/)** - Advanced language models and AI capabilities
- **[LlamaIndex](https://www.llamaindex.ai/)** - Data framework for LLM applications
- **[LangChain](https://langchain.com/)** - Framework for developing applications with LLMs
- **[Google AI](https://ai.google/)** - Google's AI models and services
- **[NLTK](https://www.nltk.org/)** - Natural Language Toolkit for text processing

**Databases and Storage**:
- **[MongoDB](https://www.mongodb.com/)** - Document database for application data
- **[Qdrant](https://qdrant.tech/)** - Vector database for semantic search
- **[MinIO](https://min.io/)** - High-performance object storage
- **[Redis](https://redis.io/)** - In-memory data structure store for caching

**Communication and Integration**:
- **[Twilio](https://www.twilio.com/)** - Cloud communications platform
- **[Requests](https://requests.readthedocs.io/)** - HTTP library for Python
- **[WebSockets](https://websockets.readthedocs.io/)** - Real-time communication support

#### Development and Deployment Tools

**Development Tools**:
- **[Docker](https://www.docker.com/)** - Containerization platform
- **[Nginx](https://nginx.org/)** - High-performance web server and load balancer
- **[Kubernetes](https://kubernetes.io/)** - Container orchestration platform
- **[Pytest](https://pytest.org/)** - Testing framework for Python

**Code Quality and Formatting**:
- **[Black](https://black.readthedocs.io/)** - Python code formatter
- **[isort](https://isort.readthedocs.io/)** - Import sorting utility
- **[Flake8](https://flake8.pycqa.org/)** - Code linting and style checking
- **[MyPy](https://mypy.readthedocs.io/)** - Static type checking for Python

### Contributors

#### Core Team

**Project Maintainers**:
- **Lead Developer**: [Name] - Architecture and core development
- **AI/ML Engineer**: [Name] - AI integration and optimization
- **DevOps Engineer**: [Name] - Infrastructure and deployment
- **Documentation Lead**: [Name] - Documentation and user guides

#### Community Contributors

We extend our gratitude to all community members who have contributed to the Eko platform through code contributions, bug reports, feature requests, and documentation improvements.

**Special Recognition**:
- Contributors who have submitted significant features or improvements
- Community members who have helped with testing and feedback
- Documentation contributors who have improved user experience
- Bug reporters who have helped identify and resolve issues

### Acknowledgments

#### Inspiration and Research

**Academic Research**:
- Natural Language Processing research from leading universities
- Multi-tenant architecture patterns from industry best practices
- AI safety and ethics guidelines from research institutions

**Open Source Community**:
- The Python community for excellent libraries and frameworks
- FastAPI community for web framework innovation
- AI/ML community for advancing the field of artificial intelligence
- DevOps community for containerization and orchestration tools

#### Industry Partners

**Technology Partners**:
- Cloud service providers for infrastructure support
- AI service providers for model access and capabilities
- Communication service providers for multi-channel integration

### Support and Sponsorship

#### How to Support the Project

**Financial Support**:
- **GitHub Sponsors**: Support ongoing development through GitHub Sponsors
- **Corporate Sponsorship**: Enterprise sponsorship opportunities available
- **Consulting Services**: Professional services and support contracts

**Non-Financial Support**:
- **Code Contributions**: Submit pull requests and improvements
- **Bug Reports**: Help identify and resolve issues
- **Documentation**: Improve guides and documentation
- **Community Support**: Help other users and contributors

#### Sponsorship Recognition

**Current Sponsors**:
- [Sponsor organizations and individuals will be listed here]

**Sponsorship Benefits**:
- **Logo Placement**: Sponsor logos in README and documentation
- **Priority Support**: Enhanced support for sponsored features
- **Feature Influence**: Input on roadmap and feature development
- **Recognition**: Acknowledgment in release notes and announcements

### Contact Information

#### Project Contacts

**General Inquiries**:
- **Email**: [<EMAIL>]
- **Website**: [https://eko-platform.com]
- **GitHub**: [https://github.com/organization/echo_bot]

**Security Issues**:
- **Security Email**: [<EMAIL>]
- **Responsible Disclosure**: Follow responsible disclosure practices
- **GPG Key**: [Public key for encrypted communications]

**Business Inquiries**:
- **Partnerships**: [<EMAIL>]
- **Enterprise Sales**: [<EMAIL>]
- **Licensing**: [<EMAIL>]

---

## 🚀 Production Deployment & Scaling

### Architecture Overview

**Workload Isolation with Proportional Scaling (5:3:2 Ratio)**

- **50% instances**: High-priority webhooks (`/reply_generate`, `/sociar_webhook`, `/whatsapp_webhook`)
- **30% instances**: Document processing with session affinity (`/setup_files`, `/process-documents`, `/add-documents`, `/check_status`)
- **20% instances**: General API (everything else)

### Manual Scaling Profiles

#### **LOCAL Profile** (5 total: 3+1+1)
```bash
# Default - uses docker-compose.override.yml automatically
docker-compose up -d

# Check instances
docker-compose ps
```

#### **SMALL Profile** (10 total: 5+3+2)
```bash
# Deploy small scale
docker-compose -f docker-compose.yml -f docker-compose.small.yml up -d

# Verify scaling
docker-compose -f docker-compose.yml -f docker-compose.small.yml ps
```

#### **MEDIUM Profile** (20 total: 10+6+4)
```bash
# Deploy medium scale
docker-compose -f docker-compose.yml -f docker-compose.medium.yml up -d

# Check all 20 instances are running
docker-compose -f docker-compose.yml -f docker-compose.medium.yml ps
```

#### **HIGH Profile** (30 total: 15+9+6)
```bash
# Deploy high scale
docker-compose -f docker-compose.yml -f docker-compose.high.yml up -d

# Verify all 30 instances
docker-compose -f docker-compose.yml -f docker-compose.high.yml ps
```

#### **MAX Profile** (40 total: 20+12+8)
```bash
# Deploy maximum scale
docker-compose -f docker-compose.yml -f docker-compose.max.yml up -d

# Check all 40 instances are running
docker-compose -f docker-compose.yml -f docker-compose.max.yml ps
```

### Scaling Commands Reference

#### **Stop Services**
```bash
# Stop current deployment
docker-compose down

# Or stop specific profile
docker-compose -f docker-compose.yml -f docker-compose.medium.yml down
```

#### **Scale Individual Services**
```bash
# Scale webhooks to 8 instances (while keeping others)
docker-compose up -d --scale app-webhooks=8

# Scale documents to 5 instances
docker-compose up -d --scale app-documents=5

# Scale general API to 3 instances
docker-compose up -d --scale app=3
```

#### **Check Service Status**
```bash
# View all running containers
docker-compose ps

# Check specific service logs
docker-compose logs app-webhooks
docker-compose logs app-documents
docker-compose logs app

# Monitor resource usage
docker stats
```

### Access Points

- **Development**: `http://localhost:8201`
- **Production**: `https://your-domain.com`
- **Traefik Dashboard**: `http://localhost:8202`
- **API Documentation**: `http://localhost:8201/docs`

### Environment Setup

```bash
# Set your domain
export DOMAIN=your-domain.com

# Or create .env file
echo "DOMAIN=your-domain.com" > .env
```

### Performance Monitoring

#### **Test Route Distribution**
```bash
# Test webhook routes (should hit webhook instances)
curl http://localhost:8201/reply_generate

# Test document routes (should have session affinity)
curl http://localhost:8201/setup_files?token=test123

# Test general routes (should hit general instances)
curl http://localhost:8201/health
```

#### **Load Testing**
```bash
# Install Apache Bench
sudo apt-get install apache2-utils

# Test webhook performance
ab -n 1000 -c 10 http://localhost:8201/reply_generate

# Test document processing
ab -n 100 -c 5 http://localhost:8201/process-documents
```

### Troubleshooting

#### **Common Issues**
```bash
# Check if all services are healthy
docker-compose ps

# View Traefik routing
curl http://localhost:8202/api/http/routers

# Check service logs for errors
docker-compose logs traefik
docker-compose logs app-webhooks
```

#### **Session Affinity Testing**
```bash
# Same token should hit same instance
curl -H "Authorization: Bearer token123" http://localhost:8201/process-documents
curl -H "Authorization: Bearer token123" http://localhost:8201/setup_files?token=token123
```

---

**Thank you for using Eko! 🚀**

*Building the future of AI-powered customer service, one conversation at a time.*
